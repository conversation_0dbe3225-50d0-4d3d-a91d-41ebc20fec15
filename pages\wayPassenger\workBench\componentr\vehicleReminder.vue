<template>
	<view class="vehicle">
		<view class="vehicle-title">
			<span>用车提示</span>
			<span @click="carClick(false)"><u-icon name="close"></u-icon></span>
		</view>
		<view class="vehicle-line"></view>
		<view class="text-dh app_t_line">
			<view class="dian"></view>
			用车方式：<span class='w'>{{washData(data.settingPassenger)}}</span>
		</view>
		<view class="text-dh app_t_line">
			<view class="dian"></view>
			可用车型：<span class='w'>{{washData(data.availableCarTypeFullName)}}</span>
		</view>
		<view class="text-dh app_t_line">
			<view class="dian"></view>
			同城限制：<span class='w'>{{washData(data.settingAcrossCity)}}</span>
		</view>
		<view class="text-dh app_t_line">
			<view class="dian"></view>
			用车路线：<span class='w'>{{washData(data.settingAvailableAddress)}}</span>
		</view>
		<view class="text-dh app_t_line">
			<view class="dian"></view>
			每单限额：<span class='w'>{{washData(data.limitMoney)}}</span>
		</view>
		<view class="text-dh app_t_line" v-if="data.limitDayMoney">
			<view class="dian"></view>
			每日限额：<span class='w'>{{washData(data.limitDayMoney)}}</span>
		</view>
		<view class="text-dh app_t_line">
			<view class="dian"></view>
			用车时段：<span class='w'>{{washData(data.useCarTime)}}</span>
		</view>

		<block v-if="!shows">
			<view class="red_tirp">
				<view style="font-weight: 700;margin-bottom: 10rpx;">用车费用支付提示:</view>
				<view>
					网约车提供企业支付和个人支付用车，当不符合企业用车条件，用户可自愿选择个人支付自费用车青在用车下单时注意选择用车车型对应的支付方式条件。
				</view>
			</view>

			<view class="u-flex">
				<view class="custom-style">
					<u-button text="取消用车" @click="carClick(false)"></u-button>
				</view>
				<view class="custom-style">
					<u-button type="primary" text="继续用车" color='#3F5AC7' @click="carClick(data)"></u-button>
				</view>

			</view>
		</block>
	</view>
</template>

<script>
	export default {
		props: {
			data: {
				type: Object,
				default: () => {
					return {}
				}
			},
			shows: {
				type: Boolean,
				default: () => {
					return false
				}
			}
		},
		data() {
			return {

			}
		},
		methods: {
			carClick(data) {
				this.$emit("catShow", data)
			},
			washData(v) {
				return v ? v : '-'
			},
		}
	}
</script>

<style scoped>
	.vehicle {
		padding: 20rpx 40rpx;
	}

	.vehicle-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.vehicle-line {
		height: 1rpx;
		width: 100%;
		background-color: #ccc;
		margin: 10rpx 0;
	}

	.text-dh {
		display: flex;
		align-items: center;
		font-size: 26rpx;
		color: #999999;
		margin: 20rpx 0;
	}

	.text-dh span {
		color: #000;
		margin-left: 40rpx;
	}

	.dian {
		width: 15rpx;
		height: 15rpx;
		border-radius: 50%;
		background-color: #ccc;
		margin: 0 10rpx;
	}

	.red_tirp {
		font-size: 26rpx;
		background: #efefef;
		color: #C45552;
		padding: 10rpx;
		border-radius: 10rpx;
	}

	.custom-style {
		width: 47%;
		margin: 10rpx;
	}

	/deep/.u-safe-bottom {
		padding: 0 !important;
	}

	.w {
		width: 350rpx;
	}
</style>