<template>
	<view class="map_net">
		<!-- <view class="mapLeft" @click="back">
			<u-icon name="arrow-left" bold></u-icon>
		</view>
		<view class="mapRight u-flex" @click="goDetail">
			{{applyData.regulationSname}}
			<u-icon name="arrow-right"></u-icon>
		</view> -->
		<web-view :src="webviewSrc" @message="handleMessage">
			<cover-view class="net cover">
				<cover-view class="net_top">
					<cover-view class="net_t_span"> 申请用车时间:{{applyData.startDate }} - {{applyData.endDate}}
					</cover-view>
				</cover-view>
				<cover-view class="net_conten">
					<cover-view class="c_tabs u-flex">
						<!-- <u-tabs :list="tabsList" :current='tabsIdx' @click='tabsChange'></u-tabs>  -->
						<cover-view class="c_t_item u-flex u-flex-direction" v-for="(item,idx) in tabsList" :key="idx"
							@click="tabsChange(idx)">
							<cover-view>{{item.name}}</cover-view>
							<cover-view class="c_t_i_line" :class="{'active':idx==tabsIdx}"></cover-view>
						</cover-view>
					</cover-view>

					<cover-view v-for="(item,idx) in list" :key="idx">

						<cover-view v-if="tabsIdx==0 && item.id==2"></cover-view>
						<cover-view class="ner_c_item u-flex u-row-between" :class="{'bg':item.id==4}" v-else>
							<cover-view class="ner_c_i_left u-flex  u-flex-1" @click="netCellClick(item)">
								<cover-view class="ner_c_i_icon">
									<cover-image :src="item.icon" v-if="item.icon"></cover-image>
									<cover-view :class="item.class" v-if="item.class"></cover-view>
								</cover-view>
								<cover-view class="ner_c_i_l_text">
									<cover-view v-show="item.value">
										<cover-view v-if="item.id==3" class="u-flexs ">
											<cover-view>您将从</cover-view>
											<cover-view class="green_c ellipsis"
												:class="{'u-flex-1':item.value.length>12}">{{item.value}}</cover-view>
											<cover-view>上车</cover-view>
										</cover-view>
										<cover-view v-else>{{item.value}}</cover-view>
									</cover-view>
									<cover-view v-show="!item.value">{{textWash(item.id)}}</cover-view>
								</cover-view>
							</cover-view>
							<cover-view class="ner_c_i_icon">
								<cover-image src="https://zqcx.di-digo.com/app/image/neRh.png"></cover-image>
							</cover-view>
						</cover-view>
					</cover-view>
					<cover-view class="net_c_btn" @click="determine">
						确定
						<!-- <u-button type="primary" text="确定"></u-button> -->
					</cover-view>
				</cover-view>
			</cover-view>
			</cover-view>
		</web-view>


		<!-- #ifdef H5 -->
		<view class="net">
			<view class="net_top">
				<span class="net_t_span"> 申请用车时间:{{applyData.startDate }} - {{applyData.endDate}} </span>
			</view>
			<view class="net_conten">
				<!-- <u-tabs :list="tabsList" :current='tabsIdx' @click='tabsChange'></u-tabs> -->

				<view class="c_tabs u-flex">
					<view class="c_t_item u-flex u-flex-direction" v-for="(item,idx) in tabsList" :key="idx"
						@click="tabsChange(idx)">
						<view>{{item.name}}</view>
						<view class="c_t_i_line" :class="{'active':idx==tabsIdx}"></view>
					</view>
				</view>

				<template v-for="item in list">
					<view v-if="tabsIdx==0 && item.id==2"></view>
					<netCell :key="item.id" :types="item.id==4" v-else>
						<template #icon>
							<u-icon :name="item.icon" v-if="item.icon"></u-icon>
							<view :class="item.class" v-if="item.class"></view>
						</template>
						<template #name>
							<view v-show="item.value" @click="netCellClick(item)">
								<span v-if="item.id==3">您将从
									<span class="green_c ">{{item.value}}</span>
									上车</span>
								<span v-else>{{item.value}}</span>
							</view>
							<view v-show="!item.value" @click="netCellClick(item)">{{textWash(item.id)}}</view>
						</template>
					</netCell>
				</template>
				<view class="net_c_btn" @click="determine">
					确定
					<!-- <u-button type="primary" text="确定"></u-button> -->
				</view>
			</view>
		</view>
		<!-- #endif -->

	</view>
</template>

<script>
	import qs from 'qs'
	import netCell from '../../components/netCell/netCell.vue'
	import getSeat from '@/components/getSeat/getSeat.vue'
	import {
		applyNetCarDetai,
		gdapi,
		newapplynetcar
	} from '@/config/consoler.js'
	export default {
		components: {
			netCell,
			getSeat
		},
		data() {
			return {
				webviewSrc: '',
				tabsList: [{
					name: '现在'
				}, {
					name: '预约'
				}],
				list: [{
						id: 1,
						icon: 'https://zqcx.di-digo.com/app/image/neMy.png',
						value: '',
					},
					{
						id: 2,
						icon: 'https://zqcx.di-digo.com/app/image/neTime.png',
						value: ''
					},
					{
						id: 3,
						class: 'circle green',
						value: ''
					},
					{
						id: 4,
						class: 'circle yeelo',
						value: '',
					},
				],
				tabsIdx: 0,
				data: {},
				userInfo: {},
				applyData: {},
				addressData: {}
			}
		},
		onLoad(e) {
			this.userInfo = this.$common.getItem('userInfo')
			this.userInfo.applyId = e.id
			this.getInfo()
			this.getLocation()

			uni.$on('netWockCar', (res) => {
				console.log(res, 'res');
			})
		},
		created() {
			// #ifdef H5
			//监听并接收消息
			window.addEventListener('message', this.h5handleMessage);
			// #endif
		},

		beforeDestroy() {
			// #ifdef H5
			window.removeEventListener('message', this.h5handleMessage);
			// #endif
		},

		methods: {
			h5handleMessage(evt) {
				let that = this
				try {
					if (evt.data.data && evt.data.data.arg) {
						let {
							folat,
							folong,
							foNames,
							adcode
						} = evt.data.data.arg
						that.list[2].value = foNames
						that.list[2].address = foNames
						that.list[2].longitude = folong
						that.list[2].latitude = folat
						that.list[2].cityCode = adcode

						that.addressData.folong = folong
						that.addressData.folat = folat
						that.addressData.foName = encodeURIComponent(foNames)

						// that.renewUrl()
						// console.log(evt.data.data.arg, 'evt');
					}
				} catch (e) {
					console.log(e);
				}
				// console.log('接收到的消息3：' + JSON.stringify(evt.data.data));
			},
			handleMessage(evt) {
				let that = this
				try {
					if (evt.detail.data && evt.detail.data[0]) {
						let {
							folat,
							folong,
							foNames,
							adcode
						} = evt.detail.data[0]
						that.list[2].value = foNames
						that.list[2].address = foNames
						that.list[2].longitude = folong
						that.list[2].latitude = folat
						that.list[2].cityCode = adcode

						that.addressData.folong = folong
						that.addressData.folat = folat
						that.addressData.foName = encodeURIComponent(foNames)
					}
				} catch (e) {
					console.log(e);
				}
			},
			determine() {
				if (!this.list[0].value) return uni.$u.toast('请选择乘车人')
				if (!this.list[1].value && this.tabsIdx == 1) return uni.$u.toast('请选择时间')
				if (!this.list[2].value) return uni.$u.toast('请选择出发地')
				if (!this.list[3].value) return uni.$u.toast('请选择目的地')

				let obj = {
					"isNow": this.tabsIdx == 0 ? true : false,
					"netApplyId": this.userInfo.applyId,
					"regulationId": this.applyData.regulationId,
					"userId": this.list[0].userId ? this.list[0].userId : this.list[0].id,
					"name": this.list[0].name,
					"mobile": this.list[0].mobile,
					"reserveStartTime": this.list[1].valueName,
					"fromAddrDetail": this.list[2].address ? this.list[2].address : this.list[2].value,
					"fromAddrName": this.list[2].value,
					"fromAreaCode": this.list[2].cityCode,
					"fromLat": this.list[2].latitude,
					"fromLng": this.list[2].longitude,
					"toAddrDetail": this.list[3].address,
					"toAddrName": this.list[3].value,
					"toAreaCode": this.list[3].cityCode,
					"toLat": this.list[3].latitude,
					"toLng": this.list[3].longitude,
				}

				newapplynetcar(obj).then(res => {
					uni.$u.toast(res)
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/wayPassenger/trip/trip'
						});
					}, 1000)
				})
			},
			renewUrl() {
				let that = this

				setTimeout(() => {
					let http = 'https://zqcx.di-digo.com/app-h5/'
					// let http = 'https://zqcxdev.di-digo.com/app-h5/'
					let url = '/hybrid/html/networkCar.html'
					let {
						folong,
						folat,
						foName,
						toName,
						tolong,
						tolat,
					} = that.addressData

					let text =
						`?folong=${folong}&folat=${folat}&foName=${foName}&tolong=${tolong}&tolat=${tolat}&toName=${toName}&sysId=${this.applyData.regulationId}&sys=${encodeURIComponent(this.applyData.regulationSname)}`

					// #ifdef H5
					that.addressData.type = "H5"
					that.addressData.accessToken = JSON.parse(uni.getStorageSync("userInfo")).accessToken
					// that.webviewSrc = url + '?' + qs.stringify(that.addressData)
					that.webviewSrc = url + text
					// #endif
					// #ifdef MP-WEIXIN
					that.addressData.type = "WEIXIN"
					that.addressData.accessToken = JSON.parse(uni.getStorageSync("userInfo")).accessToken
					// that.webviewSrc = `${http}${url}?${qs.stringify(that.addressData)}`
					that.webviewSrc = `${http}${url}${text}`
					// #endif
				}, 600)
			},

			getLocation() {
				let that = this
				uni.getLocation({
					type: 'gcj02',
					success: function(res) {
						gdapi(res).then(ress => {
							that.list[2].value = ress.aois[0].name
							that.list[2].address = ress.formatted_address
							that.list[2].longitude = res.longitude
							that.list[2].latitude = res.latitude
							that.list[2].cityCode = ress.addressComponent.adcode

							that.addressData.folong = res.longitude
							that.addressData.folat = res.latitude
							that.addressData.foName = encodeURIComponent(ress.aois[0].name)

							that.renewUrl()
						}).catch(err => {
							uni.$u.toast('获取位置失败请手动选择')
						})
					},
					fail: () => {
						uni.showModal({
							content: '定位获取失败,请重新获取',
							success: (res) => {
								that.getLocation()
							}
						})
					}
				})
			},
			goDetail() {
				uni.navigateTo({
					url: '/pages/wayPassenger/index/systemDetail/systemDetail?id=' + this.applyData
						.regulationId
				})
			},
			setData(item, t) {
				if (t == 1) {
					this.list[0].value = item.name + ' ' + this.maskPhone(item.mobile)
					this.list[0].name = item.name
					this.list[0].mobile = item.mobile
					this.list[0].userId = item.userId
				}
				if (t == 2) {
					this.list[1].value = item.value
					this.list[1].valueName = item.valueName
				}
				if (t == 3 || t == 4) {
					this.list.splice(t - 1, 1, Object.assign({}, this.list[t - 1], item))

				}
				if (t == 3) {
					this.addressData.folong = item.longitude
					this.addressData.folat = item.latitude
					this.addressData.foName = encodeURIComponent(item.value)
					this.renewUrl()
				}
				if (t == 4) {
					this.addressData.tolong = item.longitude
					this.addressData.tolat = item.latitude
					this.addressData.toName = encodeURIComponent(item.value)
					this.renewUrl()
				}
			},
			tabsChange(e) {
				if (e == this.tabsIdx) return
				this.tabsIdx = e
				this.list[1].value = ''
				this.list[1].valueName = ''
			},
			netCellClick(v) {
				this.userInfo.indexs = v.id
				if (v.id == 1) {
					uni.navigateTo({
						url: '/pager/frequently/frequently?id=' + this.userInfo.userId
					})
				}
				if (v.id == 2) {
					uni.navigateTo({
						url: '/pagec/middleware/middleware?type=2&date=' + this.applyData.date
					})
				}
				if (v.id == 3 || v.id == 4) {
					uni.navigateTo({
						url: '/pagec/middleware/middleware?type=' + v.id
					})
				}
			},
			getInfo() {
				this.list[0].value = this.userInfo.name + this.maskPhone(this.userInfo.mobile)
				this.list[0].name = this.userInfo.name
				this.list[0].mobile = this.userInfo.mobile
				this.list[0].userId = this.userInfo.userId
				applyNetCarDetai({
					id: this.userInfo.applyId
				}).then(res => {
					let startDate = new Date(res.startDate).getTime()
					let endDate = new Date(res.endDate).getTime()
					res.date = uni.$u.timeFormat(startDate, 'yyyy年mm月dd日');
					res.startDate = uni.$u.timeFormat(startDate, 'yyyy/mm/dd hh:MM');
					res.endDate = uni.$u.timeFormat(endDate, 'yyyy/mm/dd hh:MM');
					this.applyData = res
				})
			},
			maskPhone(phoneNumber) {
				return phoneNumber.substr(0, 3) + '****' + phoneNumber.substr(7);
			},
			textWash(v) {
				let text = ['', '请选择乘车人', '请选择预约时间', '请选择出发地', '请选择目的地']
				return text[v]
			},
			back() {
				uni.navigateBack({
					delta: 2
				});
			},


		}
	}
</script>

<style scoped>
	.map_net {
		position: relative;
		height: 100vh;
		background: #fafafa;
	}

	/*  */
	.cover {
		position: fixed !important;
		z-index: 10070 !important;
	}

	.net {
		z-index: 999;
		position: absolute;
		bottom: 30rpx;
		width: calc(100% - 40rpx);
		margin: 0 20rpx;
	}

	/*  */
	.net_conten {
		background: #fff;
		width: 100%;
		border-radius: 6rpx;
		padding-bottom: 10rpx;
		box-shadow: 0px 0px 3px 0px #ccc;
	}

	.net_c_btn {
		color: #fff;
		background-color: #3c9cff;
		border-color: #3c9cff;
		border-width: 1px;
		border-style: solid;
		margin: 20rpx;
		text-align: center;
		border-radius: 10rpx;
		width: calc(100% - 80rpx);
		padding: 20rpx;
	}

	/*  */
	.net_top {
		border: 1px solid;
		margin-bottom: 5px;

		border-radius: 6rpx;
		background: #00000036;
	}

	.net_t_span {
		font-size: 26rpx;
		margin: 0 20rpx;
		color: #fff;
		height: 70rpx;
		line-height: 68rpx;
	}

	/*  */
	.mapLeft,
	.mapRight {
		position: absolute;
		background: #fff;
		z-index: 999;
		top: 40rpx;
		padding: 10rpx;
		box-shadow: 0px 0px 3px #ccc;
	}

	.mapLeft {
		left: 30rpx;
		border-radius: 50%;
	}

	.mapRight {
		right: 30rpx;
		border-radius: 20rpx;
		font-size: 21rpx;
		padding: 10rpx 20rpx;
	}

	/*  */
	.green_c {
		color: #07c160;
		margin: 0 10rpx;
	}

	.green {
		background: #07c160;
	}

	.yeelo {
		background: #e99d42;
	}

	.circle {
		width: 20rpx;
		height: 20rpx;
		margin: 10rpx;
		border-radius: 50%;
	}

	/*  */
	.c_tabs {
		padding: 20rpx;
		padding-bottom: 0rpx;
	}

	.c_t_item {
		color: '#ccc' !important;
		padding: 0 30rpx;
		padding-bottom: 10rpx;
	}

	.c_t_i_line {
		display: none;
		visibility: hidden;
		background: #3c9cff;
		width: 30rpx;
		padding: 2rpx;
		margin-top: 10rpx;
	}

	.active {
		display: block;
		visibility: inherit;
	}


	/*  */
	.ner_c_item {
		padding: 24rpx;
		font-size: 28rpx;
	}

	.ner_c_i_icon {
		width: 44rpx;
		margin: 0 10rpx;
	}

	.ner_c_i_l_text {
		margin-left: 10rpx;
		flex: 1;
	}

	.bg {
		background: #f4f4f4;
		margin: 0 24rpx;
		padding: 24rpx 0px !important;
		border-radius: 20rpx;
	}

	.btn {
		color: #fff;
		background-color: #3c9cff;
		border-color: #3c9cff;
		border-width: 1px;
		border-style: solid;
	}

	.ellipsis {
		text-align: left;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
</style>