<template>
	<view>
		<u-popup :show="show" mode="center" :customStyle="{
					'width': ' 80%',
					'border-radius': '10px',
					'padding': '10px',
					'z-index':'990'
				}">
			<view class="topTitle">
				<text class="topText">{{title}}</text>
				<i class="iconfont icon-close" @tap="$emit('close')"></i>
			</view>
			<view>
				<slot></slot>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		props: {
			show: {
				type: Boolean,
				default: () => {
					return false
				}
			},
			title: {
				type: String,
				default: () => {
					return '标题'
				}
			}
		},
		data() {
			return {

			}
		},
		methods: {

		}
	}
</script>

<style scoped>
	.topTitle {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 2rpx solid #dfdfdf;
		padding-bottom: 20rpx;
		margin-bottom: 20rpx;
	}

	.topText {
		font-weight: 700;
		color: #303030;
	}

	/deep/.u-transition {
		z-index: 990 !important;
	}
</style>
