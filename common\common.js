export default {
	provinceList: [
		[{
				label: '北京',
				children: ['北京市']
			},
			{
				label: '天津',
				children: ['天津市']
			},
			{
				label: '河北',
				children: ['石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '保定市', '张家口市', '承德市', '沧州市', '廊坊市', '衡水市']
			},
			{
				label: '山西',
				children: ['太原市', '大同市', '阳泉市', '长治市', '晋城市', '朔州市', '晋中市', '运城市', '忻州市', '临汾市', '吕梁市']
			},
			{
				label: '内蒙古',
				children: ['呼和浩特市', '包头市', '乌海市', '赤峰市', '通辽市', '鄂尔多斯市', '呼伦贝尔市', '巴彦淖尔市', '乌兰察布市', '兴安盟', '锡林郭勒盟',
					'阿拉善盟'
				]
			},
			{
				label: '辽宁',
				children: ['沈阳市', '大连市', '鞍山市', '抚顺市', '本溪市', '丹东市', '锦州市', '营口市', '阜新市', '辽阳市', '盘锦市', '盘锦市',
					'朝阳市', '葫芦岛市'
				]
			},
			{
				label: '吉林',
				children: ['长春市', '吉林市', '四平市', '辽源市', '通化市', '白山市', '松原市', '白城市', '延边']
			},
			{
				label: '黑龙江',
				children: ['哈尔滨市', '齐齐哈尔市', '鸡西市', '鹤岗市', '双鸭山市', '大庆市', '伊春市', '佳木斯市', '七台河市', '牡丹江市', '黑河市',
					'绥化市', '大兴安岭地区',
				]
			},
			{
				label: '上海',
				children: ['上海市']
			},
			{
				label: '江苏',
				children: ['南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市', '盐城市', '扬州市', '镇江市', '泰州市',
					'宿迁市'
				]
			},
			{
				label: '浙江',
				children: ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市']
			},
			{
				label: '安徽',
				children: ['合肥市', '芜湖市', '蚌埠市', '淮南市', '马鞍山市', '淮北市', '铜陵市', '安庆市', '黄山市', '滁州市', '阜阳市', '宿州市',
					'六安市', '亳州市', '池州市', '宣城市'
				]
			},
			{
				label: '福建',
				children: ['福州市', '厦门市', '莆田市', '三明市', '泉州市', '漳州市', '南平市', '龙岩市', '宁德市']
			},
			{
				label: '江西',
				children: ['南昌市', '景德镇市', '萍乡市', '九江市', '新余市', '鹰潭市', '赣州市', '吉安市', '宜春市', '抚州市', '上饶市']
			},
			{
				label: '山东',
				children: ['济南市', '青岛市', '淄博市', '枣庄市', '东营市', '烟台市', '潍坊市', '济宁市', '泰安市', '威海市', '日照市', '莱芜市',
					'临沂市', '德州市', '聊城市', '滨州市', '菏泽市'
				]
			},
			{
				label: '河南',
				children: ['郑州市', '开封市', '洛阳市', '平顶山市', '安阳市', '鹤壁市', '新乡市', '焦作市', '济源市', '濮阳市', '许昌市', '漯河市',
					'三门峡市', '南阳市', '商丘市', '信阳市', '周口市', '驻马店市'
				]
			},
			{
				label: '湖北',
				children: ['武汉市', '黄石市', '十堰市', '宜昌市', '襄阳市', '鄂州市', '荆门市', '孝感市', '荆州市', '黄冈市', '咸宁市', '随州市', '恩施',
					'仙桃市', '潜江市', '天门市', '神农架林区'
				]
			},
			{
				label: '湖南',
				children: ['长沙市', '株洲市', '湘潭市', '衡阳市', '邵阳市', '岳阳市', '常德市', '张家界市', '益阳市', '郴州市', '永州市', '怀化市',
					'娄底市', '湘西'
				]
			},
			{
				label: '广东',
				children: ['广州市', '韶关市', '深圳市', '珠海市', '汕头市', '佛山市', '江门市', '湛江市', '茂名市', '肇庆市', '惠州市', '梅州市',
					'汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '东沙群岛', '潮州市', '揭阳市', '云浮市'
				]
			},
			{
				label: '广西',
				children: ['南宁市', '柳州市', '桂林市', '梧州市', '北海市', '防城港市', '钦州市', '贵港市', '玉林市', '百色市', '贺州市', '河池市',
					'来宾市', '崇左市'
				]
			},
			{
				label: '海南',
				children: ['海口市', '三亚市', '三沙市', '五指山市', '琼海市', '儋州市', '文昌市', '万宁市', '东方市', '定安县', '屯昌县', '澄迈县',
					'临高县', '白沙', '昌江', '乐东', '陵水', '保亭', '琼中'
				]
			},
			{
				label: '重庆',
				children: ['重庆市']
			},
			{
				label: '四川',
				children: ['成都市', '自贡市', '攀枝花市', '泸州市', '德阳市', '绵阳市', '广元市', '遂宁市', '内江市', '乐山市', '南充市', '眉山市',
					'宜宾市', '广安市', '达州市', '雅安市', '巴中市', '资阳市', '阿坝', '甘孜', '凉山'
				]
			},
			{
				label: '贵州',
				children: ['贵阳市', '六盘水市', '遵义市', '安顺市', '铜仁市', '黔西南', '毕节市', '黔东南', '黔南']
			},
			{
				label: '云南',
				children: ['昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市', '楚雄', '红河', '文山', '西双版纳', '大理',
					'德宏', '怒江', '迪庆'
				]
			},
			{
				label: '西藏',
				children: ['拉萨市', '昌都地区', '山南地区', '日喀则地区', '那曲地区', '阿里地区', '林芝地区']
			},
			{
				label: '陕西',
				children: ['西安市', '铜川市', '宝鸡市', '咸阳市', '渭南市', '延安市', '汉中市', '榆林市', '安康市', '商洛市']
			},
			{
				label: '甘肃',
				children: ['兰州市', '嘉峪关市', '金昌市', '白银市', '天水市', '武威市', '张掖市', '平凉市', '酒泉市', '庆阳市', '定西市', '陇南市',
					'临夏', '甘南'
				]
			},
			{
				label: '青海',
				children: ['西宁市', '海东市', '海北', '黄南', '海南', '果洛', '玉树', '海西']
			},
			{
				label: '宁夏',
				children: ['银川市', '石嘴山市', '吴忠市', '固原市', '中卫市']
			},
			{
				label: '新疆',
				children: ['乌鲁木齐市', '克拉玛依市', '吐鲁番地区', '哈密地区', '昌吉', '博尔塔拉', '巴音郭楞', '阿克苏地区', '克孜勒苏柯尔克孜自治州', '喀什地区',
					'和田地区', '伊犁', '塔城地区', '阿勒泰地区', '石河子市', '阿拉尔市', '图木舒克市', '五家渠市'
				]
			},
			{
				label: '台湾',
				children: ['台北市', '高雄市', '台南市', '台中市', '金门县', '南投县', '基隆市', '新竹市', '嘉义市', '新北市', '宜兰县', '新竹县',
					'桃园县', '苗栗县', '彰化县', '嘉义县', '云林县', '屏东县', '台东县', '花莲县', '澎湖县', '连江县'
				]
			},
			{
				label: '香港',
				children: ['香港岛', '香港岛', '新界']
			},
			{
				label: '澳门',
				children: ['澳门', '离岛']
			}
		]
	],
	//弹出提示窗方法 参数说明arg1:title标题提示 arg2:提示的详情的内容 cb1:成功的回调  cb2:失败的回调
	prompt(arg1, arg2, cb1, cb2) {

		uni.showModal({
			title: arg1,
			content: arg2,
			success: function(res) {
				if (res.confirm) {
					cb1()
					// console.log('用户点击确定');
				} else if (res.cancel) {
					cb2()
					// console.log('用户点击取消');
				}
			}
		});
	},

	// 开启等待
	loading(obj) {
		uni.showLoading({
			title: obj.title
		});
	},

	// 关闭等待
	hideLoading() {
		uni.hideLoading();
	},

	getItem(key) { //取
		if(uni.getStorageSync(key)){
			return JSON.parse(uni.getStorageSync(key))
		}
	},
	setItem(key, value) { //存
		uni.setStorageSync(key, JSON.stringify(value));
	},
	removeItem(key) { //删
		uni.removeStorageSync(key);
	},
}
