<script>
	import {
		mapMutations
	} from 'vuex';
	export default {
		onLaunch: function() {
			// uni.hideTabBar()

			let that = this
			uni.addInterceptor('navigateBack', { //监听返回
				success(e) {
					that.watchRouter()
				}
			})

			uni.getStorage({
				key: 'uerInfo',
				success: (res) => {
					console.log(res, 'res');
					that.login(res.data);
				}
			});
		},
		onShow: function() {
			const updateManager = uni.getUpdateManager();
			updateManager.onCheckForUpdate(function(res) {
				// 请求完新版本信息的回调
				console.log(res.hasUpdate);
			});
			updateManager.onUpdateReady(function(res) {
				uni.showModal({
					title: '更新提示',
					content: '新版本已经准备好，是否重启应用？',
					showCancel: false,
					success(res) {
						if (res.confirm) {
							// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
							updateManager.applyUpdate();
						}
					}
				});
			});

			updateManager.onUpdateFailed(function(res) {
				// 新的版本下载失败
				console.log('download error')
				uni.showModal({
					title: '提示',
					content: '新版小程序下载失败\n请自行退出程序，手动卸载本程序，再运行',
					confirmText: "知道了"
				});
			});
		},
		onHide: function() {
			// console.log('App Hide')
		},
		methods: {
			watchRouter() {
				// #ifdef H5
				const pages = getCurrentPages()
				if (pages.length === 1) {
					history.back()
				}
				// #endif
				// let page = pages[pages.length - 1].$page.fullPath; //完整路由地址
				// let typer = page.split('type=')[1] //携带的type参数
				// if (typer) {
				// 	uni.reLaunch({
				// 		url: '/pages/wayPassenger/index/index'
				// 	})
				// }

			},
			...mapMutations(['login']),

		}
	}
</script>

<style lang="scss">
	@import "uview-ui/index.scss";
	@import "common/common.scss";
	@import "@/static/css/icon.css";


	// @font-face {
	// 	font-family: pingfang;
	// 	/* #ifdef H5 */
	// 	src: url('/iconr/pingfang.ttf')
	// 	/* #endif */
	// 	/* #ifdef MP-WEIXIN */
	// 	src: url('https://zqcxdev.di-digo.com/app/image/pingfang.ttf')
	// 	/* #endif */
	// }


	page {
		// font-family: Heavyr;
		background-color: #F9FAFE;
		// filter: grayscale(1)
	}

	.footer_box {
		position: fixed;
		bottom: 0;
		width: calc(100% - 64rpx);
		padding: 20rpx 32rpx;
		background-color: #fff;
		z-index: 999;
	}

	wx-swiper .wx-swiper-dots {
		bottom: 0;
		left: 50%;
		background-color: #D1DEFF;
	}

	wx-swiper .wx-swiper-dot {
		width: 10rpx;
		height: 10rpx;
		border-radius: 100%;
		background: #ffffff00 !important;
		transition: all .5s;
	}

	wx-swiper .wx-swiper-dot-active {
		width: 40rpx;
		border-radius: 5rpx;
		background: #346CF2 !important;
	}
</style>