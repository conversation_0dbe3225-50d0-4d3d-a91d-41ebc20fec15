<template>
	<view class="trip">
		<u-navbar title="行程" :placeholder="true">
			<view slot="left"></view>
		</u-navbar>

		<view class="trip_box" v-for="(item,index) in dataList" :key="index">
			<view class="trip_b_time">
				{{getMmdd(item.reserveStartTime) }}
				<!-- {{getWeekDay(item.reserveStartTime)}} -->
			</view>
			<view class="trip_box_s">
				<view v-if="item.travelType==3">
					<tripNetCar :item="item" @tripClick="tripClick" @list='queryList'></tripNetCar>
				</view>
				<view v-else>
					<view class="trip_s_tit u-flex u-row-between">

						<view class="trip_icon">
							<u-icon size="40" name="https://zqcx.di-digo.com/app/image/qi.png"
								v-if="item.travelType==1 && item.orderType!=2">
							</u-icon>
							<u-icon size="40" name="https://zqcx.di-digo.com/app/image/zu.png"
								v-if="item.travelType==2">
							</u-icon>
							<u-icon size="40" name="https://zqcx.di-digo.com/app/image/wang.png"
								v-if="item.travelType==3">
							</u-icon>
							<u-icon size="40" name="https://zqcx.di-digo.com/app/image/si.png"
								v-if="item.travelType==1 && item.orderType==2">
							</u-icon>
						</view>
						<!-- <view class="u-flex">
							<u-tag  text="1人"  plain size="mini"></u-tag>
						</view> -->
						<view class="u-flex">
							<span class="u-text-bold trip_carNumber">{{item.carNumber ?item.carNumber:''}}</span>
						</view>
						<view class="u-flex">
							<!-- <u-tag v-if="item.travelType==2" text="租" type="warning" plain size="mini"></u-tag> -->
							<view class="trip_s_tag">
								<u-tag :text="item.compOrderStateName" plain size="mini"> </u-tag>
							</view>

							<view class="trip_s_uicon">
								<view @click="moreBtn($event,item)">
									<u-icon size="20" name="https://zqcx.di-digo.com/app/image/diandian.png">
									</u-icon>
								</view>
								<view class="trip_s_airClass" v-if="item.airShow">
									<view class="footbtn footbtnCenter" @click="telBtn(item.driverMobile)">
										<u-icon name="phone-fill"></u-icon>联系司机
									</view>
									<view class="footbtn border" @click="telBtn(item.psgMobile)">
										<u-icon name="server-fill"></u-icon>联系调度
									</view>
									<view class="footbtn" @click="goTrip(item)" style="border-bottom: 1px solid #ccc;">
										<u-icon name="list"></u-icon>变更行程
									</view>
									<view class="footbtn hui" @click="cancelBtn(item)">
										<u-icon name="close"></u-icon>取消订单
									</view>
								</view>
							</view>
						</view>
					</view>

					<view class="trip_s_content u-flex u-row-between" @click="gonav(item)">
						<view class="trip_s_c_txt u-text-center">
							<view class="trip_s_c_t">
								{{item.fromAddrName}}
							</view>
							<view class="trip_s_c_c">
								{{getHhmm(item.reserveStartTime)}}
							</view>
							<view class="trip_s_c_b">
								{{getYmd(item.reserveStartTime) }}
							</view>
						</view>
						<view class="flex_line_box">
							<view class="flex_line"></view>
							<view class="flex_bg">
								{{second(Number(item.reserveEndTimes) - Number(item.reserveStartTimes))}}
							</view>
						</view>
						<view class="trip_s_c_txt u-text-center">
							<view class="trip_s_c_t">
								{{item.toAddrName}}
							</view>
							<view class="trip_s_c_c">
								{{getHhmm(item.reserveEndTime) }}
							</view>
							<view class="trip_s_c_b">
								{{getYmd(item.reserveEndTime)}}
							</view>
						</view>
					</view>

				</view>

				<!-- <view class="trip_s_footer u-flex u-row-around">
					<view class="footbtn hui" @click="cancelBtn(item)">
						取消订单
					</view>
					<view class="footbtn footbtnCenter" @click="telBtn(item.driverMobile)">
						联系司机
					</view>
					<view class="footbtn" @click="telBtn(item.psgMobile)">
						联系调度
					</view>
				</view> -->

				<!-- <view class="trip_s_footer u-flex u-row-around">
					<view class="footbtnT huiR">
						取消订单
					</view>
					<view class="footbtnT focus">
						去用车
					</view>
				</view> -->

			</view>
		</view>

		<u-loadmore v-if="dataList.length>0" :status="status" />

		<u-empty v-if="dataList.length==0" text="行程为空" icon="http://cdn.uviewui.com/uview/empty/order.png">
		</u-empty>

		<!-- <tabBart :current='1'></tabBart> -->
	</view>
</template>

<script>
	import {
		mytravellist,
		mytravelinfo,
		mytravelCancel
	} from '@/config/api.js';
	import {
		messagecount
	} from '@/config/consoler.js';
	import tripNetCar from '../workBench/componentr/tripNetCar.vue'
	export default {
		components: {
			tripNetCar
		},
		data() {
			return {
				dataList: [],
				status: 'loadmore',
				pageNum: 1,
				pageSize: 20,
				hasNext: true,
				compOrderStateList: [],
				airShow: false,
				popData: [{
					title: '联系司机'
				}, {
					title: '联系调度'
				}, {
					title: '取消行程'
				}, ]
			}
		},
		onLoad() {
			// uni.hideTabBar()
			this.getMsg()
		},
		onShow() {
			this.queryList()
		},
		onReachBottom() {
			if (this.hasNext) {
				this.status = 'loading';
				this.pageNum = ++this.pageNum;
				setTimeout(() => {
					this.queryList()
				}, 1000)
			}
		},
		methods: {
			tripClick(val) {
				if (val.types) {
					uni.$u.route('/pager/networkCar/networkCar', {
						id: val.netTravelId,
					})
				} else {
					this.cancelBtn(val)
				}
				return
				let text = val.types ? '去用车' : '是否确认取消订单'
				uni.showModal({
					content: text,
					success: (e) => {}
				})
			},
			getMsg() {
				messagecount({}).then(res => {
					let gentle = res.countPush + res.countSystem
					let sum = gentle > 99 ? '99+' : `${gentle}`
					uni.setTabBarBadge({
						index: 3,
						text: sum ? sum : '0'
					})
				})
			},
			moreBtn(e, item) {
				this.$set(item, 'airShow', true)
				this.setFalse(item)

				setTimeout(() => {
					this.$set(item, 'airShow', false)
				}, 3000)
			},
			setFalse(item) {
				this.dataList.forEach(v => {
					if (item.travelId == v.travelId) {} else {
						this.$set(v, 'airShow', false)
					}
				})
			},
			goTrip(item) {
				let urlr = `/pager/editTrip/editTrip?id=${item.travelId}&orderType=${item.orderType}`
				uni.navigateTo({
					url: urlr
				})
			},
			telBtn(tel) {
				if (!tel) return uni.$u.toast('暂无联系方式')
				uni.makePhoneCall({
					phoneNumber: tel, //仅为示例，并非真实的电话号码
					success: function() {
						console.log("拨打电话成功！")
					},
					fail: function() {
						console.log("拨打电话失败！")
					}
				})
			},
			cancelBtn(item) {
				console.log(item, '');
				let that = this
				uni.showModal({
					content: '是否取消当前订单',
					success: (res) => {
						if (res.confirm) {
							mytravelCancel({
								id: item.applyId,
								travelType: item.travelType
							}).then(res => {
								uni.$u.toast("操作成功")
								setTimeout(() => {
									that.queryList()
								}, 1000)
							})
						}
					}
				})

			},
			gonav(item) {
				uni.$u.route('/pages/map/mapnotit', {
					url: "/hybrid/html/navigation.html",
					orderType: item.orderType,
					id: item.travelId,
				});
			},
			queryList() {
				// 
				let arrs = this.$common.getItem('dicVoList')
				arrs.forEach(item => {
					if (item.dicCode == "comp_order_state") {
						this.compOrderStateList = item.dicValueList
					}
				})
				mytravellist({
					params: {
						pageNum: this.pageNum,
						pageSize: this.pageSize,
						// userId: this.$common.getItem('userInfo').userId
					}
				}).then((data) => {
					data.forEach(v => {
						v.compOrderStateName = this.washData(v.compOrderState)
						let dater = (new Date(v.reserveStartTime)).getTime();
						v.reserveStartTimer = dater
						let datere = (new Date(v.reserveEndTime)).getTime();
						v.reserveEndTimer = datere
						v.airShow = false
					})
					// data[0].travelType = 3
					this.dataList = data
					this.hasNext = data.hasNext
					if (data.hasNext) {
						this.status = 'loadmore'
					} else {
						this.status = 'nomore'
					}
				})
			},
			washData(id) {
				let text = '-'
				this.compOrderStateList.forEach(v => {
					if (v.dicValue == id) {
						text = v.dicDescribe
					}
				})
				return text
			},
			getHhmm(date) {
				let artr = date.split(' ')[1].split(':')
				return `${artr[0]}:${artr[1]}`
			},
			getMmdd(date) {
				let artr = date.split(' ')[0].split('-')
				return `${artr[1]}-${artr[2]}`
			},
			getYmd(date) {
				let artr = date.split(' ')[0].split('-')
				return `${artr[0]}-${artr[1]}-${artr[2]}`
			},
			getWeekDay(date) {
				let dater = new Date(date)
				let week;
				if (dater.getDay() == 0) week = "周日"
				if (dater.getDay() == 1) week = "周一"
				if (dater.getDay() == 2) week = "周二"
				if (dater.getDay() == 3) week = "周三"
				if (dater.getDay() == 4) week = "周四"
				if (dater.getDay() == 5) week = "周五"
				if (dater.getDay() == 6) week = "周六"
				return week;
			},
			second(value) {
				// console.log(value, 'value1');
				let theTime = parseInt(value / 1000); // 秒
				let middle = 0; // 分
				let hour = 0; // 小时

				if (theTime >= 60) {
					middle = parseInt(theTime / 60);
					theTime = parseInt(theTime % 60);
					if (middle >= 60) {
						hour = parseInt(middle / 60);
						middle = parseInt(middle % 60);
					}
				}
				let result = "";
				if (theTime > 0) {
					result = "" + parseInt(theTime) + "秒"
				}
				if (middle > 0) {
					result = "" + parseInt(middle) + "分" + result;
				}
				if (hour > 0) {
					result = "" + parseInt(hour) + "小时" + result;
				}
				return result ? result : 0 + "秒";
			}
		}
	}
</script>

<style lang="scss" scoped>
	.trip {
		padding-bottom: 80rpx;

		.trip_box {
			margin: 0 32rpx;

			.trip_b_time {
				margin: 20rpx 13rpx;
				font-size: 28rpx;
			}

			.trip_b_time.before {
				color: #999;
			}

			.trip_box_s {
				background-color: #fff;
				border-radius: 8rpx;
				box-shadow: 1px 1px 5px 0px #bcbcbc2b;

				.trip_s_tit {
					border-bottom: 1px solid #D6DAE9;
					padding: 18rpx 0 14rpx 0;
					// margin-left: 13rpx;
					margin-right: 13rpx;
					position: relative;

					.trip_icon {
						position: absolute;
						top: 0;
						left: 0;
					}


					.trip_carNumber {
						margin-left: 80rpx;
					}

					.trip_s_tag {
						margin: 0 28rpx;
					}

					.trip_s_uicon {
						position: relative;

						.trip_s_airClass {
							position: absolute;
							background-color: #fff;
							width: 240rpx;
							right: 20rpx;
							top: 60rpx;
							box-shadow: 0 0 3px 0px #6666665e;
							// 1px 1px 5px 0px #bcbcbc5e
							z-index: 999;
							border-radius: 20rpx;
							transition: background 0.3s ease-in-out;

							.footbtn {
								height: 80rpx;
								line-height: 80rpx;
								font-size: 29rpx;
								display: flex;
								justify-content: space-evenly;
								align-items: center;
							}

							.border {
								border-top: 1px solid #ccc;
								border-bottom: 1px solid #ccc;
							}


						}
					}


				}

				.trip_s_content {
					margin-top: 24rpx;
					padding-bottom: 20rpx;
					position: relative;

					.trip_s_c_txt {
						margin: 0 48rpx;

						.trip_s_c_t {
							font-size: 24rpx;
							color: #404040;
							max-width: 160rpx;
						}

						.trip_s_c_c {
							font-size: 36rpx;
							color: #404040;
							margin-top: 10rpx;
						}

						.trip_s_c_b {
							font-size: 24rpx;
							color: #999999;
							margin-top: 12rpx;
						}
					}

					.flex_line_box {
						position: absolute;
						left: 50%;
						margin-left: -106rpx;

						.flex_line {
							width: 212rpx;
							height: 4rpx;
							border-radius: 2rpx;
							background-color: #F1F4FD;
						}

						.flex_bg {
							width: 180rpx;
							height: 46rpx;
							border-radius: 24rpx;
							background-color: #F1F4FD;
							text-align: center;
							font-size: 24rpx;
							color: #404040;
							line-height: 46rpx;
							position: absolute;
							left: 50%;
							margin-left: -90rpx;
							top: -22rpx;
						}
					}
				}

				.trip_s_footer {
					border-top: 1px solid #F9FAFE;

					// .footbtn {
					// 	text-align: center;
					// 	width: 33.3%;
					// 	height: 80rpx;
					// 	line-height: 80rpx;
					// 	font-size: 28rpx;
					// }

					.footbtnCenter {
						border-left: 1px solid #F9FAFE;
						border-right: 1px solid #F9FAFE;
					}

					.hui {
						color: #999999;
					}

					.huiR {
						color: #999999;
						border-right: 1px solid #F9FAFE;
					}

					.footbtnT {
						text-align: center;
						width: 50%;
						height: 80rpx;
						line-height: 80rpx;
						font-size: 32rpx;
					}

					.focus {
						color: #346CF2;
					}
				}
			}
		}
	}
</style>