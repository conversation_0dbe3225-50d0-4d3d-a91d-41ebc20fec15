<template>
	<view @click='goClick(item,false)'>
		<view class="u-flex u-row-between">
			<view class="u-flex">
				<span class="regulation" v-show="item.netApplyType">{{dataWash(item.netApplyType==1?'日常':'差旅')}}</span>
				<span style="font-weight: 700;">{{dataWash(item.regulationSname)}}</span>
			</view>
			<view class="u-center">
				<span class="text-rt">{{dataWash(item.compOrderStateName)}}</span>
				<view class="tips">
					<span @click.stop="showClick(item)">
						<u-icon size="25" name="https://zqcx.di-digo.com/app/image/diandian.png"></u-icon>
					</span>
					<template v-if="tipsList.length!=0">
						<view class="trip_class" v-show="item.show">
							<view class="footbtn" v-for="it in tipsList" :key="it.text" @click.stop="itClick(it,item)">
								<u-icon :name="it.icon"></u-icon>{{it.text}}
							</view>
						</view>
					</template>
				</view>
			</view>
		</view>
		<view class="divider"></view>
		<view class="text-dh">申请单号：
			<span>{{dataWash(item.applyCode)}}</span>
			<span class="text-copy" @click.stop="copyClick(item)" v-if="item.applyCode">复制</span>
		</view>
		<view class="text-dh app_t_line">用车日期：<span>{{timeWash(item,true)}}</span></view>
		<view class="text-dh app_t_line">用车时段：<span>{{dataWash(item.dateName,false)}}</span></view>
		<!-- <view class="text-dh app_t_line">用车次数：<span>{{dataWash(item.usrCarNumber)}}</span></view> -->
		<view class="text-dh app_t_line">申请原因：<span>{{dataWash(item.description)}}</span></view>
		<view class="text-dh app_t_line" v-if="tabtype==1">等待时长：<span class="text-rt">{{dataWash(item.waitTime)}}</span>
		</view>
		<view class="goCar" style="display: flex;justify-content: end;" @click.stop="goClick(item,true)"
			v-if="item.compOrderState == 200&&item.isApplyDIdi">
			<span class="text-copy goCarSpan">去用车</span>
		</view>

	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default: () => {
					return {}
				}
			},
			tabtype: {
				type: Number,
				default: () => {
					return ''
				}
			},
			tipsList: {
				type: Array,
				default: () => {
					return []
				}
			}
		},
		data() {
			return {
				compOrderList: [],

			}
		},
		mounted() {
			console.log(this.tabtype, 'tabtype');
		},
		methods: {
			goClick(item, t) {
				// item.type = t
				// this.$emit('goClick', item)


				if (t) {
					uni.switchTab({
						url: '/pages/wayPassenger/trip/trip'
					})
				} else {
					uni.$u.route('/pager/networkCarDetais/networkCarDetais', {
						id: item.applyId,
						type: 1,
					})
				}
			},
			copyClick(item) {
				uni.setClipboardData({
					data: item.applyCode,
					success: function() {
						uni.$u.toast('复制成功')
					}
				});
			},
			itClick(it, item) {
				this.$emit('tipsSelect', it, item)
			},
			showClick(val) {
				this.$emit('showType', val)
			},
			timeWash(val, t) {
				let start = val.startTimer.split(' ')
				let endt = val.endTimer.split(' ')
				return t ? `${start[0]}` : !t ? `${start[1]} - ${endt[1]}` : '-'
			},
			statsWash(v) {
				return v ? v : '-'
			},
			dataWash(v) {
				return v ? v : '-'
			},
		}
	}
</script>

<style scoped>
	.regulation {
		background-color: #7728F5;
		padding: 4rpx 16rpx;
		color: #fff;
		border-radius: 20rpx;
		margin-right: 20rpx;
	}

	.divider {
		margin: 16rpx 0;
		height: 1rpx;
		background: #cccc;
	}

	.text-dh {
		font-size: 24rpx;
		color: #999999;
	}

	.text-dh span {
		color: #000;
		margin-left: 10rpx;
	}

	.text-rt {
		color: #346CF2 !important;
	}

	.app_t_line {
		margin-top: 12rpx;
	}

	.tips {
		position: relative;
	}

	.trip_class {
		position: absolute;
		background-color: #fff;
		width: 240rpx;
		right: 40rpx;
		top: 30rpx;
		box-shadow: 0 0 5px 1px #6666665e;
		z-index: 999;
		border-radius: 20rpx;
		transition: background 0.3s ease-in-out;
	}

	.footbtn {
		height: 80rpx;
		line-height: 80rpx;
		font-size: 29rpx;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
	}

	.border {
		border-top: 1px solid #ccc;
		border-bottom: 1px solid #ccc;
	}

	.text-copy {
		margin: 0 20rpx !important;
		border-radius: 10rpx;
		color: #3894ff !important;
		padding: 0 10rpx;
		border: 1px solid #3894ff;
		background-color: #e9f3ff;
	}

	.goCar {
		display: flex;
		justify-content: end;
	}

	.goCarSpan {
		background-color: #7728F5;
		color: #fff !important;
		border: 1px solid #7728F5;
		padding: 10rpx 20rpx;
		font-size: 24rpx;
	}
</style>