<template>
	<view class="my">
		<!-- #ifdef H5 -->
		<view class="name_box">
			<!-- #endif -->

			<!-- #ifdef MP-WEIXIN -->
			<view class="name_box" style="padding-top: 100rpx;">
				<!-- #endif -->
				<view class="u-flex name_b">
					<u-avatar size="70" @click="goAvatar()" :src="headUrl"
						default-url='https://zqcx.di-digo.com/app/image/head_2.png'>
					</u-avatar>
					<view class="u-flex" @click="goperson()">
						<view class="name_sbox">
							<view class="name_t">
								{{userInfoVo.name}}
							</view>
							<view class="name_txt">
								{{compMerVo.compName}}
							</view>
						</view>
						<view class="right_icon">
							<u-icon color="#fff" name="arrow-right"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<view class="wallet_box" v-if="false">
				<view class="wallet" @click="gowallet()">
					<view class="u-flex u-row-between">
						<view>
							我的钱包
						</view>
						<view>
							<u-icon color="#fff" name="arrow-right"></u-icon>
						</view>
					</view>
					<view class="wallet_money">
						<view class="wallet_money_t">
							¥100.02
						</view>
						<view class="wallet_money_b">
							补贴余额
						</view>
					</view>
					<view class="walletr" style="left: 1px;top: 1rpx;"></view>
					<view class="walletr" style="right: 1px;top: 1rpx;width: 150rpx;height: 150rpx;"></view>
					<view class="walletr" style="right: 280rpx;top: 160rpx;width: 100rpx;height: 100rpx;"></view>
				</view>

			</view>
			<u-cell-group class="cellgroup" :border='false'>
				<view v-for="item in workBoxList" :key='item.resName'>
					<view class="car_box" style="border-radius:0" v-if="item.resName=='我的车辆'">
						<view class="car_tit u-flex u-row-between" @click="goCarList">
							<view>
								{{item.resName}}
							</view>
							<view>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</view>
						<swiper class="swiper_box" :indicator-dots="indicatorDots" :autoplay="autoplay"
							:interval="interval" :duration="duration" v-if="carList.length!=0">
							<swiper-item v-for="(item,index) in carList" :key="index">
								<view class="u-flex car">
									<u-icon class="iconStyle" size="40"
										name="https://zqcx.di-digo.com/app/image/icon-3.png">
									</u-icon>
									<view class="car_name_sbox">
										<view class="car_t">
											{{ `${item.carNumber}  `}} <span v-if="item.numberColor">({{item.numberColor}})</span>
										</view>
										<view class="car_txt">
											{{ `${item.carBrandName} ${item.brandModelName} ${item.carColor}`}}
										</view>
									</view>
									<view class="car_icon">
										<view class="car-btn">
											已加入
										</view>
									</view>
								</view>
							</swiper-item>
						</swiper>
					</view>
					<template v-else>
						<u-cell :title="item.resName" @click="goItemPage(item)">
							<u-icon class="iconStyle" slot="icon" size="18" :name="item.menuUrl"></u-icon>
						</u-cell>
					</template>

				</view>
			</u-cell-group>

			<view class="cell_list">
				<u-cell-group class="cellgroup" :border='false'>
					<!-- <u-cell title="订单管理" @click="goPage(0)">
						<u-icon class="iconStyle" slot="icon" size="18"
							name="https://zqcx.di-digo.com/app/image/my_icon1.png"></u-icon>
					</u-cell> -->
					<!-- <u-cell title="权限说明" @click="goPage(1)">
					<u-icon class="iconStyle" slot="icon" size="18" name="https://zqcx.di-digo.com/app/image/my_icon2.png"></u-icon>
				</u-cell>
				<u-cell title="常用地址" :border='false' @click="goPage(2)">
					<u-icon class="iconStyle" slot="icon" size="18" name="https://zqcx.di-digo.com/app/image/my_icon3.png"></u-icon>
				</u-cell> -->
					<u-cell title="切换司机端" @click="goPage(3)" v-if="userInfo.appTerminal=='1,2'">
						<u-icon class="iconStyle" slot="icon" size="18"
							name="https://zqcx.di-digo.com/app/image/driver_icon2.png"></u-icon>
					</u-cell>
				</u-cell-group>
			</view>

			<view class="cell_list">
				<u-cell-group class="cellgroup" :border='false'>
					<u-cell title="设置" :border='false' @click="goset()">
						<u-icon class="iconStyle" slot="icon" size="18"
							name="https://zqcx.di-digo.com/app/image/my_icon4.png"></u-icon>
					</u-cell>
				</u-cell-group>
			</view>
			<!-- <tabBart :current='4'></tabBart> -->
		</view>
</template>

<script>
	import {
		getuserinfo,
		appcacherole,
		getmenubyuser,
	} from '@/config/api.js';
	import {
		carGetcarbyuserList,
		messagecount
	} from '@/config/consoler.js';

	import tabBart from '@/components/tabBart/tabBart.vue'
	export default {
		components: {
			// tabBart
		},
		data() {
			return {
				indicatorDots: true,
				autoplay: false,
				interval: 2000,
				duration: 500,
				compMerVo: {},
				userInfoVo: {
					name: null,
					compName: null
				},
				headUrl: '',
				carList: [],
				userInfo: {},
				workBoxList: []
			};
		},
		onLoad() {
			// uni.hideTabBar()
			this.userInfo = this.$common.getItem('userInfo')
			this.getMsg()
		},
		methods: {
			goItemPage(item) {
				if (!item.children) return uni.$u.toast('暂无内容')
				item.resPath = item.children.map(v => {
					return v.resPath
				}).join()
				
				uni.navigateTo({
					url: `/${item.resUrl}?list=${item.resPath}`
				})
			},
			getMsg() {
				messagecount({}).then(res => {
					let gentle = res.countPush + res.countSystem
					let sum = gentle > 99 ? '99+' : `${gentle}`
					uni.setTabBarBadge({
						index: 3,
						text: sum ? sum : '0'
					})
				})
			},
			getInitr() {
				getmenubyuser().then((res) => {
					let att = res.filter(v => {
						return v.resUrl == "my"
					})
					this.workBoxList = att[0].children ? att[0].children : [],
						console.log(this.workBoxList, '');
				})
				carGetcarbyuserList().then(v => {
					this.carList = v.filter(res => {
						return res.selfCarState == 20
					})
				})
				getuserinfo({
					params: {}
				}).then(data => {
					this.compMerVo = data.compMerVo;
					this.userInfoVo = data.userInfoVo;
					this.headUrl = data.userInfoVo.headUrl;
				})



			},
			goPage(type) {
				let that = this
				if (type == 0) {
					uni.$u.route('/pager/orderMenu/orderMenu');
				} else if (type == 3) {
					uni.showModal({
						content: '是否确定切换至司机端',
						success: (res) => {
							if (res.confirm) {
								that.adoptFun()
							}
						}
					})
				}
			},
			adoptFun() {
				appcacherole({
					cacheRoleType: '2'
				}).then(data => {
					uni.reLaunch({
						url: '/pageDriver/driveIndex/driveIndex'
					})
					// uni.$u.route('/pageDriver/driveIndex/driveIndex')
				})
			},
			goperson() {
				uni.$u.route('/pager/myPersonal/myPersonal', {
					...this.compMerVo,
					...this.userInfoVo
				});
			},
			goCarList() {
				uni.$u.route('/pages/wayPassenger/my/myCar/myCar');
			},
			gowallet() {
				uni.$u.route('/pages/wayPassenger/my/wallet/wallet');
			},
			goset() {
				uni.$u.route('/pages/wayPassenger/my/setting/setting');
			},
			goAvatar() {
				uni.$u.route('/pager/myAvatar/myAvatar');
			}

		},
		onShow() {

			this.getInitr()
		}
	}
</script>

<style lang="scss">
	.my {
		padding-bottom: 80rpx;

		.name_box {
			padding: 30rpx 32rpx 0 32rpx;
			color: #fff;
			background: linear-gradient(30deg, #7D83FB 0%, #06AFFF 100%);
			overflow: hidden;

			.name_b {
				margin: 56rpx 0;
			}

			.name_sbox {
				margin-left: 24rpx;

				.name_t {
					font-size: 32rpx;
					margin-bottom: 24rpx;
				}

				.name_txt {
					font-size: 28rpx;
				}
			}

			.right_icon {
				position: absolute;
				right: 32rpx;
			}
		}

		.wallet_box {
			border-radius: 27rpx;
			background-color: #fff;
			padding: 32rpx;
			margin-top: -20rpx;
			font-size: 28rpx;
			position: relative;

			.walletr {
				position: absolute;
				width: 200rpx;
				height: 200rpx;
				border-radius: 50%;
				background: #ffffff26;
			}

			.wallet {
				background-color: #06AFFF;
				color: #fff;
				padding: 20rpx 21rpx;
				border-radius: 13rpx;



				.wallet_money {
					text-align: center;

					.wallet_money_t {
						font-size: 44rpx;
						margin-top: 10rpx;
					}

					.wallet_money_b {
						font-size: 24rpx;
						margin-top: 10rpx;
						margin-bottom: 16rpx;
					}
				}
			}
		}



		.car_box {
			border-radius: 27rpx;
			background-color: #fff;
			font-size: 28rpx;
			margin-top: 32rpx;

			.car_tit {
				padding: 32rpx;
				border-bottom: 1px solid #f5f5f5;
			}

			.car {
				background: #6F88FC;
				box-shadow: 0rpx 5rpx 7rpx 0rpx rgba(111, 136, 252, 0.67);
				border-radius: 13rpx;
				padding: 43rpx 69rpx 35rpx 47rpx;
				margin: 0 32rpx;

				.car_name_sbox {
					color: #fff;
					margin-left: 20rpx;

					.car_t {
						font-size: 28rpx;
						margin-bottom: 6rpx;
					}

					.car_txt {
						font-size: 24rpx;
					}
				}

				.car_icon {
					position: absolute;
					right: 79rpx;

					.car-btn {
						padding: 9rpx 20rpx;
						font-size: 24rpx;
						color: #346CF2;
						background-color: #fff;
						border-radius: 7rpx;
					}
				}
			}

			.swiper_box {
				// margin-top: 34rpx;
				height: 220rpx;
			}
		}


		/deep/ uni-swiper .uni-swiper-dot {
			background-color: transparent;
			border: 1px solid #ccc;
		}

		/deep/ uni-swiper .uni-swiper-dot-active {
			background-color: #346CF2;
		}

		.cell_list {
			background-color: #fff;
			margin-top: 33rpx;

			/deep/ .u-line {
				border-color: #E9ECF7 !important;
				margin: 0 32rpx !important;
				width: calc(100% - 64rpx) !important;
			}

			.iconStyle {
				margin-right: 14rpx;
			}
		}
	}

	/deep/.u-cell {
		background-color: #fff;
	}
</style>