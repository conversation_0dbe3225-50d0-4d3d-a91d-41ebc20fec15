<template>
	<view class="apply">
		<u-navbar title="我的订单" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="nav_box">
			<u-tabs :list="navList" :scrollable="true" lineWidth="22" lineHeight="2" lineColor="#346CF2"
				:activeStyle="{color: '#346CF2'}" :inactiveStyle="{color: '#999999'}" @click="changeTab"
				itemStyle="height: 40px; padding:0 10px">
			</u-tabs>
		</view>
		<view class="tabpage">
			<view v-for="(item,index) in dataList" :key="index">
				<view class="trip_b_time">
					{{item.reserveStartTimeSeconds | date('mm-dd')}}
					<!-- {{getWeekDay(new Date(item.reserveStartTimeSeconds))}} -->
				</view>
				<view class="app_list" @click="goDetail(item)">
					<view v-if="item.travelType==1" class="jiao_img">
						<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/qi.png">
						</u-image>
					</view>
					<view v-if="item.travelType==2" class="jiao_img">
						<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/zu.png">
						</u-image>
					</view>
					<view @click="goDetail(item)">
						<view class="u-flex u-row-between">
							<view class="u-flex">
								<span class="text-dh">{{item.carNumber}} {{item.numberColor}}</span>
							</view>
							<view class="text-rt u-flex">
								<u-tag
									:text='item.compOrderState==10?"待审批":item.compOrderState==95?"行后待审批":item.compOrderState==20?"待派车":item.compOrderState==24?"租赁待审批":item.compOrderState==25?"租赁待派车":item.compOrderState==30?"待接单":item.compOrderState==40?"待执行":item.compOrderState==50?"执行中":item.compOrderState==60?"到达出发地":item.compOrderState==70?"进行中":item.compOrderState==80?"到达目的地":item.compOrderState==120?"结算审核中":item.compOrderState==200?"已完成":item.compOrderState==210?"已取消":item.compOrderState==220?"审批驳回":item.compOrderState==230?"调度驳回":item.compOrderState==100?"待确认":item.compOrderState==90?"已回场":item.compOrderState==110?"待结算":item.compOrderState==205?"待评价":"订单异常"'
									type="warning" size="mini" plain></u-tag>
								<u-icon name="arrow-right" color='#f9ae3d'></u-icon>
							</view>
						</view>

						<u-line color="#E6E6E6" margin="20rpx 0"></u-line>

						<view class="app_t_line u-flex">
							<u-image class="tab_icon" :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/driver_s_icon2.png">
							</u-image>
							<span>行程单号:{{item.travelCode}}</span>
							<span class='text-c' v-if="item.dispatchMode==1">拆</span>
						</view>

						<view class="app_t_line u-flex" v-if="item.travelType==2">
							<u-image class="tab_icon" :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/gongsi2.png"></u-image>
							<span>{{item.applyUnitName}}</span>
						</view>


						<!-- 企业单，预约开始时间跟预约结束时间 -->
						<view class="app_t_line u-flex" v-if="item.travelType==1">
							<u-image class="tab_icon" :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/driver_s_icon1.png">
							</u-image>
							<span>{{item.reserveStartTimeSeconds | date('mm月dd日 hh:MM')}} -
								{{item.reserveEndTimeSeconds| date('mm月dd日 hh:MM')}}</span>
						</view>

						<!-- 租赁单，预约开始时间跟计费套餐全称 -->
						<view class="app_t_line u-flex u-row-between" v-else>
							<div class="u-flex">
								<u-image class="tab_icon" :width="14" :height="14"
									src="https://zqcx.di-digo.com/app/image/driver_s_icon1.png">
								</u-image>
								<span>{{item.reserveStartTimeSeconds | date('mm月dd日 hh:MM')}}</span>
							</div>
							<span>{{item.valuationFullName}}</span>
						</view>

						<view class="app_t_line u-flex">
							<view class="spot"></view>
							<span>{{item.fromAddrName}}</span>
						</view>

						<view class="app_t_line u-flex" v-if="item.throughAddrInfoName">
							<view class="spot" style="background-color: #5ac725;"></view>
							<div style="flex: 1;">{{item.throughAddrInfoName}}</div>
						</view>

						<view class="app_t_line u-flex">
							<view class="spot spottwo"></view>
							<span>{{item.toAddrName}}</span>
						</view>
					</view>

				</view>

			</view>
		</view>

		<!-- 更多 -->
		<u-loadmore v-if="dataList.length>0" :status="status" />

		<u-empty v-if="dataList.length==0" mode="order" icon="http://cdn.uviewui.com/uview/empty/order.png">
		</u-empty>

	</view>
</template>

<script>
	import {
		driverorderlist,
		driverorderinfo
	} from '@/config/api.js';
	export default {
		data() {
			return {
				dataList: [],
				status: 'loadmore',
				pageNum: 1,
				pageSize: 20,
				hasNext: true,
				type: '1',
				navList: [{
						name: '全部',
						val: '1'
					}, {
						name: '待执行',
						val: '2'
					}, {
						name: '进行中',
						val: '3'
					}, {
						name: '待确认',
						val: '4'
					}, {
						name: '待评价',
						val: '5'
					}, {
						name: '已完成',
						val: '6'
					},
					// {
					// 	name: '驳回/取消',
					// 	val:'230'
					// }
					// , {
					// 	name: '申诉',
					// 	val:'20'
					// },
				]
			};
		},
		onShow() {
			this.queryList()
		},
		onLoad() {
			// this.queryList()
		},
		
		onReachBottom() {
			if (this.hasNext) {
				this.status = 'loading';
				this.pageNum = ++this.pageNum;
				setTimeout(() => {
					this.queryList()
				}, 1000)
			}
		},
		methods: {
			getWeekDay(date) {
				let week;
				if (date.getDay() == 0) week = "周日"
				if (date.getDay() == 1) week = "周一"
				if (date.getDay() == 2) week = "周二"
				if (date.getDay() == 3) week = "周三"
				if (date.getDay() == 4) week = "周四"
				if (date.getDay() == 5) week = "周五"
				if (date.getDay() == 6) week = "周六"
				return week;
			},
			changeTab(item) {
				if (item.val == this.type) return
				this.type = item.val
				this.$set(this, 'pageNum', 1)
				this.$set(this, 'dataList', [])

				this.queryList()
			},
			// 跳转详情
			goDetail(item) {
				
				
				uni.$u.route('/pages/map/driverMap', {
					title: '订单详情',
					id: item.travelId,
					valuationId: item.valuationId
				})
				
				return
				
				uni.$u.route('/pages/map/mapnotit', {
					url: "/hybrid/html/drivermap.html",
					title: '订单详情',
					id: item.travelId,
					valuationId: item.valuationId,
					type: this.type
				});
				this.dataList = []
			},
			queryList() {
				let params = {
					type: this.type,
					pageNum: this.pageNum,
					pageSize: this.pageSize,
				}

				driverorderlist({
					params: params
				}).then((data) => {
					data.pageList.forEach(v => {
						if (v.throughAddrInfo) {
							v.throughAddrInfo = JSON.parse(v.throughAddrInfo)
							this.$set(v, 'throughAddrInfoName', v.throughAddrInfo.map(v => v.siteAddrName)
								.join(
									' → '))
						}
					})
					if (params.pageNum == 1) {
						this.dataList = data.pageList
					} else {
						this.dataList.push(...data.pageList)
					}
					this.hasNext = data.hasNext
					if (data.hasNext) {
						this.status = 'loadmore'
					} else {
						this.status = 'nomore'
					}
				})
			},

		}
	}
</script>

<style lang="scss" scoped>
	.apply {
		.nav_box {
			background-color: #fff;
		}

		.tab_top {
			width: 520rpx;
			margin: 23rpx auto;

			.tab_top_btn {
				width: 133rpx;
				height: 53rpx;
				line-height: 53rpx;
				text-align: center;
				border-radius: 30rpx;
				font-size: 28rpx;
			}

			.tab_top_btn.active {
				background-color: #346CF2;
				color: #fff;
			}
		}

		.tabpage {
			.trip_b_time {
				margin: 20rpx 33rpx;
				font-size: 28rpx;
			}

			.trip_b_time.before {
				color: #999;
			}

			.filterBox {
				padding: 0 32rpx;

				.search {
					/deep/.u-search__content {
						background-color: #E9ECF7 !important;
					}

					/deep/.u-search__content__input {
						background-color: #E9ECF7 !important;
					}
				}
			}

			.app_list {
				background-color: #fff;
				margin: 14rpx 11rpx;
				border-radius: 11rpx;
				padding: 30rpx;
				font-size: 28rpx;
				position: relative;

				.jiao_img {
					position: absolute;
					left: 0;
					top: 0;
				}

				.appbtnbox {
					// position: absolute;
					// right: 27rpx;
					// bottom: 27rpx;
				}

				.appbtn {
					background-color: #346CF2;
					padding: 4rpx 18rpx;
					font-size: 28rpx;
					color: #fff;
					border-radius: 8rpx;
					margin-left: 30rpx;
				}

				.icon-right {
					position: absolute;
					top: 50%;
					right: 27rpx;
					margin-top: -16rpx;
				}

				.tab_icon {
					margin-right: 16rpx;
				}

				.text-dh {
					font-size: 32rpx;
					font-weight: bold;
					color: #262626;
					padding-left: 30rpx;
				}

				.text-rt {
					color: #346CF2;
				}

				.text-c {
					border: 1rpx solid #9659f7;
					background: #eee5fe;
					margin: 20rpx;
					padding: 0 5rpx;
					color: #9659f7;
					border-radius: 10rpx;
					font-size: 16rpx;
				}

				.app_t_line {
					margin-top: 14rpx;
				}

				.spot {
					width: 16rpx;
					height: 16rpx;
					background-color: #239EFC;
					border-radius: 50%;
					margin: 0 24rpx 0 8rpx;
				}

				.spottwo {
					background-color: #FF7031;
				}

				.spotthree {
					background-color: #28D79F;
				}
			}
		}
	}
</style>
