<template>
	<view class="conten">
		<u-navbar @leftClick="leftClick" :title="getTitle" :placeholder="true"></u-navbar>
		<cover-view class="tips">{{estimate}}</cover-view>
		<map id='map' :longitude="lnglat[0]" :latitude="lnglat[1]" :markers="markers" :polyline="planLists" :scale="14"
			show-location="true" style="width: 100vw; height: 80vh;">
		</map>
		<MpWx :detail="detailData" :position='position' @refresh='getInfo'></MpWx>
	</view>
</template>

<script>
	let that
	import MpWx from "./MpWx.vue"
	import {
		gdapi,
		amapApi
	} from '@/config/consoler.js'
	import {
		driverorderinfocopy
	} from '@/config/api.js';
	export default {
		components: {
			MpWx
		},
		data() {
			return {
				detailData: {
					compOrderState: null,
					orderEventHis: []
				},
				option: {},
				planList: [],
				position: ''

			}
		},

		onLoad(op) {
			that = this
			if (op) {
				that.$set(that, 'option', op)
			}
			that.getInfo()
			that.getPosition()
		},

		methods: {
			getInfo() {
				driverorderinfocopy({
					id: that.option.id
				}).then(res => {
					if (res.throughAddrInfo) {
						let arrs = JSON.parse(res.throughAddrInfo)
						that.$set(res, 'throughAddrInfo', arrs)
						that.$set(res, 'throughAddrInfoName', arrs.map(v => v.siteAddrName)
							.join(
								' → '))
					}
					if (res.feeDetail) {
						res.feeDetail = JSON.parse(res.feeDetail)
						if (Array.isArray(res.feeDetail)) {
							res.feeDetail.forEach(res => {
								if (res.feeDetailCode == 200 && !Array.isArray(res.unit)) {
									res.unit = JSON.parse(res.unit)
								}
							})
						} else {
							res.feeDetail = JSON.parse(res.feeDetail)
						}

					}
					// console.log(res, '****************************')
					that.detailData = res
					that.getDetailMap()
				})

			},
			getDetailMap() {
				let detail = that.detailData || {}
				let waypoints = ''
				if (detail.throughAddrInfo) {
					waypoints = detail.throughAddrInfo.map(res => {
						return `${res.siteLng},${res.siteLat}`
					}).join(';')
				}
				// 暂用个人key 4f1a73814bc424b77d47ca032b6cd630
				let params =
					`origin=${detail.fromLng},${detail.fromLat}&destination=${detail.toLng},${detail.toLat}&waypoints=${waypoints||''}&key=f50ac9b4e67fc88593738c211a6658cc`

				uni.request({
					url: `https://zqcx.di-digo.com/amap/v3/direction/driving?${params}`,
					success: (res) => {
						let data = res.data.route || []
						if (data.paths && data.paths[0] && data.paths[0].steps) {
							washData(data.paths[0].steps)
						}
					},
					fail: (err) => {
						console.log('查看为什么路径规划报错了：' + err)
					}
				});

				const washData = (steps) => {
					console.log(steps, '路径规划打印出来看----')
					let points = []
					steps.forEach(item => {
						const polen = item.polyline.split(';')
						polen.forEach(vv => {
							let splits = vv.split(',')
							points.push({
								longitude: parseFloat(splits[0]),
								latitude: parseFloat(splits[1])
							})
						})
					})

					// 确保points数组不为空
					if (points.length > 0) {
						// 直接设置planList而不是this.planList
						that.$set(that, 'planList', [{
							points: points,
							width: 3,
							arrowLine: true,
							arrowIconPath: "../../static/arrow.png", // 添加箭头图标路径
							color: '#4fd408',
						}])
						console.log('设置路线数据成功，点数：', points.length)
					} else {
						console.log('路线点数据为空')
					}
				}
			},
			leftClick() {
				if (this.option.isBack) {
					uni.$u.route('/pageDriver/driveIndex/driveIndex')
				} else {
					// #ifdef H5
					uni.navigateBack({
						delta: 2,
					})
					// #endif

					// #ifdef MP-WEIXIN
					uni.navigateBack({
						delta: 1,
					})
					// #endif
				}
			},
			getPosition() {
				try {
					uni.getLocation({
						type: 'wgs84',
						success: function(res) {
							gdapi(res).then(res => {
								that.$set(that, 'position', res.formatted_address ||
									`当前经纬度：${res.longitude} ${res.latitude}`)
							})
						}
					})
				} catch (error) {
					//TODO handle the exception
					console.log(error)
				}
			},
		},
		computed: {
			planLists() {
				// console.log('planLists计算属性被调用，数据：', JSON.stringify(this.planList))
				// 确保返回数组，即使是空数组
				return this.planList && this.planList.length > 0 ? this.planList : []
			},
			markers() {
				let covers = []
				if (!this.detailData) return []
				covers = [{
					id: 1000,
					latitude: this.detailData.fromLat || '',
					longitude: this.detailData.fromLng || '',
					width: 25,
					height: 25,
					callout: {
						content: this.detailData.fromAddrName,
						display: 'ALWAYS',
						padding: 5,
						borderRadius: 5
					},

					iconPath: '../../static/start.png'
				}, {
					id: 1001,
					latitude: this.detailData.toLat || '',
					longitude: this.detailData.toLng || '',
					width: 25,
					height: 25,
					callout: {
						content: this.detailData.toAddrName,
						display: 'ALWAYS',
						padding: 5,
						borderRadius: 5
					},
					iconPath: '../../static/end.png'
				}]

				if (this.detailData.throughAddrInfo && this.detailData.throughAddrInfo.length != 0) {
					this.detailData.throughAddrInfo.forEach((res, idx) => {
						covers.splice(1, 0, {
							id: 2001 + idx,
							latitude: res.siteLat || '',
							longitude: res.siteLng || '',
							width: 25,
							height: 25,
							callout: {
								content: res.siteAddrName,
								display: 'ALWAYS',
								padding: 5,
								borderRadius: 5
							},
							iconPath: '../../static/tu.png'
						})
					})
				}
				return covers
			},
			lnglat() {
				return [this.detailData.fromLng, this.detailData.fromLat]
			},
			estimate() {
				return `预估里程: ${(this.detailData.aboutDistance  / 1000).toFixed(1) || 0} 公里`
			},
			getTitle() {
				let text = '',
					e = this.detailData.compOrderState || 1000;
				text = e == 10 ? '待审批' : e == 95 ? '行后待审批' : e == 20 ? '待派车' : e == 24 ? '租赁待审批' : e == 25 ?
					'租赁待派车' :
					e == 30 ? '待接单' : e == 40 ? '待执行' : e == 50 ? '前往出发地' : e == 60 ? '前往目的地' : e == 70 ? '进行中' :
					e ==
					80 ? '到达目的地' : e == 90 ? '已回场' : e == 100 ? '待确认' : e == 200 ? '已完成' : e == 205 ? '待评价' : e ==
					210 ?
					'已取消' : e == 220 ? '审批驳回' : e == 230 ? '调度驳回' : e == -1 ? '订单异常' : '---'
				return text
			}
		},
	}
</script>

<style scoped>
	#map {
		width: 100vw;
		height: 80vh;
	}

	.conten {
		position: relative;
	}

	.tips {
		position: absolute;
		left: calc(50% - 150rpx);
		font-size: 24rpx;
		background: #fff;
		width: 300rpx;
		text-align: center;
		box-shadow: 0px 0px 2px 0px #ccc;
		padding: 6rpx;
		border-radius: 35rpx;
		z-index: 9;
		/* #ifdef H5 */
		top: 100rpx;
		/* #endif */
		/* #ifdef MP-WEIXIN */
		top: 200rpx;
		/* #endif */
	}
</style>