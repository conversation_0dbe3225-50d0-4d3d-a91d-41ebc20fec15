<template>
	<view>
		<cover-view class="TabBar tabBarAuto">
			<cover-view class="tab" v-for="(item,index) in list" :key="index" @click="navigatorTo(item.pagePath)">
				<cover-image class="plain" :src="current === index ? item.selectedIconPath : item.iconPath">
				</cover-image>
				<cover-view :class="['text',{'active':current === index}]">{{item.text}}
				</cover-view>
				<view v-if="index==3" class="airbubble"> {{msgNumber}} </view>
			</cover-view>
		</cover-view>
	</view>
</template>

<script>
	import {
		getuserinfo,
	} from '@/config/api.js';
	export default {
		data() {
			return {
				msgNumber: 0,
				tabValr: '首页',
				list: [{
						"pagePath": "/pages/wayPassenger/index/index",
						"iconPath": "/static/home.png",
						"selectedIconPath": "/static/home-select.png",
						"text": "首页"
					},
					{
						"pagePath": "/pages/wayPassenger/trip/trip",
						"iconPath": "/static/trip.png",
						"selectedIconPath": "/static/trip-select.png",
						"text": "行程"
					},
					{
						"pagePath": "/pages/wayPassenger/workBench/newWorkBench",
						"iconPath": "/static/workBench.png",
						"selectedIconPath": "/static/workBench-select.png",
						"text": "工作台"
					},
					{
						"pagePath": "/pages/wayPassenger/message/message",
						"iconPath": "/static/message.png",
						"selectedIconPath": "/static/message-select.png",
						"text": "消息"
					},
					{
						"pagePath": "/pages/wayPassenger/my/my",
						"iconPath": "/static/my.png",
						"selectedIconPath": "/static/my-select.png",
						"text": "我的"
					}
				]
			}
		},
		created() {},
		props: {
			//激活的tabbar，默认第一个页面tabbar激活
			current: {
				type: Number,
				default: 0
			}
		},
		methods: {
			getMsg() {
				let that = this
				getuserinfo({}).then(res => {
					that.$set(this, 'msgNumber', res.userInfoVo.message)
				})
			},
			async navigatorTo(params) {
				uni.switchTab({
					url: params,
				})
				await this.getMsg()
			},

		}
	}
</script>

<style lang="scss" scoped>
	.tabBarAuto {
		padding-bottom: 0;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.TabBar {
		position: fixed;
		bottom: 0;
		left: 0;
		height: 100rpx;
		width: 750rpx;
		background-color: #fff;
		z-index: 9999;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.tab {
		width: 150rpx;
		height: 100rpx;
		text-align: center;
		position: relative;
		display: flex;
		flex-flow: column nowrap;
		align-items: center;
		justify-content: center;

		.airbubble {
			position: absolute;
			background: #f56c6c;
			z-index: 99;
			top: 6rpx;
			right: 20rpx;
			color: #fff;
			padding: 4rpx 10rpx;
			border-radius: 100rpx;
			font-size: 24rpx;
		}

		.plain {
			margin-top: 8rpx;
			width: 48rpx;
			height: 48rpx;
		}

		.special {
			width: 80rpx;
			height: 80rpx;
		}

		.text {
			text-align: center;
			line-height: 1.7;
			font-size: 24rpx;
			color: #8a8a8a;
			position: relative;



			&.active {
				color: #0048FF;
				// color: #ff007f;
			}
		}

	}


	.centerIcon {
		position: fixed;
		width: 100%;
		height: 123rpx;
		bottom: 0;
		left: 0;
		display: flex;
		align-items: flex-start;
		justify-content: center;
		z-index: 9988;
	}

	.centerIcon-img {
		width: 80rpx;
		height: 80rpx;
	}
</style>
