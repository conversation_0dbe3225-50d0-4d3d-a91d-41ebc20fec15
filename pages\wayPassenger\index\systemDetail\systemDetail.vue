<template>
	<view class="systemDetail">
		<u-navbar title="制度详情" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="Title">
			<u-cell-group class="cellgroup" :border='false'>
				<u-cell :border='false' title="用车制度" :value="dataObj.regulationName"></u-cell>
			</u-cell-group>
		</view>
		<view class="entp_content">
			<u-cell-group class="cellgroup" :border='false'>
				<u-cell title="制度信息 "></u-cell>
				<u-cell :titleStyle="titleStyle" :border='false' title="可用日期" :value="dataObj.settingAvailableDate">
				</u-cell>
				<u-cell :titleStyle="titleStyle" :border='false' title="用车时段" :value="dataObj.settingAvailableTime">
				</u-cell>
				<u-cell :titleStyle="titleStyle" :border='false' title="用车地点" :value="dataObj.settingAvailableAddress">
				</u-cell>
				<u-cell :titleStyle="titleStyle2" :border='false' title="可用车型" >
					<text slot='value' style="text-align: right;"> {{dataObj.availableCarTypeList}}</text>
				</u-cell>
				<u-cell :titleStyle="titleStyle" :border='false' title="用车限制" :value="dataObj.regulationScene"></u-cell>
				<u-cell :titleStyle="titleStyle" :border='false' title="每次金额" :value="dataObj.limitMoney+'元'"></u-cell>
				<!-- <u-cell  :titleStyle="titleStyle" :border='false' title="每日限额" :value="dataObj.settingLimitMoney"></u-cell> -->
				<u-cell :titleStyle="titleStyle" :border='false' title="同城限制" :value="dataObj.settingAcrossCity">
				</u-cell>
				<u-cell :titleStyle="titleStyle" :border='false' title="用车备注" :value="dataObj.settingRemark"></u-cell>
			</u-cell-group>
		</view>
	</view>
</template>

<script>
	import {
		regulationinfor
	} from '@/config/api.js';
	export default {
		data() {
			return {
				titleStyle: {
					"color": "#999999",
					"min-width": "200rpx"
				},
				titleStyle2: {
					"color": "#999999",
					"min-width": "200rpx",
					"text-align": 'left'
				},
				dataObj: {
					regulationName: '',
					settingAvailableTime: '',
					settingAvailableDate: '',
					settingAvailableAddress: '',
					availableCarTypeList: '',
					settingAcrossCity: '',
					limitMoney: '',
					settingLimitMoney: '',
					regulationScene: '',
					settingRemark: ''
				},
				optionr:{}
			};
		},
		onLoad(option) {
			this.optionr=option
			this.getDetail()
		},
		methods: {
			getDetail() {
				regulationinfor({
					params: {
						id: this.optionr.id
					}
				}).then((data) => {
					if (data.settingAvailableTime == 0) {
						this.dataObj.settingAvailableTime = '不限'
					} else if (data.settingAvailableTime == 1) {
						this.dataObj.settingAvailableTime = '自定义时段'
					} else if (data.settingAvailableTime == 2) {
						this.dataObj.settingAvailableTime = '每日时段'
					} else if (data.settingAvailableTime == 3) {
						this.dataObj.settingAvailableTime = '工作日/节假日'
					}

					if (data.settingAvailableDate == 0) {
						this.dataObj.settingAvailableDate = '不限'
					} else if (data.settingAvailableDate == 1) {
						this.dataObj.settingAvailableDate = '自定义时段'
					}


					if (data.settingAvailableAddress == 0) {
						this.dataObj.settingAvailableAddress = '不限'
					} else if (data.settingAvailableAddress == 1) {
						this.dataObj.settingAvailableAddress = '常驻城市'
					} else if (data.settingAvailableAddress == 2) {
						this.dataObj.settingAvailableAddress = '限定可用城市'
					} else if (data.settingAvailableAddress == 3) {
						this.dataObj.settingAvailableAddress = '指定出发地或目的地'
					}

					if (data.availableCarTypeList && data.availableCarTypeList.length > 0) {
						data.availableCarTypeList.map((item) => {
							this.dataObj.availableCarTypeList += item.carTypeFullName + '、'
						})
					}

					if (data.settingAcrossCity == 0) {
						this.dataObj.settingAcrossCity = '不允许跨城'
					} else if (data.settingAcrossCity == 1) {
						this.dataObj.settingAcrossCity = '允许跨城'
					} else if (data.settingAcrossCity == 2) {
						this.dataObj.settingAcrossCity = '允许部分城市跨城'
					}

					this.dataObj.limitMoney = data.limitMoney

					if (data.settingLimitMoney == 0) {
						this.dataObj.settingLimitMoney = '不限'
					} else if (data.settingLimitMoney == 1) {
						this.dataObj.settingLimitMoney = '限制'
					}

					if (data.regulationScene == 1) {
						this.dataObj.regulationScene = '企业公车'
					} else if (data.regulationScene == 5) {
						this.dataObj.regulationScene = '私车公用'
					} else if (data.regulationScene == 10) {
						this.dataObj.regulationScene = '网约车'
					}

					this.dataObj.regulationName = data.regulationName
					if (data.settingRemark == 0) {
						this.dataObj.settingRemark = '无需填写备注'
					} else if (data.settingRemark == 1) {
						this.dataObj.settingRemark = '需填写备注'
					}

				})
			},
		}
	}
</script>

<style lang="scss">
	.systemDetail {
		.Title {
			background-color: #fff;
			margin: 26rpx 0;
		}

		.entp_content {
			background-color: #fff;
		}
	}
</style>
