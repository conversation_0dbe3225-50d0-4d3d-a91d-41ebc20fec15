<template>
	<view>
		<u-navbar :title="data.title" :autoBack="true" :placeholder="true"></u-navbar>

		<u-cell-group class="cellgroup" :border='false'>
			<u-cell title="实际开始里程:">
				<u--input placeholder="请输入实际开始里程" v-model='mileValue.actualStartMileage' type='number' border='none'
					clearable slot='value' @input='startChange' :readonly='data.type==2'>
					<span slot='suffix'>公里</span>
				</u--input>
			</u-cell>
			<u-cell title="实际结束里程:" v-if="data.type==2">
				<u--input placeholder="请输入实际结束里程" v-model='mileValue.actualEndMileage' type='number' border='none'
					clearable slot='value' @input='endChange'>
					<span slot='suffix'>公里</span>
				</u--input>
			</u-cell>
		</u-cell-group>

		<view class="mile_up">
			<view class="u-flex u-flex-direction">
				<u-upload :fileList="startFile" :capture='["camera"]' @afterRead="afterRead" @delete="deletePic"
					multiple :maxCount="1" :disabled='data.type==2' :deletable='data.type==1'>
				</u-upload>
				<span>(实际开始里程)</span>
			</view>

			<view class="u-flex u-flex-direction" v-if="data.type==2">
				<u-upload :fileList="endFile" :capture='["camera"]' @afterRead="afterRead" @delete="deletePic" multiple
					:maxCount="1">
				</u-upload>
				<span>(实际结束里程)</span>
			</view>
		</view>

		<!-- #ifdef MP-WEIXIN -->
		<canvas canvas-id="watermarkCanvas" style="position: absolute; top: -9999px;"
			:style="{width:canvasWidth,height:canvasHeight}"></canvas>
		<!-- #endif -->
		<view class="footer_box">
			<u-button type="primary" color="#346CF2" text="确认" @click="submit"></u-button>
		</view>


	</view>
</template>

<script>
	import {
		carPrivateorderinfo,
		carPrivatestateorder,
		carPrivaRivedes,
		uploadImg,
	} from '@/config/consoler.js';
	export default {
		data() {
			return {
				data: {},
				valDtail: {},
				mileValue: {},
				startFile: [],
				endFile: [],
				imagePath: '',
				address: {},
				addressName: '',
				canvasWidth: '375px',
				canvasHeight: '600px',
			}
		},
		watch: {
			address(news, ope) {
				this.getArdds(news)
			}
		},
		onLoad(op) {
			op.title = op.type == 1 ? '开始里程' : '结束里程'
			this.data = op
			this.getDtail()
		},
		methods: {
			submit() {
				if (this.data.type == 1) {
					if (!this.mileValue.actualStartMileage) return uni.$u.toast('请输入开始里程')
					if (this.startFile.length == 0) return uni.$u.toast('请上传开始里程图片')
					let obj = {
						actualStartMileage: this.mileValue.actualStartMileage,
						startMileageImgUrl: this.startFile[0].urlr,
						id: this.valDtail.travelId
					}
					carPrivatestateorder(obj).then(res => {
						uni.$u.toast('操作成功')
						this.mileValue.actualStartMileage = ''
						this.startFile = []
						uni.navigateBack()
					})
				} else {
					if (!this.mileValue.actualEndMileage) return uni.$u.toast('请输入结束里程')
					if (this.mileValue.actualStartMileage > this.mileValue.actualEndMileage) return uni.$u.toast(
						'结束里程要大于实际开始里程')
					if (this.endFile.length == 0) return uni.$u.toast('请上传结束里程图片')

					let obj = {
						actualEndMileage: this.mileValue.actualEndMileage,
						endMileageImgUrl: this.endFile[0].urlr,
						id: this.valDtail.travelId
					}

					carPrivaRivedes(obj).then(res => {
						uni.$u.toast('操作成功')
						this.mileValue.actualEndMileage = ''
						this.endFile = []
						uni.navigateBack()
					})
				}
			},
			// 上传附件
			afterRead(event) {
				let t = this.data.type == 1 ? 'startFile' : 'endFile'
				let lists = [].concat(event.file)
				let fileListLen = this[t].length

				lists.forEach(r => {
					// #ifdef MP-WEIXIN
					this.addWatermarkIn(r, t, fileListLen)
					// #endif
					// #ifdef H5
					this.getImage(r, t, fileListLen)
					// #endif
				})
			},
			// h5
			getImage(r, t, fileListLen) {
				const url = r.url
				let image = new Image();
				image.src = r.url;
				image.onload = () => {
					let date = uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd hh:MM:ss')

					let canvas = document.createElement('canvas');
					canvas.width = image.width;
					canvas.height = image.height;

					let ctx = canvas.getContext('2d');

					ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

					let fontSize = Math.min(canvas.width, canvas.height) * 0.03;

					ctx.font = `${fontSize}px Arial`;
					ctx.fillStyle = '#fff';

					ctx.fillText(date, 20, canvas.height - 260);
					ctx.fillText(`${this.addressName}`, 20, canvas.height - 160);


					const toDataURL = canvas.toDataURL('image/jpeg', 0.3)
					let fles = {
						url: toDataURL
					}
					this.uploadImage(fles, t, fileListLen)
				};
			},
			// 在微信小程序中添加水印
			addWatermarkIn(r, t, fileListLen) {
				let that = this
				uni.getImageInfo({
					src: r.url,
					success: (info) => {
						let date = uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd hh:MM:ss')

						that.canvasWidth = info.width + 'px';
						that.canvasHeight = info.height + 'px';

						const ctx = uni.createCanvasContext('watermarkCanvas', that)
						ctx.drawImage(r.url, 0, 0, info.width, info.height);

						// 计算字体大小，可以根据需要进行调整
						const fontSize = Math.min(info.width, info.height) * 0.02;
						console.log(fontSize, 'fontSize');
						ctx.setFontSize(fontSize);
						ctx.fillStyle = '#fff';

						ctx.fillText(date, 20, info.height - 200);
						ctx.fillText(`${this.addressName}`, 20, info.height - 160);

						ctx.draw(false, () => {
							setTimeout(() => {
								uni.canvasToTempFilePath({
									canvasId: "watermarkCanvas",
									fileType: 'jpg',
									width: info.width,
									height: info.height,
									success: res => {
										let fles = {
											url: res.tempFilePath
										}
										this.uploadImage(fles, t, fileListLen)
									}
								})
							}, 500)
						});
					},
					fail: (error) => {
						console.error('获取图片信息失败', error);
					},
				});
			},
			uploadImage(fles, t, fileListLen) {
				uni.showLoading({
					title: '正在上传中..'
				});
				uploadImg(fles, 4).then(res => {
					if (res) {
						this[t].splice(fileListLen, 1, Object.assign({
							status: 'success',
							message: '',
							urlr: res,
							url: fles.url
						}))
						uni.hideLoading();
					} else {
						uni.hideLoading();
						this[t].splice(fileListLen, 1)
						uni.$u.toast('上传失败')
					}
				})
			},
			// 删除附件
			deletePic(event) {
				if (this.data.type == 1) {
					this.startFile.splice(event.index, 1)
				} else {
					this.endFile.splice(event.index, 1)
				}
			},
			getDtail() {
				let that = this
				carPrivateorderinfo({
					id: this.data.id
				}).then(res => {
					this.valDtail = res
					if (this.data.type == 2) {
						this.mileValue.actualStartMileage = res.actualStartMileage
						this.startFile = [{
							url: res.startMileageImgUrl
						}]
					}
				})
				this.getLocations()
			},
			getLocations() {
				let that = this
				uni.showLoading({
					title: '正在获取定位..'
				})

				uni.getLocation({
					type: 'gcj02',
					success: function(res) {
						that.address = res
						uni.hideLoading()
						console.log('当前位置的经度：' + res.longitude);
						console.log('当前位置的纬度：' + res.latitude);
					},
					fail: () => {
						uni.hideLoading()

						uni.showModal({
							content: '定位获取失败,请重新获取',
							success: (res) => {
								that.addressName = '暂无定位'
								that.getLocations()
							}
						})
					}
				})
			},
			getArdds(res) {
				let that = this
				let url =
					`https://zqcxdev.di-digo.com/gdapi/v3/geocode/regeo?output=JSON&location=${res.longitude},${res.latitude}&key=f50ac9b4e67fc88593738c211a6658cc&radius=1000&extensions=all`
				uni.request({
					url: url, //仅为示例，并非真实接口地址。
					method: 'get',
					success: (res) => {
						if (res.statusCode == 200 && res.data.status == 1) {
							that.addressName = res.data.regeocode.formatted_address
							console.log(that.addressName);
						} else {
							uni.showModal({
								content: '定位获取失败,请重新获取',
								success: (res) => {
									that.addressName = '暂无定位'
									that.getLocations()
								}
							})
						}
					},

				});
			},
			endChange(val) {
				this.$nextTick(() => {
					this.$set(this.mileValue, 'actualEndMileage', val.match(/^\d*(\.?\d{0,1})/g)[0])
				})
			},
			startChange(val) {
				if (this.data.type == 2) return
				this.$nextTick(() => {
					this.$set(this.mileValue, 'actualStartMileage', val.match(/^\d*(\.?\d{0,1})/g)[0])
				})
			}
		}
	}
</script>

<style scoped>
	.cellgroup {
		background-color: #fff;
		margin-top: 20rpx;
	}

	.mile_up {
		margin: 30rpx 0;
		display: flex;
		justify-content: space-around;
		background: #fff;
		padding: 20px;
	}

	.mile_up span {
		font-size: 26rpx;
		margin-top: 20rpx;
	}

	/deep/.uni-input-input {
		text-align: end !important;
	}

	/deep/.uni-input-placeholder {
		text-align: end !important;
	}

	/deep/.u-upload__button {
		margin: 0;
	}

	/deep/.u-cell-group {
		background-color: #fff;
	}
</style>