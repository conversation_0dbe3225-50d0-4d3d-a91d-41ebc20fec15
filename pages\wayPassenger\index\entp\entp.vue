<template>
	<view class="entp">
		<u-navbar :title="carTitle" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="entp_content">
			<div id="container"></div>
			<u-cell-group class="cellgroup" :border='false'>
				<u-cell title="乘车人" :value="psgUser" isLink @click="goPage(0)">
					<u-icon class="iconStyle" slot="icon" size="14"
						name="https://zqcx.di-digo.com/app/image/entp_icon1.png"></u-icon>
				</u-cell>
				<u-cell title="车辆" :value="carNumber" isLink @click="goPage(9)" v-if="pageParams.types==2">
					<u-icon class="iconStyle" slot="icon" size="14"
						name="https://zqcx.di-digo.com/app/image/entp_icon9.png"></u-icon>
				</u-cell>
				<u-cell title="用车制度" :value="regulationName" isLink @click="goPage(1)">
					<u-icon class="iconStyle" slot="icon" size="14"
						name="https://zqcx.di-digo.com/app/image/entp_icon2.png"></u-icon>
				</u-cell>
				<u-cell title="成本中心" :value="costCenterOrgName" isLink :border='false' @click="goPage(2)">
					<u-icon class="iconStyle" slot="icon" size="14"
						name="https://zqcx.di-digo.com/app/image/entp_icon3.png"></u-icon>
				</u-cell>
			</u-cell-group>

			<u-cell-group class="cellgroup" :border='false'>
				<u-cell :title="reserveStartTimeDay?reserveStartTimeDay+'  -  '+reserveEndTimeDay:'用车时间'" isLink
					@click="showDatetimeStart = true">
					<u-icon class="iconStyle" slot="icon" size="14"
						name="https://zqcx.di-digo.com/app/image/entp_icon4.png"></u-icon>
				</u-cell>
				<u-cell @click="goPage(3)">
					<u-icon class="iconStyle" slot="icon" size="12"
						name="https://zqcx.di-digo.com/app/image/entp_icon5.png"></u-icon>
					<view class="posspanBox" slot="title">从<span class="posspan">{{fromAddrName}}</span>出发</view>
				</u-cell>
				<u-cell v-for="(item,index) in channeList" :key="index">
					<u-icon class="iconStyle" slot="icon" size="12"
						name="https://zqcx.di-digo.com/app/image/entp_icon6_1.png"></u-icon>
					<view class="posspanBox" slot="title" @click="goPage(8,index)">
						{{item.siteAddrName?`途径点${index+1} `: `请选择途径点 ${index+1} `}}
						<span class="posspans">
							{{item.siteAddrName}}</span>
					</view>
					<u-icon slot="right-icon" size="14" name="close" @click="delBtn(item,index)"></u-icon>
				</u-cell>

				<u-cell isLink :border='false'>
					<u-icon class="iconStyle" slot="icon" size="12"
						name="https://zqcx.di-digo.com/app/image/entp_icon6.png"></u-icon>
					<view class="endplace" slot="title" @click.stop="goPage(4)">{{toAddrName?toAddrName:'您要去哪?'}}</view>
					<u-icon slot="right-icon" size="14" name="plus" @click="pushBtn()"></u-icon>
				</u-cell>
			</u-cell-group>

			<u-cell-group class="cellgroup" :border='false'>
				<u-cell title="审批人" :value="checkUserName" isLink @click="goPage(5)" v-if="approverType==1">
					<u-icon class="iconStyle" slot="icon" size="14"
						name="https://zqcx.di-digo.com/app/image/entp_icon1.png"></u-icon>
				</u-cell>
				<u-cell title="用车备注" isLink :border='false' @click="goPage(6)" :value="remark">
					<text slot="value" class="textChan">{{remark}}</text>
					<u-icon class="iconStyle" slot="icon" size="14"
						name="https://zqcx.di-digo.com/app/image/entp_icon7.png"></u-icon>
				</u-cell>
			</u-cell-group>

			<u-cell-group class="cellgroup" :border='false' v-if="pageParams.types==1">
				<u-cell title="乘车人数" :value="psgNums?psgNums+'人':''" isLink @click="goPage(7)">
					<u-icon class="iconStyle" slot="icon" size="14"
						name="https://zqcx.di-digo.com/app/image/entp_icon1.png"></u-icon>
				</u-cell>
				<!-- <u-cell title="是否配司机">
					<u-icon class="iconStyle" slot="icon" size="14" name="https://zqcx.di-digo.com/app/image/entp_icon8.png"></u-icon>
					<u-radio-group slot="right-icon" size='14' v-model="isAllotDriver" placement="row" @change="changedriver">
						<u-radio name="1" label="是" style="margin-right: 16px;"></u-radio>
						<u-radio name="0" label="否"></u-radio>
					</u-radio-group>
				</u-cell> -->
				<u-cell title="选择用车类型" :border='false' isLink @click="tanyclei()">
					<text slot="value" class="textChan" style="text-align: right;">{{carTypeName}}</text>
					<u-icon class="iconStyle" slot="icon" size="14"
						name="https://zqcx.di-digo.com/app/image/entp_icon9.png"></u-icon>
				</u-cell>
			</u-cell-group>

		</view>

		<view class="footer_box">
			<u-button type="primary" color="#346CF2" text="确认" @click="confirmClick"></u-button>
		</view>



		<!-- 时间控件 -->
		<u-datetime-picker :show="showDatetimeStart" v-model="choiceTime" title='开始时间' mode="datetime" :filter="filter"
			:formatter="formatter" closeOnClickOverlay @confirm="confirmStart" @cancel="cancelStart"
			@close="closeStart"></u-datetime-picker>

		<u-datetime-picker :show="showDatetimeEnd" v-model="choiceTime" title='结束时间' mode="datetime" :filter="filter"
			:minDate="choiceTime" :formatter="formatter" closeOnClickOverlay @confirm="confirmEnd" @cancel="cancelEnd"
			@close="closeEnd">
		</u-datetime-picker>

		<!-- 下拉选择控件 -->
		<u-picker :show="selectshow" :columns="availableCarTypeList" keyName="carTypeFullName" closeOnClickOverlay
			@cancel="cancelselect" @confirm="confirmselect" @close="closeselect"></u-picker>

		<!-- 弹出控件 -->
		<u-popup class="popup_box" :show="popupshow" :round="4" mode="center" @close="popupclose"
			:customStyle="styleObjr">
			<view class="pop_tit">
				用车申请确认
			</view>
			<view class="pop_stit">
				{{regulationName}}
			</view>
			<view class="popup_content">
				<u-cell-group class="cellgroup" :border='false'>
					<u-cell :titleStyle="titleStyle" :border='false' title="用车时间"
						:value="reserveStartTimeDay+'  -  '+reserveEndTimeDay">
					</u-cell>
					<u-cell :titleStyle="titleStyle" :border='false' :value="fromAddrName+'  -  '+toAddrName">
						<view class="popupLtit" slot="title">用车路线</view>
						<view slot="value">
							{{`${fromAddrName} → ${channeList.length!=0? channeList.map(v=>v.siteAddrName).join(' → ')+' → ':''}  ${toAddrName}`}}
						</view>
					</u-cell>
					<u-cell :titleStyle="titleStyle" :border='false' title="审批人" :value="checkUserName"
						v-if="approverType==1"></u-cell>
					<u-cell :titleStyle="titleStyle" :border='false' title="用车备注" :value="remark">
						<template slot="value">
							<text style="width: 60%;text-align: right;">{{remark}}</text>
						</template>
					</u-cell>
					<u-cell :titleStyle="titleStyle" :border='false' title="乘车人数" :value="psgNums"></u-cell>
					<u-cell :titleStyle="titleStyle" :border='false' title="用车车型" :value="carTypeName"
						v-if="carTypeName"></u-cell>
				</u-cell-group>
			</view>

			<view class="popbtn_box u-flex u-row-around" style="margin: 10rpx 0;">
				<view class="popbtn">
					<u-button color="#e9ecf7" type="primary" text="取消" @click="popupshow=false"></u-button>
				</view>
				<view class="popbtnR">
					<u-button type="primary" @click="allsubmit()" text="确定提交"></u-button>
				</view>
			</view>
		</u-popup>

		<!-- 弹出控件 选择司机-->
		<u-popup class="popup_box" :show="choiceshow" :round="4" mode="center">
			<view class="pop_tit">
				从乘车人中选择司机
			</view>
			<view class="popup_content">
				<u-radio-group size='14' v-model="driverradio">
					<ul class="extul">
						<li>
							<u-radio name="0" label="李四"></u-radio>
						</li>
						<li>
							<u-radio name="1" label="李四"></u-radio>
						</li>
						<li>
							<u-radio name="2" label="李四"></u-radio>
						</li>
					</ul>
				</u-radio-group>
			</view>

			<view class="popbtn_box u-flex u-row-between">
				<u-button class="popbtn" type="primary" @click="choiceclose()" text="取消"></u-button>
				<u-button class="popbtnR" type="primary" text="确定提交" @click="submit()"></u-button>
			</view>

		</u-popup>
		<!-- 选择器 -->
		<u-picker :show="pickerShow" :columns="pickerList" @confirm="pickerConfirm" keyName="name"></u-picker>

		<!-- 地址选择器 -->
		<getSeat @seatClose='seatClose' v-if="seatShow" :seatIndex='seatIndex' style="top:0;width: 100%;"></getSeat>
	</view>
</template>

<script>
	import {
		regulationinfo,
		applycomporder,
		applyprivateorder
	} from '@/config/api.js';
	import {
		carGetcarbyuserList,
	} from '@/config/consoler.js';
	import getSeat from '@/components/getSeat/getSeat.vue'
	export default {
		components: {},
		data() {
			return {
				styleObjr: {
					width: '85%',
					padding: '0 30rpx'
				},
				userInfo: {},
				pageParams: {},
				showDatetimeStart: false,
				showDatetimeEnd: false,
				popupshow: false,
				selectshow: false,
				choiceshow: false,
				choiceTime: null,
				driverradio: '',
				titleStyle: {
					"color": "#999999"
				},
				// -----------------------乘车人
				psgUser: '',
				psgUserId: '',
				checkListData: [],
				// -----------------------用车制度
				regulationName: '',
				regulationId: '',
				// -----------------------成本中心
				costCenterOrgName: '',
				costCenterOrgId: '',
				// -----------------------用车时间
				reserveStartTime: '',
				reserveStartTimeDay: '',
				reserveEndTime: '',
				reserveEndTimeDay: '',
				// -----------------------出发地点
				fromAddrName: '出发地',
				fromAddrDetail: '',
				fromAreaCode: '',
				fromLat: '',
				fromLng: '',
				// -----------------------途径地
				toAddrName: '',
				toAddrDetail: '',
				toAreaCode: '',
				toLat: '',
				toLng: '',
				// -----------------------里程
				aboutDistance: '',
				// -----------------------时长
				aboutDuration: '',
				// -----------------------审批人
				checkUserName: '',
				checkUserId: '',
				checkUserPhone: '',
				sortr: '',
				psgUserIdr: '',
				// -----------------------用车备注
				remark: '',
				// -----------------------乘车人数
				psgNums: 1,
				tgtUserIds: '',
				checkListNumData: [],
				// -----------------------是否配司机
				isAllotDriver: "1",
				// -----------------------用车类型
				carTypeId: "",
				carTypeName: "",
				availableCarTypeList: [],
				// -----------------------外部联系人数组
				outTgtUserINfos: [],
				channeList: [],
				approverType: null,
				// ----------------------- 用车类型
				carTitle: null,
				// ----------------------- 车辆
				carId: "",
				carNumber: "",
				// 
				carList: [],
				pickerType: 0,
				pickerList: [],
				pickerShow: false,
				// 城市选择器
				seatShow: false,
				seatIndex: null,
			};
		},
		watch: {
			regulationName(val, valr) {
				this.getApprover()
			},
		},
		onLoad(option) {
			this.userInfo = this.$common.getItem('userInfo')
			this.carTitle = option.types == 1 ? '企业公车' : option.types == 2 ? '私车公用' : option.types == 3 ? '网约车' : ''
			this.pageParams = option
			this.regulationName = option.regulationName
			this.regulationId = option.regulationId
			this.psgUser = this.userInfo.name
			this.psgUserId = this.userInfo.userId
			this.choiceTime = Number(new Date())
			if (option.types == 2) {
				this.carListr()
			}
		},
		methods: {
			confirmClick() {
				if (this.pageParams.regulationScene != 5) return this.popupshow = true

				if (!this.regulationName) return uni.$u.toast('请选择用车制度')
				if (!this.costCenterOrgName) return uni.$u.toast('请选择成本中心')
				if (!this.carNumber) return uni.$u.toast('请选择车辆')
				if (!this.psgUser) return uni.$u.toast('请选择乘车人')
				if (!this.reserveEndTimeDay) return uni.$u.toast('请选择用车时间')
				if (!this.toAddrName) return uni.$u.toast('请选择用车路线')
				this.popupshow = true
			},
			seatClose(item) {
				if (item) {
					// 3出发地 8途径地 4目的地
					let locat = item.location.split(',')
					if (typeof item.idx == 'string') {
						let v = item.idx.split(',')
						item.idx = Number(v[0])
						item.idxr = Number(v[1])
					}
					if (item.idx == 3) {
						this.$set(this, 'fromAddrName', item.name)
						this.$set(this, 'fromAddrDetail', `${item.cityname}${item.address}`)
						this.$set(this, 'fromAreaCode', item.cityCode)
						this.$set(this, 'fromLat', locat[1])
						this.$set(this, 'fromLng', locat[0])
						this.getFalse()
					} else if (item.idx == 4) {
						this.$set(this, 'toAddrName', item.name)
						this.$set(this, 'toAddrDetail', `${item.cityname}${item.address}`)
						this.$set(this, 'toAreaCode', item.cityCode)
						this.$set(this, 'toLat', locat[1])
						this.$set(this, 'toLng', locat[0])
						this.getFalse()
					} else if (item.idx == 8) {
						let objr = {
							siteAddrName: item.name,
							siteAddrDetail: item.address,
							siteAreaCode: item.cityCode,
							siteLat: locat[1],
							siteLng: locat[0],
						}
						this.channeList.splice(item.idxr, 1, objr)
						this.getFalse()
					}
				} else {
					this.getFalse()
				}


			},
			getFalse() {
				this.$set(this, 'seatShow', false)
				this.$set(this, 'seatIndex', null)
			},
			pickerConfirm(val) {
				let valr = val.value[0]
				if (this.pickerType == 9) {
					this.carId = valr.carId
					this.carNumber = valr.carNumber
				}
				this.pickerShow = false
			},
			closeStart() {
				this.showDatetimeStart = false
			},
			cancelStart() {
				this.showDatetimeStart = false
			},
			confirmStart(e) {
				this.showDatetimeStart = false
				this.reserveStartTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
				this.reserveStartTimeDay = uni.$u.timeFormat(e.value, 'mm月dd日 hh:MM')
				this.showDatetimeEnd = true
			},
			closeEnd() {
				this.showDatetimeEnd = false
			},
			cancelEnd() {
				this.showDatetimeEnd = false
			},
			confirmEnd(e) {
				this.showDatetimeEnd = false
				this.reserveEndTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
				this.reserveEndTimeDay = uni.$u.timeFormat(e.value, 'mm月dd日 hh:MM')
			},
			closeselect() {
				this.selectshow = false
			},
			cancelselect() {
				this.selectshow = false
			},
			confirmselect(e) {
				this.selectshow = false
				this.carTypeId = e.value[0].carTypeId
				this.carTypeName = e.value[0].carTypeFullName
			},
			popupclose() {
				this.popupshow = false
			},
			choiceclose() {
				this.choiceshow = false
			},
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				if (type === 'hour') {
					return `${value}时`
				}
				if (type === 'minute') {
					return `${value}分`
				}
				return value
			},
			filter(mode, options) {
				if (mode === 'year') {
					return options.filter((option) => option == new Date().getFullYear());
				}
				return options;
			},
			delBtn(item, idx) {
				let that = this
				if (!item.siteAddrName) {
					that.channeList.splice(idx, 1)
				} else {
					uni.showModal({
						title: '提示',
						content: `确认删除途径点 "${item.siteAddrName}" 吗？`,
						success: function(res) {
							if (res.confirm) {
								that.channeList.splice(idx, 1)
							} else if (res.cancel) {}
						}
					});
				}
			},
			pushBtn() {
				this.channeList.push({
					"siteLng": "",
					"siteLat": "",
					"siteAddrName": "",
					"siteAddrDetail": "",
					"siteAreaCode": ""
				})
			},
			goPage(type, idx) {
				if (type == 0 && this.pageParams.types == 2) return uni.$u.toast('私车公用禁止修改乘车人')
				if (type == 0) {
					uni.$u.route('/pages/wayPassenger/index/choiceRide/choiceRide', {
						checkListData: JSON.stringify(this.checkListData)
					});
				} else if (type == 1) {
					uni.$u.route('/pages/wayPassenger/index/system/system', {
						regulationScene: this.pageParams.regulationScene,
						userId: this.psgUserId
					});
				} else if (type == 2) {
					if (this.regulationId) {
						uni.$u.route('/pages/wayPassenger/index/cost/cost', {
							regulationId: this.regulationId,
							userId: this.psgUserId
						});
					} else {
						uni.$u.toast('请先选择用车制度')
					}

				} else if (type == 3) {
					this.seatIndex = type
					this.seatShow = true
					return
					uni.$u.route('/pages/map/map', {
						url: "/hybrid/html/postion.html",
						title: '出发地',
						toAddrName: this.toAddrName,
						toAddrDetail: this.toAddrDetail,
						toAreaCode: this.toAreaCode,
						toLat: this.toLat,
						toLng: this.toLng
					});
				} else if (type == 4) {
					this.seatIndex = type
					this.seatShow = true
					return
					uni.$u.route('/pages/map/map', {
						url: "/hybrid/html/postionend.html",
						title: '目的地',
						fromAddrName: this.fromAddrName,
						fromAddrDetail: this.fromAddrDetail,
						fromAreaCode: this.fromAreaCode,
						fromLat: this.fromLat,
						fromLng: this.fromLng
					});
				} else if (type == 5) {
					if (this.regulationName) {
						uni.$u.route('/pages/wayPassenger/index/approver/approver', {
							regulationId: this.regulationId,
							psgUserIdr: this.psgUserId
						});
					} else {
						uni.$u.toast('请先选择用车制度')
					}
				} else if (type == 6) {
					uni.$u.route('/pages/wayPassenger/index/remarks/remarks', {
						remark: this.remark ? this.remark : null
					});
				} else if (type == 7) {
					uni.$u.route('/pages/wayPassenger/index/peer/peer', {
						checkListNumData: JSON.stringify(this.checkListNumData),
						outTgtUserINfos: JSON.stringify(this.outTgtUserINfos),
						psgNums: this.psgNums,
					});
				} else if (type == 8) {
					this.seatIndex = type + `,${idx}`
					this.seatShow = true
					return
					uni.$u.route('/pages/map/map', {
						url: "/hybrid/html/postion.html",
						title: '途经点',
						name: 'channel',
						idxr: idx,
					});
				} else if (type == 9) {
					console.log(this.carList, 'this.carList');
					if (this.carList.length == 0) return uni.$u.toast('请先新增车辆')
					this.pickerType = type
					this.pickerList = [this.carList]
					this.pickerShow = true
				}

			},
			changedriver(name) {
				if (name == 0) {
					// 是否配司机弹框
					// this.choiceshow=true
				}
			},
			allsubmit() {
				let params = {
					// -----------------------乘车人
					psgUser: this.isAllotDriver,
					psgUserId: this.psgUserId,
					// -----------------------用车制度
					regulationName: this.regulationName,
					regulationId: this.regulationId,
					// -----------------------成本中心
					costCenterOrgName: this.costCenterOrgName,
					costCenterOrgId: this.costCenterOrgId,
					// -----------------------用车时间
					reserveStartTime: this.reserveStartTime,
					reserveEndTime: this.reserveEndTime,
					// -----------------------出发地点
					fromAddrName: this.fromAddrName,
					fromAddrDetail: this.fromAddrDetail,
					fromAreaCode: this.fromAreaCode,
					fromLat: this.fromLat,
					fromLng: this.fromLng,
					// -----------------------途径地
					toAddrName: this.toAddrName,
					toAddrDetail: this.toAddrDetail,
					toAreaCode: this.toAreaCode,
					toLat: this.toLat,
					toLng: this.toLng,
					// -----------------------里程
					aboutDistance: this.aboutDistance,
					// -----------------------时长
					aboutDuration: this.aboutDuration,
					// -----------------------审批人 
					checkUserName: this.checkUserName,
					checkUserId: this.checkUserId,
					checkUserPhone: this.checkUserPhone,
					sort: this.sortr,
					psgUserId: this.psgUserId,
					// -----------------------用车备注
					description: this.remark,
					// -----------------------乘车人数
					psgNums: this.psgNums,
					tgtUserIds: this.tgtUserIds,
					// -----------------------是否配司机
					isAllotDriver: this.isAllotDriver,
					// -----------------------用车类型
					carTypeId: this.carTypeId,
					// -----------------------外部联系人数组
					outTgtUserINfos: this.outTgtUserINfos,
					// -----------------------途径点
					throughAddrInfo: this.channeList.length == 0 ? null : JSON.stringify(this.channeList),
					// -----------------------车辆
					carId: this.carId,
					carNumber: this.carNumber
				}

				if (this.pageParams.types == 1) {
					applycomporder(params).then((data) => {
						this.popupshow = false;
						this.getRouter(data)
					})
				} else {
					params.applyType = 2
					applyprivateorder(params).then((data) => {
						this.popupshow = false;
						this.getRouter(data)
					})
				}


			},
			getRouter(data) {
				uni.$u.route('/pager/result/result', {
					psgUser: this.isAllotDriver,
					checkUserName: this.checkUserName,
					checkUserPhone: this.checkUserPhone,
					applyCode: data.applyCode,
					orderEventHis: JSON.stringify(data.orderEventHis),
					regulationScene: this.pageParams.regulationScene
				});
			},
			tanyclei() {
				if (this.regulationId) {
					regulationinfo({
						params: {
							reguid: this.regulationId
						}
					}).then((data) => {
						this.availableCarTypeList = [data.availableCarTypeList]
						this.selectshow = true
					})
				} else {
					uni.$u.toast('请先选择用车制度')
				}
			},

			getApprover() {
				regulationinfo({
					params: {
						reguid: this.regulationId,
						psgUserId: this.psgUserId,
					}
				}).then((data) => {
					this.approverType = data.settingProcess
				})
			},
			carListr() {
				carGetcarbyuserList().then(v => {
					let attr = v.filter(res => {
						return res.selfCarState == 20 
					})
					attr.forEach(v => {
						v.name = v.carNumber
					})
					this.carList = attr
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.entp {
		padding-bottom: 200rpx;

		.entp_content {
			/deep/ .u-cell__body {
				padding: 32rpx 30rpx;
			}

			/deep/ .u-cell {
				// margin: 0 20rpx;
			}

			/deep/ .u-line {
				border-color: #E9ECF7 !important;
			}

			.cellgroup {
				background-color: #fff;
				/* #ifdef H5 */
				margin-top: 26rpx;
				/* #endif */

				.posspanBox {
					font-weight: bold;
				}

				.posspan {
					color: #FF7031;
				}

				.posspans {
					color: #5ac725;
				}

				.endplace {
					// font-size: 36rpx;
					font-weight: bold;
				}


			}
		}

		.popup_box {
			/deep/.u-popup__content {
				width: 84%;
				padding: 0 30rpx;
			}
		}



		.pop_tit {
			font-size: 36rpx;
			text-align: center;
			padding: 30rpx 0;
			border-bottom: 1px solid #E9ECF7;
		}

		.pop_stit {
			font-size: 32rpx;
			font-weight: bold;
			padding: 30rpx 0 10rpx 0;
		}

		.popup_content {
			/deep/.u-cell__body {
				padding: 5px 0;
			}

			.popupLtit {
				width: 100px;
				color: rgb(153, 153, 153);
			}
		}

		.popbtn {
			color: #666;
			background-color: #E9ECF7;
			border-color: #E9ECF7;
			width: 240rpx;
		}

		.popbtnR {
			background-color: #346CF2;
			border-color: #346CF2;
			width: 240rpx;
		}

		.popbtn_box {
			padding: 20rpx 0 25rpx 0;
		}

		.extul {
			margin: 0;
			padding: 0;
			width: 100%;
			font-size: 28rpx;

			li {
				list-style: none;
				margin: 32rpx 0;
			}
		}

		.iconStyle {
			margin-right: 20rpx
		}

		/deep/.u-cell-group__wrapper {
			background-color: #fff;
			/* #ifdef MP-WEIXIN */
			margin-top: 26rpx;
			/* #endif */
		}
	}

	.textChan {
		width: 60%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		text-align: right
	}
</style>