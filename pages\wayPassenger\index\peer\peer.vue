<template>
	<view class="peer">
		<u-navbar title="选择同行人" :autoBack="true" :placeholder="true"></u-navbar>
		<view style="padding-bottom: 120rpx;">
			<view class="u-flex u-row-between psgNumsBox">
				<view class="peerTitNum">乘车人数</view>
				<u-number-box v-model="psgNums"></u-number-box>
			</view>
			<view class="peerTit">
				选择同行人
			</view>
			<xiaolu-tree :checkList="checkList" keyValue="id" v-if="tree.length>0" :props="props" @sendValue="confirm"
				:isCheck="true" :treeNone="tree"></xiaolu-tree>
			<view class="ride_tit u-flex u-row-between">
				<span class='u-flex'>
					外部联系人
					<u-icon v-if="changeSwShow" class="addicon" name="plus" color="#2979ff" bold @click="openPop()">
					</u-icon>
				</span>
				<u-switch v-model="isexternal" size="20" activeColor="#0EC358" @change="changeSw"></u-switch>
			</view>

			<!-- 外部联系人列表 -->
			<ul class="extul" v-for="(item,index) in outTgtUserINfos" :key="index">
				<li class="u-flex u-row-between">
					<span>{{item.name}}</span>
					<span class='u-flex'>{{item.mobile}}
						<u-icon name="trash" @click='delBtn(item,index)'></u-icon>
					</span>
				</li>
			</ul>
		</view>


		<!-- 弹出控件 -->
		<u-popup class="popup_box" :show="popupshow" :round="4" mode="center" @close="popupclose">
			<view class="pop_tit">
				添加外部联系人
			</view>
			<view class="popup_content">
				<u--form labelPosition="left" labelWidth="0" :model="popupmodel" :rules="rules" ref="uForm">
					<u-form-item prop="name" borderBottom>
						<u--input placeholder="输入姓名" prefixIcon="account"
							prefixIconStyle="font-size: 22px;color: #909399" v-model="popupmodel.name" border="none">
						</u--input>
					</u-form-item>
					<u-form-item prop="mobile" borderBottom>
						<u--input placeholder="输入电话" prefixIcon="phone" prefixIconStyle="font-size: 22px;color: #909399"
							v-model="popupmodel.mobile" border="none" type="number"></u--input>
					</u-form-item>
				</u--form>
			</view>

			<!-- <view class="popbtn_box u-flex u-row-between"> -->
			<view class="u-flex u-row-around popbtn_box">
				<view class="popbtn">
					<u-button color='#ccc' type="primary" @click="popupclose()" text="取消"></u-button>
				</view>
				<view class="popbtnR">
					<u-button type="primary" @click="submit()" text="确定"></u-button>
				</view>
			</view>

		</u-popup>

	</view>
</template>

<script>
	import XiaoluTree from '@/components/xiaolu-tree/tree.vue';
	import {
		treeListorg
	} from '@/config/api.js';
	export default {
		components: {
			XiaoluTree
		},
		data() {
			return {
				pageParams: {},
				radiosj: 0,
				checkList: [],
				isexternal: false,
				popupshow: false,
				changeSwShow: false,
				popupmodel: {
					name: '',
					mobile: '',
				},
				rules: {
					name: {
						type: 'string',
						required: true,
						message: '请填写姓名',
						trigger: ['blur', 'change']
					},
					mobile: [{
							required: true,
							message: '请输入手机号',
							trigger: ['change', 'blur'],
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// uni.$u.test.mobile()就是返回true或者false的
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change', 'blur'],
						}
					]

				},
				props: { //单选模式(任意一项)
					label: 'name',
					children: 'children',
					multiple: true, //值为true时为多选，为false时是单选
					checkStrictly: false, //需要在多选模式下才传该值，checkStrictly为false时，可让父子节点取消关联，选择任意一级选项。为true时关联子级，可以全选
					nodes: true //在单选模式下，nodes为false时，可以选择任意一级选项，nodes为true时，只能选择叶子节点
				},
				tree: [
					//id，user字段必须，user'字段名不可更改，id的字段明可以通过传keyValue指定，user为true时说明没有子级，user为false时说明有子级（即children的长度不为0）
				],
				// 外部联系人数组
				outTgtUserINfos: [],
				// 乘车人数
				psgNums: 1
			};
		},
		onReady() {
			// 如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则
			this.$refs.uForm.setRules(this.rules)
		},
		onLoad(option) {
			this.pageParams = option
			console.log(this.pageParams);
			this.psgNums = option.psgNums ? option.psgNums : 1
			this.inittree()
			if (this.pageParams.outTgtUserINfos) {
				this.outTgtUserINfos = JSON.parse(this.pageParams.outTgtUserINfos)
			}
		},
		methods: {
			delBtn(item, idx) {
				let that = this
				uni.showModal({
					content: `是否删除 ${item.name} ？`,
					success: () => {
						that.outTgtUserINfos.splice(idx, 1)
					}
				})
			},
			openPop() {
				this.popupmodel = {
						name: '',
						mobile: ''
					},
					this.popupshow = true
			},
			popupclose() {
				this.popupshow = false
			},
			submit() {
				this.$refs.uForm.validate().then(res => {
					this.popupshow = false
					this.outTgtUserINfos.push(this.popupmodel)
				}).catch(errors => {
					uni.$u.toast('校验失败')
				})
			},
			inittree() {
				treeListorg({}).then((data) => {
					this.recursionList(data)
					this.tree = data
					let checkListNumData = JSON.parse(this.pageParams.checkListNumData)
					if (checkListNumData && checkListNumData.length > 0) {
						this.checkList = checkListNumData
					}
				})
			},
			recursionList(data) {
				data.map((item) => {
					if (item.children && item.children.length > 0) {
						item.user = false
						this.recursionList(item.children)
					} else {
						item.user = true
					}
				})
			},
			confirm(data) {
				let choiceNum = data.length + this.outTgtUserINfos.length + 1
				console.log(choiceNum)
				if (choiceNum <= this.psgNums) {
					let pages = getCurrentPages(); //获取跳转的所有页面
					let nowPage = pages[pages.length - 1]; //当前页
					let prevPage = pages[pages.length - 2]; //上一页
					let tgtUserIds = '';
					data.map((item) => {
						tgtUserIds += item.id + ','
					})
					console.log(tgtUserIds)
					prevPage.$vm.psgNums = this.psgNums
					prevPage.$vm.tgtUserIds = tgtUserIds
					prevPage.$vm.checkListNumData = data
					prevPage.$vm.outTgtUserINfos = this.outTgtUserINfos

					uni.navigateBack({
						delta: 1
					});
				} else {
					uni.$u.toast('同行人数与乘车人数之和不得超过所填乘车人数')
				}
			},
			changeSw(val) {
				this.changeSwShow = val
				if (!val) {
					this.outTgtUserINfos = []
				}
			}
		}
	}
</script>

<style lang="scss">
	.peer {
		height: calc(100vh - 120rpx);

		.psgNumsBox {
			margin-top: 20rpx;
			padding: 0 32rpx;
		}

		.peerTit {
			padding: 0 32rpx;
			font-size: 28rpx;
			font-weight: bold;
			margin-top: 20rpx;
		}

		.peerTitNum {
			font-size: 28rpx;
			font-weight: bold;
		}

		.ride_tit {
			background-color: #E9ECF7;
			padding: 18rpx 32rpx;
			// padding-bottom: 210rpx;
		}

		.addicon {
			display: inline-block;
		}

		.extul {
			width: 100%;
			margin: 0;
			padding: 0;
			font-size: 28rpx;

			li {
				padding: 20rpx 32rpx;
			}
		}

		/deep/.u-popup__content {
			width: 80%;
			padding: 0 30rpx;
		}

		.popup_box {




			/deep/.u-cell__body {
				padding: 5px 0;
			}

			.popup_content {


				.popupLtit {
					width: 100px;
					color: rgb(153, 153, 153);
				}
			}





		}
	}

	.pop_tit {
		font-size: 36rpx;
		text-align: center;
		// padding: 30rpx 0;
		margin: 40rpx 0;
	}

	.popbtn_box {
		/* #ifdef MP-WEIXIN */
		margin: 40rpx 0;
		/* #endif */
		/* #ifdef H5 */
		padding: 40rpx 0 50rpx 0;
		/* #endif */

		.popbtn {
			color: #666;
			background-color: #E9ECF7;
			border-color: #E9ECF7;
			width: 240rpx;
		}

		.popbtnR {
			background-color: #346CF2;
			border-color: #346CF2;
			width: 240rpx;
		}
	}
</style>
