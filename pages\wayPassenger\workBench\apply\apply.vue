<template>
	<view class="apply">
		<u-navbar :title="titles" :autoBack="true" :placeholder="true" @leftClick="leftClick"></u-navbar>
		<view class="nav_box">
			<u-tabs :list="navList" :scrollable="false" lineWidth="22" lineHeight="2" lineColor="#346CF2"
				:activeStyle="{color: '#262626'}" :inactiveStyle="{color: '#999999'}" @change="changeTab"
				itemStyle="width:25%; height: 40px; padding:0">
			</u-tabs>
		</view>
		<!-- <view class="tab_top u-flex u-row-between">
			<view class="tab_top_btn active">
				自有车
			</view>
			<view class="tab_top_btn">
				网约车
			</view>
		</view> -->
		<tab-page :tabtype="tabtype" :applyType='applyType'></tab-page>
	</view>
</template>

<script>
	import tabPage from "./tabpage/tabpage"
	export default {
		components: {
			tabPage
		},
		data() {
			return {
				tabtype: 1,
				navList: [{
					name: '待审批',
					type: 1,
					// badge: {
					// 	isDot: true
					// }
				}, {
					name: '已通过',
					type: 2,
				}, {
					name: '已驳回',
					type: 3,
				}, {
					name: '已取消',
					type: 4,
				}],
				applyType: null,
				titles: '我的申请'
			};

		},
		onLoad(option) {
			this.applyType = option.typer
			this.titles = option.typer == 1 ? '企业用车申请' : option.typer == 2 ? '私车公用申请' : option.typer == 3 ? '网约车申请' :
				'我的申请'
		},
		methods: {
			leftClick() {
				// #ifdef H5
				const pages = getCurrentPages()
				let page = pages[pages.length - 1].$page.fullPath; //完整路由地址
				let typer = page.split('type=')[1] //携带的type参数

				if (typer) {
					uni.reLaunch({
						url: '/pages/wayPassenger/index/index'
					})
				}
				// #endif
			},
			changeTab(item) {
				this.tabtype = item.type
			}
		}
	}
</script>

<style lang="scss" scoped>
	.apply {
		.nav_box {
			background-color: #fff;
			margin-bottom: 20rpx;
		}

		.tab_top {
			width: 360rpx;
			margin: 23rpx auto;

			.tab_top_btn {
				width: 133rpx;
				height: 53rpx;
				line-height: 53rpx;
				text-align: center;
				border-radius: 30rpx;
				font-size: 28rpx;
			}

			.tab_top_btn.active {
				background-color: #346CF2;
				color: #fff;
			}
		}
	}
</style>