<template>
	<view class="index">
		<u-navbar title="正其出行" :placeholder="true">
			<view slot="left"></view>
			<!-- #ifdef H5 -->
			<view slot="right" v-if="userInfo.appTerminal=='1,2'">
				<!-- #endif -->
				<!-- #ifdef MP-WEIXIN -->
				<view slot="left" v-if="userInfo.appTerminal=='1,2'">
					<!-- #endif -->
					<view class="nav_right u-center u-flex-direction" @click="openShow">
						<u-icon class="nav_right_icon" name="https://zqcx.di-digo.com/app/image/change.png"
							size="20">
						</u-icon>
						<view class="nav_right_txt">
							司机入口
						</view>
					</view>
				</view>
			</view>
		</u-navbar>

		<view class="banner">
			<u-swiper :list="bannerList" :height="180" :radius="6" indicator indicatorMode="line" circular>
			</u-swiper>
		</view>

		<view class="index_box" v-if="officialCarsRegulation.length!=0">
			<view class="index_stit">
				企业公车
			</view>
			<view class="index_sbox u-flex u-flex-wrap u-row-between">
				<view v-for="(item,index) in officialCarsRegulation" :key="index" class="index_ssbox u-flex"
					@click="goEntp(item,1)">
					<view class="tap">
						{{item.bindType==1 ? "个人" : "部门"}}
					</view>
					<view class="u-flex-3">
						<u-image :width="36" :height="36" shape="circle" :lazyLoad="true"
							src="https://zqcx.di-digo.com/app/image/index_icon1.png"></u-image>
					</view>
					<view class="u-flex-9 hegreName">
						{{item.regulationName}}
					</view>
				</view>
			</view>
		</view>




		<view class="index_box" v-if="privateCarsRegulation.length!=0">
			<view class="index_stit">
				私车公用
			</view>
			<view class="index_sbox u-flex u-flex-wrap u-row-between">
				<view v-for="(item,index) in privateCarsRegulation" :key="index" class="index_ssbox u-flex"
					@click="goEntp(item,2)">
					<view class="tap">
						{{item.bindType==1 ? "个人" : "部门"}}
					</view>
					<view class="u-flex-3">
						<u-image :width="36" :height="36" shape="circle" :lazyLoad="true"
							src="https://zqcx.di-digo.com/app/image/index_icon2.png"></u-image>
					</view>
					<view class="u-flex-9 hegreName">
						{{item.regulationName}}
						<!-- {{item.regulationSname}} -->
					</view>
				</view>
			</view>
		</view>

		<!-- <view class="index_box">
			<view class="index_stit">
				网约车
			</view>
			<view class="index_sbox u-flex u-flex-wrap u-row-between">
				<view v-for="(item,index) in netCarsRegulation" :key="index" class="index_ssbox u-flex" @click="goEntp(item,3)">
					<view class="tap">
						{{item.bindType==1 ? "个人" : "部门"}}
					</view>
					<view class="u-flex-3">
						<u-image :lazyLoad="true" :width="36" :height="36" shape="circle" src="https://zqcx.di-digo.com/app/image/index_icon6.png"></u-image>
					</view>
					<view class="u-flex-9 hegreName">
						{{item.regulationName}}
					</view>
				</view>
				
			</view>
		</view> -->
		<u-empty v-if="officialCarsRegulation.length==0" text="请联系管理添加功能权限!"
			icon="http://cdn.uviewui.com/uview/empty/order.png" style='margin-top: 60rpx;'>
		</u-empty>

		<!-- 组件 -->
		<u-popup class="popup_bg" :round="5" :show="show" mode="center" @close="close">
			<view class="popup_tit">
				确定切换
			</view>
			<view class="popup_txt">
				是否确定切换至司机端？
			</view>
			<view class="u-flex u-row-between popup_btn_box">
				<u-button class="popup_btn" color="#346CF2" type="primary" text="确定" @click="adoptFun()"></u-button>
				<u-button class="popup_btn two" color="#E9ECF7" type="primary" text="取消" @click="close()"></u-button>
			</view>
		</u-popup>

	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from 'vuex';

	import {
		regulationList,
		appcacherole
	} from '@/config/api.js';

	export default {
		data() {
			return {
				show: false,
				bannerList: [
					'https://zqcx.di-digo.com/app/image/luobo.jpg',
					'https://zqcx.di-digo.com/app/image/lunbo_1.png',
				],
				officialCarsRegulation: [],
				privateCarsRegulation: [],
				netCarsRegulation: [],
				userInfo: {}
			}
		},
		onShow() {
			this.userInfo = this.$common.getItem('userInfo')
			this.bindLogin()
		},

		computed: mapState(['hasLogin', 'uerInfo']),
		methods: {
			...mapMutations(['logout']),
			openShow() {
				let that = this
				uni.showModal({
					content: '是否确定切换至司机端',
					success: (res) => {
						if (res.confirm) {
							that.adoptFun()
						}
					}
				})
			},
			bindLogin() {
				if (this.$common.getItem('userInfo')) {
					// 两种角色的按缓存角色跳转
					if (this.$common.getItem('userInfo').appTerminal == '1,2') {
						if (this.$common.getItem('userInfo').cacheRoleType == '2') {
							uni.$u.route('/pages/driver/index/index');
						} else if (this.$common.getItem('userInfo').cacheRoleType == '1') {
							this.initBadge()
						} else {
							uni.$u.route('/pages/login/selectRole/selectRole');
						}
					} else {
						// 一种角色的按角色跳转
						if (this.$common.getItem('userInfo').appTerminal == '2') {
							uni.$u.route('/pages/driver/index/index');
						} else {
							this.initBadge()
						}
					}

				} else {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}
			},
			initBadge() {
				if (true) {
					uni.showTabBar()
					// uni.setTabBarBadge({
					// 	index: 3,
					// 	text: '99'
					// })
					// uni.showTabBarRedDot({
					// 	index: 4
					// })
				} else {
					// uni.hideTabBar()
					uni.removeTabBarBadge({
						index: 3
					})
					uni.hideTabBarRedDot({
						index: 4
					})
				}
				regulationList({}).then(data => {
					this.officialCarsRegulation = data.officialCarsRegulation
					this.privateCarsRegulation = data.privateCarsRegulation
					this.netCarsRegulation = data.netCarsRegulation
				})

			},
			goEntp(item, t) {
				// 1  企业公车 2私车公用
				item.typer = t
				uni.$u.route({
					url: '/pages/wayPassenger/index/entp/entp',
					params: item
				})
			},
			close() {
				this.show = false
			},
			adoptFun() {
				this.show = false
				appcacherole({
					cacheRoleType: '2'
				}).then(data => {
					this.userInfo.cacheRoleType = '2';
					this.$common.setItem('userInfo', this.userInfo)
					uni.$u.route('/pageDriver/driveIndex/driveIndex');
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.index {
		padding-bottom: 80rpx;

		.nav_right {
			.nav_right_icon {
				/deep/.u-icon__img {
					left: 50%;
					margin-left: -20rpx;
				}
			}

			.nav_right_txt {
				font-size: 24rpx;
			}
		}

		.banner {
			width: 94vw;
			margin: 26rpx auto 0 auto;
		}

		.hegreName {
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			font-weight: 600;
			height: 100rpx;
			display: flex;
			align-items: center;
			margin: 0 12rpx;
			font-size: 29rpx;
			letter-spacing: 1rpx;
		}

		.index_box {
			margin: 0 30rpx;

			.index_stit {
				font-size: 28rpx;
				padding: 36rpx 0 24rpx 0rpx;
				color: #666666;
				font-weight: 600;
			}

			.index_ssbox {
				width: calc(50% - 40rpx);
				padding: 24rpx 16rpx 24rpx 16rpx;
				background-color: #e3f1ff;
				margin-bottom: 20rpx;
				font-size: 30rpx;
				position: relative;
				border-radius: 20rpx;

				.tap {
					position: absolute;
					right: 20rpx;
					bottom: 13rpx;

					padding: 4rpx 8rpx;
					background-color: #FF3131;
					color: #fff;
					font-size: 18rpx;
					border-radius: 10rpx;

				}
			}
		}


		.popup_bg {
			.popupcbox {
				padding: 0 53rpx;
			}

			.upload_box {
				padding: 0 53rpx 40rpx 53rpx;

				.upload_name {
					line-height: 80rpx;
					font-size: 36rpx;
				}
			}

			.select_input {
				height: 60rpx;
				border: 1px solid #CCCCCC;
				border-radius: 8rpx;
				padding-left: 20rpx;
				line-height: 60rpx;
				color: #999;
				margin: 20rpx 0;
			}

			.selectbox {
				padding: 0 53rpx;
				font-size: 28rpx;
			}

			.popup_btn_box {
				padding-bottom: 27rpx;
			}

			.popup_tit {
				font-size: 36rpx;
				text-align: center;
				padding: 40rpx 0 30rpx 0;
			}

			.popup_tit_t {
				padding: 40rpx 53rpx 30rpx 53rpx;
				font-size: 36rpx;
			}

			.popup_txt {
				font-size: 28rpx;
				text-align: center;
				margin-bottom: 40rpx;
			}

			/deep/.u-popup__content {
				width: 84%;
			}

			.popup_btn {
				width: 230rpx;
				height: 80rpx;

				/deep/ .u-button__text {
					font-size: 36rpx !important;
				}
			}

			.two {
				color: #666666 !important;
			}
		}


	}
</style>
