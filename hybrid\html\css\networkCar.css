.amap-logo,
.amap-copyright {
	display: none !important;
}


#app {
	position: relative;
}

.mapLeft,
.mapRight {
	position: absolute;
	background: #fff;
	z-index: 999;
	top: 20rem;
	padding: 10rem;
	box-shadow: 0rem 0rem 3rem #ccc;
}

.mapLeft {
	left: 15rem;
	border-radius: 50%;
	width: 20rem;
	height: 20rem;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 5rem;
}

.mapRight {
	right: 15rem;
	border-radius: 10rem;
	font-size: 12rem;
	padding: 5rem 13rem;
	display: flex;
	justify-content: center;
	align-items: center;
}

.imgs {
	width: 14rem;
	height: 14rem;
}

.addrrs {
	position: absolute;
	z-index: 999;
	left: calc(50% - 80rem);
	top: calc(50% - 40rem);
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.addrrs-text {
	width: 160rem;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	font-size: 12rem;
	text-align: center;
	font-weight: bold;
	/* top: 42% !important; */
}