<template>
	<view>
		<view class="filterBox u-flex">
			<view class="filter_name u-flex" @click="typeClick" v-show="tabtype==2">
				<span style="margin-right: 10rpx;">{{searWash(stateVal)}}</span>
				<u-icon name="arrow-down" size='12'></u-icon>
			</view>
			<u-search class="search" placeholder="搜索订单" shape='square' :showAction="false" v-model="keyword"
				@search="searchBtn">
			</u-search>
		</view>
		<scroll-view class="scroll-content" scroll-y="true" refresher-enabled="true" :refresher-triggered="triggered"
			:refresher-threshold="100" refresher-background="#F9FAFE" @scrolltolower="onReflowe"
			@refresherrefresh="onRefresh">
			<view class="app_list" v-for="(item,index) in dataList" :key="index">
				<view class="jiao_img">
					<u-image v-if="item.dispatchButton.isShowTagLease==0" :width="30" :height="30"
						src="https://zqcx.di-digo.com/app/image/qi.png"></u-image>
				</view>
				<view class="jiao_img">
					<u-image v-if="item.dispatchButton.isShowTagLease==1" :width="30" :height="30"
						src="https://zqcx.di-digo.com/app/image/zu.png"></u-image>
				</view>
				<view @click="goDetail(item.travelId)">
					<!-- <view class="icon-right">
						<u-icon name="arrow-right"></u-icon>
					</view> -->
					<view class="u-flex u-row-between u-col-top">
						<view class="u-flex">
							<view>
								<!-- <view class="text-dh">申请单号：{{item.applyCode}}</view> -->
								<view class="text-dh" v-if="item.travelCode">行程单号：{{item.travelCode}}
								</view>
							</view>
						</view>
						<view class="text-rt u-flex">
							<view class="text-rt-tag" v-if="item.dispatchButton.isShowTagSplit==1">
								<u-tag text="拆" type="warning" size="mini" plain plainFill></u-tag>
							</view>
							{{item.compOrderState==10?"待审批":item.compOrderState==95?"行后待审批":item.compOrderState==20?"待派车":item.compOrderState==24?"租赁待审批":item.compOrderState==25?"租赁待派车":item.compOrderState==30?"待接单":item.compOrderState==40?"待执行":item.compOrderState==50?"执行中":item.compOrderState==60?"到达出发地":item.compOrderState==70?"进行中":item.compOrderState==80?"到达目的地":item.compOrderState==120?"结算审核中":item.compOrderState==200?"已完成":item.compOrderState==210?"已取消":item.compOrderState==220?"审批驳回":item.compOrderState==230?"调度驳回":item.compOrderState==100?"待确认":item.compOrderState==90?"已回场":item.compOrderState==110?"待结算":item.compOrderState==205?"待评价":"订单异常"}}
						</view>
					</view>
					<!-- 租赁转入单 -->
					<view v-if="item.intoOrOut==2">
						<view class="app_t_line u-flex">
							<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/gongsi.png">
							</u-image>
							<span class="tab_icon">{{item.applyUnitName}}</span>
						</view>
						<view class="app_t_line u-flex">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png"></u-image>
							<span class="tab_icon">{{item.psgName}} &nbsp;&nbsp;&nbsp;|
								&nbsp;&nbsp;&nbsp;{{item.psgNums}}人</span>
						</view>
					</view>
					<!-- 租赁转出单 -->
					<view v-else>
						<view class="app_t_line u-flex">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png"></u-image>
							<span class="tab_icon">{{item.psgName}} &nbsp;&nbsp;&nbsp;|
								&nbsp;&nbsp;&nbsp;{{item.psgNums}}人</span>
						</view>
					</view>
					<view class="app_t_line u-flex">
						<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon.png">
						</u-image>
						<span class="tab_icon">{{getTiemr(item.reserveStartTime)}} -
							{{getTiemr(item.reserveEndTime)}}</span>
					</view>
					<!-- 起始、途径、终点 -->
					<view class="app_addrs">
						<view class="ma u-flex">
							<view class="spot"></view>
							<span style="width: 85%;">{{item.fromAddrName}}</span>
						</view>
						<view class="ma u-flex" v-if="item.throughAddrInfo && item.throughAddrInfo.length > 0">
							<view class="spot" style="background-color: chartreuse;"></view>
							<span style="width: 85%;">{{item.throughAddrInfoName}}</span>
						</view>
						<view class="ma u-flex">
							<view class="spot endspot"></view>
							<span style="width: 85%;">{{item.toAddrName}}</span>
						</view>
					</view>
					<!-- 企业单显示 -->
					<view v-if="item.travelType==1">
						<view class="line-dotted"></view>
						<!-- 已派车 -->
						<view
							v-if="item.compOrderState>=30&&item.compOrderState!=210&&item.compOrderState!=220&&item.compOrderState!=230">
							<!-- 企业单租赁待审批（24） 按租赁单字段进行展示 -->
							<view v-if="item.compOrderState==24">
								<view class="app_t_line u-flex" v-if="item.intoOrOut==1">
									<u-image :width="14" :height="14"
										src="https://zqcx.di-digo.com/app/image/gongsi.png">
									</u-image>
									<span class="tab_icon">{{item.leasingCompName}}</span>
								</view>

								<view class="app_t_line u-flex" v-if="item.carNumber">
									<u-image :width="14" :height="14"
										src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png">
									</u-image>
									<span class="tab_icon">{{item.carNumber}}<span
											v-if='item.numberColor'>({{item.numberColor}})</span></span>
								</view>

								<view class="app_t_line u-flex" v-if="item.driverName">
									<u-image :width="14" :height="14"
										src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon2.png">
									</u-image>
									<span class="tab_icon">{{item.driverName}}</span>
								</view>
							</view>
							<view v-else>
								<view class="app_t_line u-flex" v-if="item.carNumber">
									<u-image :width="14" :height="14"
										src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png">
									</u-image>
									<span class="tab_icon">{{item.carNumber}}<span
											v-if='item.numberColor'>({{item.numberColor}})</span></span>
								</view>

								<view class="app_t_line u-flex" v-if="item.driverName">
									<u-image :width="14" :height="14"
										src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon2.png">
									</u-image>
									<span class="tab_icon">{{item.driverName}}</span>
								</view>
							</view>
						</view>
						<!-- 未派车 -->
						<view v-else>
							<!-- 企业单租赁待审批（24） 按租赁单字段进行展示 -->
							<view v-if="item.compOrderState==24">
								<view class="app_t_line u-flex">
									<u-image :width="14" :height="14"
										src="https://zqcx.di-digo.com/app/image/gongsi.png">
									</u-image>
									<span class="tab_icon">{{item.leasingCompName}}</span>
								</view>
								<view class="app_t_line u-flex dw_box">
									<view class="u-flex u-flex-4">
										<u-image :width="14" :height="14"
											src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png">
										</u-image>
										<span class="tab_icon">{{item.wantCarTypeFullName}}</span>
									</view>
									<view class="u-flex u-flex-7">
										<u-image :width="14" :height="14"
											src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon.png">
										</u-image>
										<span class="tab_icon">{{item.valuationFullName}}</span>
									</view>
									<view class="u-flex u-flex-2">
										<u-image :width="14" :height="14"
											src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png">
										</u-image>
										<span class="tab_icon">{{item.psgNums}}人</span>
									</view>
								</view>
								<view class="app_t_line u-flex" v-if="item.remark">
									<u-image :width="14" :height="14"
										src="https://zqcx.di-digo.com/app/image/wdsq_icon4.png">
									</u-image>
									<span class="tab_icon">{{item.remark}}</span>
								</view>
							</view>
							<view v-else>
								<view class="app_t_line u-flex">
									<u-image :width="14" :height="14"
										src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png">
									</u-image>
									<span class="tab_icon">{{item.wantCarTypeFullName}}</span>
								</view>
								<view class="app_t_line u-flex" v-if="item.description">
									<u-image :width="14" :height="14"
										src="https://zqcx.di-digo.com/app/image/wdsq_icon4.png">
									</u-image>
									<span class="tab_icon">{{item.description}}</span>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="app_t_line u-flex">
					<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/gongsi.png">
					</u-image>
					<span class="tab_icon">租车方式：{{item.rentType==1?'带驾租车':item.rentType==2?'不带驾租车':'-'}}</span>
				</view>
				<!-- 租赁单显示 -->
				<view v-if="item.travelType==2">
					<view class="line-dotted"></view>
					<!-- 已派车 -->
					<view v-if="item.compOrderState>=30">
						<view class="app_t_line u-flex" v-if="item.intoOrOut==1">
							<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/gongsi.png">
							</u-image>
							<span>{{item.leasingCompName}}</span>
						</view>

						<view class="app_t_line u-flex" v-if="item.carNumber">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png"></u-image>
							<span class="tab_icon">{{item.carNumber}}<span
									v-if='item.numberColor'>({{item.numberColor}})</span></span>
						</view>

						<view class="app_t_line u-flex" v-if="item.driverName">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon2.png"></u-image>
							<span class="tab_icon">{{item.driverName}}</span>
						</view>
					</view>
					<!-- 未派车 -->
					<view v-else>
						<view class="app_t_line u-flex" v-if="item.intoOrOut==1">
							<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/gongsi.png">
							</u-image>
							<span class="tab_icon">{{item.leasingCompName}}</span>
						</view>
						<view class="app_t_line u-flex dw_box">
							<view class="u-flex u-flex-4">
								<u-image :width="14" :height="14"
									src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png"></u-image>
								<span class="tab_icon">{{item.wantCarTypeFullName}}</span>
							</view>
							<view class="u-flex u-flex-7">
								<u-image :width="14" :height="14"
									src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon.png"></u-image>
								<span class="tab_icon">{{item.valuationFullName}}</span>
							</view>
							<view class="u-flex u-flex-2">
								<u-image :width="14" :height="14"
									src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png">
								</u-image>
								<span class="tab_icon">{{item.psgNums}}人</span>
							</view>
						</view>
						<view class="app_t_line u-flex" v-if="item.remark">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/wdsq_icon4.png"></u-image>
							<span class="tab_icon">{{item.remark}}</span>
						</view>
					</view>
				</view>
				<!-- 按钮显示-->
				<view class="btn_box u-flex">

					<view class="btnmini" v-if="item.travelType==1&&item.dispatchButton.isShowButtonDisp==1">
						<u-button type="success" size="mini" text="派车" @click="paiche(item,1)"></u-button>
					</view>

					<view class="btnmini" v-if="item.travelType==2&&item.dispatchButton.isShowButtonDisp==1">
						<u-button type="success" size="mini" text="租赁派车" @click="paiche(item,2)"></u-button>
					</view>

					<view class="btnmini" v-if="item.dispatchButton.isShowButtonReDisp==1">
						<u-button type="success" size="mini" text="改派" @click="paiche(item,3)"></u-button>
					</view>

					<view class="btnmini" v-if="item.dispatchButton.isShowButtonDispReject==1">
						<u-button class="hui" color='#ccc' size="mini" text="驳回" @click="operation(item,5)">
						</u-button>
					</view>

					<view class="btnmini" v-if="item.dispatchButton.isShowButtonLeaseReject==1">
						<u-button class="hui" color='#ccc' size="mini" text="退单" @click="operation(item,1)">
						</u-button>
					</view>

					<view class="btnmini" v-if="item.dispatchButton.isShowButtonChangeTeam==1">
						<u-button type="primary" size="mini" text="转车队" @click="operation(item,2)"></u-button>
					</view>

					<view class="btnmini" v-if="item.dispatchButton.isShowButtonToLease==1">
						<u-button type="error" size="mini" text="转租赁" @click="operation(item,3)"></u-button>
					</view>
					<!-- 000000000000000000000000 -->
					<view class="btnmini" v-if="item.dispatchButton.isShowButtonToLeaseApply==1">
						<u-button type="warning" size="mini" text="转租赁审批" @click="zzlsp(item)"></u-button>
					</view>

					<view class="btnmini" v-if="item.dispatchButton.isShowButtonBack==1">
						<u-button type="warning" size="mini" text="撤单" @click="operation(item,4)"></u-button>
					</view>

					<view class="btnmini" v-if="item.dispatchButton.isShowButtonFinish==1">
						<u-button type="primary" size="mini" text="结单" @click="endOne(item)"></u-button>
					</view>
				</view>
			</view>
			<view style="text-align: center;" v-if="total==dataList.length &&dataList.length!=0">———— 到底了 ————</view>
			<u-empty v-if="dataList.length==0" mode="order" text='调度列表为空'
				icon="http://cdn.uviewui.com/uview/empty/order.png">
			</u-empty>
		</scroll-view>

		<!-- 驳回弹窗组件 -->
		<u-popup :round="5" :show="rejectshow" mode="center" @close="rejectshow=false" :customStyle="styleObjr">
			<rejectCom v-if="rejectshow" :rejectObj="formObj" :rejectName="formName" @adoptFun="adoptFun">
			</rejectCom>
		</u-popup>

		<!-- 结单弹窗组件 1-->
		<u-popup :round="5" :show="statemenShow" mode="center" @close="statemenShow=false" :customStyle="styleObjr">
			<statementOne v-if="statemenShow" :stateObj="endParam" @stateOne="receiveOne">
			</statementOne>
		</u-popup>
		<!-- 结单弹窗组件 2-->
		<u-popup :round="5" :show="newShow" mode="center" @close="newShow=false" :customStyle="styleObjr">
			<statementTwo v-if="newShow" :stateObj="newParam" :stateType="false" :oneStateObj="endParam"
				@stateTwo="receiveTwo" @stateTwoChange="receiveTwoChanege">
			</statementTwo>
		</u-popup>

		<u-picker :show="stateShow" :columns="stateList" keyName="name" @confirm='confirm'
			@cancel='typeClick'></u-picker>
	</view>
</template>

<script>
	import {
		dispcomporderlist,
		cartypetreelist,
		dispatchcompreject,
		tempvaluationinfo,
		dispatchcompchangeover
	} from '@/config/api.js';
	import rejectCom from '@/pages/wayPassenger/workBench/componentr/rejectCom.vue'
	import statementOne from '@/pages/wayPassenger/workBench/componentr/statementOne.vue'
	import statementTwo from '@/pages/wayPassenger/workBench/componentr/statementTwo.vue'
	export default {
		components: {
			statementOne,
			statementTwo,
			rejectCom,

		},
		data() {
			return {
				keyword: "",
				pageNum: 1,
				pageSize: 20,
				dataList: [],
				choiceTime: '',
				triggered: false,
				total: 0,
				rejectshow: false,
				styleObjr: {
					width: '85%'
				},
				formObj: {},
				formName: '',
				statemenShow: false,
				endParam: {},
				stateType: "",
				newParam: {},
				newShow: false,
				stateVal: '',
				stateShow: false,
				stateList: [
					[{
						id: '',
						name: '全部状态'
					}, {
						id: 1,
						name: '待接单'
					}, {
						id: 2,
						name: '进行中'
					}, {
						id: 3,
						name: '待确认'
					}, {
						id: 4,
						name: '已完成'
					}, ]
				]
			}
		},
		props: ["tabtype", 'useCarType'],
		watch: {
			tabtype(newVal, oldVal) {
				this.pageNum = 1
				this.stateVal = ''
				this.dataList = []
				this.queryList()
			}
		},
		filters: {
			transDay(time) {
				let d = parseInt(time / 60 / 60 / 24)
				let h = parseInt(time / 60 / 60 % 24)
				h = h < 10 ? '0' + h : h
				let m = parseInt(time / 60 % 60)
				m = m < 10 ? '0' + m : m
				let s = parseInt(time % 60)
				s = s < 10 ? '0' + s : s
				return d + "天" + h + "小时" + m + "分钟"
			}
		},
		onLoad() {
			this.pageNum = 1
			this.stateVal = ''
			this.dataList = []
			// this.queryList()
			this.getTree()
			this.choiceTime = Number(new Date());
		},
		methods: {
			confirm(e) {
				this.stateVal = e.value[0].id
				this.searchBtn()
				this.typeClick()
			},
			typeClick() {
				this.stateShow = !this.stateShow
			},
			searWash(v) {
				return v == 1 ? '待接单' : v == 2 ? '进行中' : v == 3 ? '待确认' : v == 4 ? '已完成' : '全部状态'
			},
			endtFtion(obj) {
				this.$set(obj, 'dayEndMileage', JSON.stringify(this.endParam.dayEndMileage || []))
				this.$set(obj, 'nights', this.endParam.nights || null)
				
				dispatchcompchangeover({
					dispatchOverVo: obj,
					id: obj.travelId,
				}).then((data) => {
					uni.$u.toast('操作成功')
					this.searchBtn()
					this.newShow = false
				})
			},
			receiveTwo(typer, obj, datar) {
				// 1返回 2取消 3 结单 4 修改
				if (typer == 1) {
					this.endParam = datar
					this.newShow = false
					this.statemenShow = true
				}
				if (typer == 2) {
					this.newShow = false
				}
				if (typer == 3) {
					this.endtFtion(obj)
				}

			},
			receiveOne(objr, datar) {
				this.endParam = datar
				if (!objr) return this.statemenShow = false
				this.newParam = objr
				this.newShow = true
				this.statemenShow = false
			},
			endOne(item) {
				this.endParam = {
					actualEndMileage: '',
					actualEndTime: '',
					startMileageImgUrl: '',
					endMileageImgUrl: '',
					actualStartMileage: item.actualStartMileage ? item.actualStartMileage : '',
					actualStartTime: '',
					otherFee: '',
					reduceFee: '',
					travelType: item.travelType,
					carTypeFullName: item.carTypeFullName,
					valuationFullName: item.valuationFullName,
					travelId: item.travelId,
					overtakeTimeType:item.overtakeTimeType
				}
				tempvaluationinfo({
					params: {
						id: item.valuationId,
					}
				}).then((data) => {
					let arrt = JSON.parse(data.otherFee)
					arrt.forEach(v => {
						v.feedatarCode = 200
					})
					this.endParam.otherFee = arrt

					this.$nextTick(() => {
						this.statemenShow = true
					})
				})
			},
			// 跳转详情
			goDetail(id) {
				uni.$u.route('/pager/dispatchDetail/dispatchDetail', {
					id: id
				});
			},
			// 按钮打开
			operation(item, num) {
				this.formObj = item
				if (num == 1) {
					this.formName = '退单'
					this.rejectshow = true
				} else if (num == 5) {
					this.formName = '驳回'
					this.rejectshow = true
				} else if (num == 2) {
					uni.$u.route('/pagec/choiceFleet/choiceFleet', {
						travelId: this.formObj.travelId,
						typer: 'list'
					});
				} else if (num == 3) {
					uni.$u.route('/pagec/newSublease/newSublease', {
						travelId: this.formObj.travelId,
						reserveStartTime: new Date(this.formObj.reserveStartTime).getTime()
					});
				}
			},
			// 驳回请求
			adoptFun(row) {
				if (!row) return this.rejectshow = false
				dispatchcompreject({
					dispatchRejectVo: {
						reason: row.checkRemark
					},
					id: this.formObj.travelId
				}).then((data) => {
					uni.$u.toast('操作成功')
					this.rejectshow = false
					setTimeout(() => {
						this.dataList = []
						this.queryList()
					}, 1000)
				})
			},
			// 派车弹窗
			paiche(item, t) {
				let obj ={
					travelId: item.travelId,
					reserveStartTime: item.reserveStartTime,
					compOrderState: item.compOrderState,
					wantCarTypeId: item.wantCarTypeId,
					carTypeId: item.carTypeId,
					wantCarTypeFullName: item.wantCarTypeFullName,
					carTypeIdName: item.carTypeIdName,
					feeTemplateId: item.feeTemplateId,
					rentType: item.rentType,
					travelType: item.travelType,
				}
				obj.typer = t
				obj.newTyper = 'add'
				uni.$u.route('pager/dispatch/newDispatch/newDispatch', {
					item: JSON.stringify(obj),
				});
			},
			// 下拉刷新
			onRefresh(e) {
				this.pageNum = 1
				this.triggered = true
				this.dataList = []
				this.queryList()
			},
			// 加载更多
			onReflowe(e) {
				if (this.total == this.dataList.length) return
				this.pageNum++
				this.queryList()
			},
			// 查询列表
			queryList() {
				dispcomporderlist({
					params: {
						orderStateType: this.tabtype,
						pageNum: this.pageNum,
						pageSize: this.pageSize,
						content: this.keyword,
						orderType: this.useCarType,
						travelState: this.stateVal ? this.stateVal : ''
					}
				}).then((data) => {
					data.pageList.forEach(v => {
						if (v.throughAddrInfo) {
							v.throughAddrInfo = JSON.parse(v.throughAddrInfo)
							v.throughAddrInfoName = v.throughAddrInfo.map(v => v.siteAddrName)
								.join(
									' → ')
						}
					})
					this.total = data.total
					if (this.pageNum == 1) {
						this.dataList = data.pageList
					} else {
						this.dataList.push(...data.pageList)
					}
					this.triggered = false
				})
			},

			//筛选
			searchBtn() {
				this.pageNum = 1
				this.dataList = []
				this.queryList()
			},
			getTree() {
				cartypetreelist({
					params: {}
				}).then((data) => {
					this.cartypetree = data
					this.carTypecolumns = [data, data[0].children]
				})
			},
			// 拆封
			getTiemr(date) {
				let tier
				let year
				try {
					tier = date.split(' ')[1].split(':')
					year = date.split(' ')[0].split('-')
					return `${year[1]}月${year[2]}日 ${tier[0]}:${tier[1]}`
				} catch (e) {
					return date
				}

			},
		}
	}
</script>

<style lang="scss" scoped>
	.filterBox {
		margin-top: 20rpx;
		padding: 0 32rpx;
	}

	.filter_name {
		font-size: 24rpx;
		padding: 0 20rpx;
		background: #fff;
		margin-right: 20rpx;
		border-radius: 10rpx;
		height: 68rpx;
		line-height: 68rpx;
	}

	.search {
		/deep/.u-search__content {
			background-color: #E9ECF7 !important;
		}

		/deep/.u-search__content__input {
			background-color: #E9ECF7 !important;
		}
	}

	.scroll-content {
		margin-top: 10rpx;
		height: calc(100vh - 310rpx);

		.app_list {
			background-color: #fff;
			margin: 14rpx 11rpx;
			border-radius: 11rpx;
			padding: 30rpx;
			padding-top: 30rpx;
			font-size: 28rpx;
			position: relative;

			.jiao_img {
				position: absolute;
				left: 0;
				top: 0;
			}

			.icon-right {
				position: absolute;
				top: 50%;
				right: 27rpx;
				margin-top: -16rpx;
			}

			.tab_icon {
				margin-left: 16rpx;
			}

			.text-dh {
				font-size: 24rpx;
				color: #999999;
			}

			.text-rt {
				color: #346CF2;

				.text-rt-tag {
					margin-right: 4rpx;

					/deep/.u-tag--mini {
						height: 33rpx;
						line-height: 33rpx;
						padding: 0 4rpx;
					}
				}
			}

			.app_t_line {
				margin-top: 14rpx;
			}

			.dw_box {
				font-size: 24rpx;
			}

			.spot {
				width: 16rpx;
				height: 16rpx;
				background-color: #239EFC;
				border-radius: 50%;
				margin: 0 24rpx 0 8rpx;
			}

			.endspot {
				background-color: #404040;
			}
		}

		.btn_box {
			padding-top: 20rpx;
			border-top: 1px dotted #999999;
			margin-top: 20rpx;

			.btnmini {
				margin: 0 32rpx;

				.hui {
					background-color: #999999;
					border-color: #999999;
					color: #fff;
				}
			}
		}
	}

	.app_addrs {
		background: #ebebeb;
		padding: 10rpx;
		border-radius: 10rpx;
		margin-top: 10rpx;
	}

	.ma {
		margin: 10rpx 0;
	}
</style>