//这里创建一个自定义配置的axios
var axios = axios.create({
	//这里可配置文件的长路径
	// #ifdef MP-WEIXIN
	// baseURL: "https://zqcxdev.di-digo.com/api/zqcx/",
	baseURL: "https://zqcx.di-digo.com/api/zqcx/",
	// #endif
	// #ifdef H5
	baseURL: "/dpc/", // 代理
	// #endif
})

//请求前
axios.interceptors.request.use(function(config) {
	// 可以设置 加载的动画效果 的展示
	//这里指的是请求之前做点什么
	console.log(config, '-----')
	config.data = config.data || {}

	if (config.method == 'GET') {
		config.headers['Content-Type'] = 'application/x-www-form-urlencoded'

	} else {
		config.headers['Content-Type'] = 'application/json'
	}

	if (config.headers.typer == 'up') {
		console.log(1);
		config.headers['Content-Type'] = 'multipart/form-data'
	}

	if (localStorage.getItem('userInfo')) {
		// 可以在此通过vm引用vuex中的变量，具体值在vm.$store.state中
		config.headers.accessToken = JSON.parse(localStorage.getItem('userInfo')).accessToken
	}
	return config
}, function(error) {
	// 对请求错误做些什么
	return Promise.reject(error);
})

//响应前
axios.interceptors.response.use(function(config) {
	// 可以设置 加载的动画效果 的隐藏
	//这里指的是请求之前做点什么
	return config
}, function(error) {
	// 对请求错误做些什么
	return Promise.reject(error);
})
