<template>
	<view>
		<u-navbar title="上传头像" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="u-center" style="margin: 30rpx 0">
			<u-avatar size="200" :src="pageParams.headUrl"
				default-url='https://zqcx.di-digo.com/app/image/head_2.png'>
			</u-avatar>
		</view>

		<okingtz-cropper @uploadSuccess="uploadSuccess" selectButtonBackgroundColor="#346cf2"
			saveButtonBackgroundColor="#346cf2"></okingtz-cropper>
	</view>
</template>

<script>
	//  1.引入项目
	import OkingtzCropper from '@/uni_modules/okingtz-cropper/components/okingtz-cropper/okingtz-cropper'

	import {
		fileupload,
		employeeinformationupdate,
		getuserinfo
	} from '@/config/api.js';
	export default {
		data() {
			return {
				pageParams: {}
			}
		},
		components: {
			//2.使用组件
			OkingtzCropper
		},
		onLoad(option) {
			this.onShowr()
		},

		methods: {
			onShowr() {
				getuserinfo({
					params: {}
				}).then(data => {
					this.pageParams = data.userInfoVo
				})
			},
			// 3.定义自己的回调函数
			uploadSuccess(tempFilePath) {
				console.log(tempFilePath, 'tempFilePath')
				// 根据自己的业务场景处理tempFilePath ;接口保存，或者上传至云空间
				// base64 这里取得是  base64.split(',')[1]那一串，前面的不取
				const bytes = window.atob(tempFilePath.split(',')[1]);
				const ia = new Uint8Array(bytes.length);
				for (let i = 0; i < bytes.length; i++) {
					ia[i] = bytes.charCodeAt(i);
				}
				const blob = new Blob([ia], {
					type: 'image/jpeg'
				});
				const fd = new FormData();

				// file就是传参的参数名
				fd.append('file', blob, new Date().getTime() + '.jpg');

				console.log(fd.get('file'), '000')

				fileupload({
					file: fd.get('file'),
					formData: {
						filePathType: '2'
					},
					name: 'file'
				}).then(data => {
					employeeinformationupdate({
						headUrl: data
					}).then(data => {
						uni.navigateBack({
							delta: 1
						})
						uni.showToast({
							title: '保存成功'
						})

					})
				})
			},
		}
	}
</script>
