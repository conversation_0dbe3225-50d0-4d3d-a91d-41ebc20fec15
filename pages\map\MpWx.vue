<template>
	<view class="coverview">
		<view style="position: relative;">
			<!--  -->
			<view class="trip-air" v-if="airShow">
				<view class="u-flexs b trip-item" @click="telClick(detail.psgMobile)"><u-icon name="phone"
						size="15"></u-icon>联系乘客</view>
				<view class="u-flexs b trip-item" @click="telClick(detail.dispMobile)"><u-icon name="phone"
						size="15"></u-icon>联系调度</view>
				<view class="u-flexs b trip-item" @click="stepOpen"><u-icon name="list" size="15"></u-icon>订单跟踪</view>
				<view class="u-flexs b trip-item" @click="mapClick()"
					v-if="detail.compOrderState==50||detail.compOrderState==60||detail.compOrderState==80"><u-icon
						name="map" size="15"></u-icon>导航位置</view>
				<view class="u-flexs b trip-item" v-if="detail.compOrderState==30" @click="receiving(0)"><u-icon
						name="close" size="15"></u-icon>取消订单</view>
			</view>
			<!--  -->
			<view class="u-flex u-row-between top">
				<view>
					<view>{{detail.carNumber || ''}}
						<template v-if="detail.numberColor">{{detail.numberColor|| ''}}</template>
					</view>
					<view class="top-_car_name">{{detail.brandModelFullName|| ''}}
						{{detail.carColor|| ''}}
					</view>
				</view>
				<view class="u-flex ">
					<view class="psg-nums">{{detail.psgNums|| ''}}人</view>
					<image class="diandian" src="https://zqcx.di-digo.com/app/image/diandian.png" @click="openAir" />
				</view>
			</view>
			<view class="top">
				<view class="u-flex">
					<view style="color: #a5a5a5;margin-right: 10rpx;">行程单号 :</view>
					<view class="font">{{detail.travelCode|| ''}}</view>
					<view class="c" v-if='detail.dispatchMode==1'>拆</view>
				</view>
			</view>
			<view class="u-flex top" v-if="detail.travelType==2">
				<view style="color: #a5a5a5;margin-right: 10rpx;">用车单位 :</view>
				<view class="font">{{detail.applyUnitName|| ''}}</view>
			</view>
			<view class="u-flex top" v-if="detail.travelType==1">
				<view style="color: #a5a5a5;margin-right: 10rpx;">用车时间 :</view>
				<view>{{detail.reserveStartTimeSeconds| timeFormat('mm月dd日 hh:MM')}} -
					{{detail.reserveEndTimeSeconds | timeFormat('mm月dd日 hh:MM')}}
				</view>
			</view>
			<view class="u-flex top" v-else>
				<view style="color: #a5a5a5;margin-right: 10rpx;">用车时间 :</view>
				<view>{{detail.reserveStartTimeSeconds| timeFormat('mm月dd日 hh:MM')}} </view>
			</view>
			<view class="u-flex top" v-if="detail.travelType!=1">
				<view style="color: #a5a5a5;margin-right: 10rpx;">用车套餐 :</view>
				<view class="font">{{detail.valuationFullName|| ''}}</view>
			</view>
			<view class="u-flex u-row-between font fee"
				v-if="detail.compOrderState==100 || detail.compOrderState==200 || detail.compOrderState==205">
				<view class="font_bold">{{detail.actualFee}} 元</view>
				<view @click="isShowClick">{{isShow? "收起":"展开"}}</view>
			</view>
			<view class="fee" v-if="isShow" style="margin-top: 0;max-height: 300rpx; overflow-y: auto;">
				<view class="u-flex u-row-between font">
					<view style="margin-right: 10rpx; ">预估里程</view>
					<view class="font">{{(detail.aboutDistance/1000).toFixed(1)}}公里</view>
				</view>
				<view class="u-flex u-row-between mt font">
					<view style="margin-right: 10rpx;">服务里程</view>
					<view class="font">{{(detail.actualUseCarMileage).toFixed(1)}}公里</view>
				</view>
				<view class="u-flex u-row-between mt font">
					<view style="margin-right: 10rpx;">服务时长</view>
					<view class="font">{{detail.actualUseCarTime|| ''}}</view>
				</view>
				<view class="u-flex u-row-between mt font">
					<view style="margin-right: 10rpx;">费用信息：</view>
				</view>
				<template v-if="detail.feeDetail">
					<view v-for="(item,index) in detail.feeDetail" :key="index">
						<template v-if="item.feeDetailCode==101">
							<view class="u-flexs u-row-between mt font">
								<view class="u-flexs u-flex-direction">
									<view class="item-title">{{item.key}}</view>
									<view class="item-detail pb" v-if='item.detail'>({{item.detail}})</view>
								</view>
								<view class="item-value">{{item.value}}元</view>
							</view>
						</template>
						<template v-else>
							<view class="u-flexs u-row-between mt font" v-if="Number(item.value)>0">
								<view class="u-flexs u-flex-direction">
									<view class="item-title">{{item.key}}</view>
									<view class="item-detail pb" v-if='item.detail'>({{item.detail}})</view>
								</view>
								<view class="item-value">{{item.value}}元</view>
							</view>
						</template>
						<template v-if="item.feeDetailCode==200">
							<template v-if="item.unit&&item.unit.length>0">
								<view v-for="(ite,idxs) in item.unit" :key="idxs">
									<template v-if="Number(ite.value)>0">
										<view class="u-flexs u-row-between mt font">
											<view class="u-flexs u-flex-direction">
												<view class="item-title">{{ite.key}}</view>
												<view class="item-detail pb" v-if='ite.detail'>({{ite.detail}})</view>
											</view>
											<view class="item-value">{{ite.value ||0}}元</view>
										</view>
									</template>
								</view>
							</template>
						</template>
					</view>
				</template>
				<view class="u-flex u-row-between mt font">
					<view style="margin-right: 10rpx;">订单金额 </view>
					<view class="font">{{detail.totalFee|| ''}}元</view>
				</view>
				<view class="u-flex u-row-between mt font" v-if="detail.discountReduceFee">
					<view style="margin-right: 10rpx;">企业折扣 </view>
					<view class="font" style="color: red;">- {{detail.discountReduceFee|| ''}}元</view>
				</view>
				<view class="u-flex u-row-between mt font" v-if="detail.reduceFee">
					<view style="margin-right: 10rpx;">优惠金额</view>
					<view class="font" style="color: red;">- {{detail.reduceFee|| ''}}元</view>
				</view>
				<view class="u-flex u-row-between mt font">
					<view style="margin-right: 10rpx;">实付金额 </view>
					<view class="font">{{detail.actualFee|| ''}}元</view>
				</view>
			</view>

			<view class="ordersBtm"
				v-if="detail.compOrderState!=100 && detail.compOrderState!=200 && detail.compOrderState!=205">
				<u-button text="确认接单" type="warning" v-if="detail.compOrderState==30" @click="receiving(1)"></u-button>
				<u-button text="开始执行" type="warning" v-if="detail.compOrderState==40"
					@click="startShowClick"></u-button>
				<u-button text="到达出发地" type="warning" v-if="detail.compOrderState==50" @click="arriveSetOut"></u-button>
				<u-button text="到达目的地" type="warning" v-if="detail.compOrderState==60" @click="arriveDes"></u-button>
				<u-button text="确定回场" type="warning" v-if="detail.compOrderState==80" @click="backcourt"></u-button>
				<u-button text="结束订单" type="warning" v-if="detail.compOrderState==90" @click="endOne"></u-button>
			</view>
		</view>

		<!-- 开始执行 -->
		<u-popup :show="startShow" mode="center" :round="10" :customStyle='{width:"80%"}' @close="startShow=false">
			<view class="popup-start" style="min-height:auto">
				<view class="s-title">开始执行</view>
				<view class="u-flex">
					<text style="margin-right: 10rpx;">开始里程数</text>
					<u--input type="number" placeholder="请输入仪表盘当前里程数" border="surround"
						v-model="start.actualStartMileage"></u--input>
				</view>
				<view class="s-upload" @click="uploadStart(false)" v-if="!start.startImage">
					<u-icon size="20" name="plus"></u-icon>
				</view>
				<view class="" v-else>
					<image :src="start.startImage" class="images" @click="lookClick(start.startImage)"></image>
				</view>
				<view style="font-size: 24rpx; margin-top: 4rpx;display: flex;">开始里程图片
					<u-icon size="20" name="trash" class="img-del" @click="endClearClick(4)"
						v-if="start.startImage"></u-icon>
				</view>
				<!-- 添加用于绘制水印的canvas，设置为不可见，并绑定动态样式 -->

				<view class="u-flex" style="margin-top: 10px;justify-content: space-around;">
					<u-button text="确定" type="primary" @click="startSave"></u-button>
					<view style="padding: 0 10rpx;"></view>
					<u-button text="取消" type="info" @click="endClearClick(1)"></u-button>
				</view>
			</view>
		</u-popup>

		<!-- 结束订单 -->
		<u-popup :show="endShow" mode="center" :round="10" :customStyle='{width:"80%"}'>
			<view class="popup-start" style="min-height:auto;overflow-y: auto;max-height: 800rpx;">
				<view class="s-title">填入里程与费用</view>
				<view v-if="detail.overtakeTimeType == 5">
					跨夜: {{end.nights||0}} 夜
				</view>
				<view style="margin: 20rpx 0;">
					实际开始里程(公里): {{detail.actualStartMileage||0}}
				</view>
				<template v-if="detail.overtakeTimeType == 5">
					<view class="u-flex" style="margin: 20rpx 0;" v-for="(item,idx) in end.dayEndMileage">
						<view style="margin-right: 10rpx;">第{{idx+1}}天结束里程(公里)</view>
						<u--input size="mini" @blur="endOvernightBlur(item,idx)" type="number" placeholder="请输入仪表盘当前里程数"
							border="surround" v-model="item.endMileage" style=" "></u--input>
					</view>
				</template>
				<view class="u-flex" style="margin: 20rpx 0;">
					<view style="margin-right: 10rpx;">最后结束里程(公里)</view>
					<u--input size="mini" @blur="endBlur" type="number" placeholder="请输入仪表盘当前里程数" border="surround"
						v-model="end.actualEndMileage" style=" "></u--input>
				</view>

				<view class="u-flex" style="justify-content: space-around;margin-bottom: 20rpx;">
					<view class="u-flex u-flex-direction">
						<image :src="detail.startMileageImgUrl" class="images"></image>
						<view>开始里程照片</view>
					</view>
					<view>
						<view class="s-upload" style="margin-top: 0;" @click="uploadStart(true)" v-if="!end.endImage">
							<u-icon size="20" name="plus"></u-icon>
						</view>
						<view class="" v-else>
							<image :src="end.endImage" class="images" @click="lookClick(end.endImage)"></image>
						</view>
						<view style="font-size: 24rpx; margin-top: 4rpx;" class="u-flex">结束里程图片 <u-icon size="20"
								name="trash" class="img-dels" @click="endClearClick(2)" v-if="end.endImage"></u-icon>
						</view>
					</view>

				</view>

				<view>费用信息：</view>
				<view class="u-flex" style="margin: 20rpx 0;" v-for="(item,idx) in endParam.otherFee" :key='idx'>
					<view style="margin-right: 10rpx;">{{item.key}} (元) </view>
					<u--input type="digit" placeholder="请输入" border="surround" v-model="item.value"
						style=" "></u--input>
				</view>
				<view class="u-flex" style="margin-top: 10px;justify-content: space-around;">
					<u-button text="取消" type="info" @click="endClearClick(3)"></u-button>
					<view style="padding: 0 10rpx;"></view>
					<u-button text="下一步" type="primary" @click="endTwoSave"></u-button>
				</view>
			</view>
		</u-popup>

		<!-- 结束订单-费用信息确认 -->
		<u-popup :show="endTwoShow" mode="center" :round="10" :customStyle='{width:"80%"}' @close="endTwoShowClick">
			<view class="popup-start" style="z-index: 9999;">
				<view class="s-title">订单费用信息确认</view>
				<view class="item b u-flex u-row-between">
					<view class="item-title">预估里程:</view>
					<view class="item-value">{{(Number(costDetail.aboutDistance)/1000).toFixed(1) || 0}}
						公里</view>
				</view>
				<view class="item b u-flex u-row-between">
					<view class="item-title">服务里程:</view>
					<view class="item-value">{{(Number(costDetail.actualDistance)/1000).toFixed(1) || 0}}
						公里</view>
				</view>
				<view class="item b u-flex u-row-between">
					<view class="item-title">服务时长:</view>
					<view class="item-value"> {{costDetail.actualDuration| transDay}}</view>
				</view>
				<template v-if="costDetail.feeDetail">
					<view class="b" v-for="(item,idx) in costDetail.feeDetail" :key="idx">
						<template v-if="item.feeDetailCode!=200">
							<view class="item pb u-flex u-row-between">
								<view class="item-title">{{item.key}}:</view>
								<view class="item-value">{{item.value}}元</view>
							</view>
							<view class="item-detail">{{item.detail||''}}</view>
						</template>
					</view>
					<view v-for="(item,idx) in costDetail.feeDetail" :key="idx">
						<template v-if="item.feeDetailCode==200">
							<template v-if="item.unit&&item.unit.length>0">
								<view class="b" v-for="(ite,index) in item.unit" :key="index">
									<view class="item pb u-flex u-row-between" style="border: 0;">
										<view class="item-title">{{ite.key}}:</view>
										<view class="item-value">{{ite.value}}元</view>
									</view>
									<view class="item-detail">{{ite.detail||''}}</view>
								</view>
							</template>
						</template>
					</view>
				</template>
				<view class="item b u-flex u-row-between">
					<view class="item-title font_bold">订单金额:</view>
					<view class="item-value">{{costDetail.totalFee}}元</view>
				</view>
				<view class="item b u-flex u-row-between" v-if="detail.travelType==2">
					<view class="item-title font_bold">企业折扣金额:</view>
					<view class="item-value" style="color: red;">- {{costDetail.discountReduceFee}}元</view>
				</view>
				<view class="item b u-flex u-row-between" v-if="detail.travelType==2">
					<view class="item-title font_bold">实付金额:</view>
					<view class="item-value" style="color: red;">{{costDetail.actualFee}}元</view>
				</view>
				<view class="u-flex" style="margin-top: 10px;justify-content: space-around;">
					<u-button text="取消" type="info" @click="endTwoShowClick"></u-button>
					<view style="padding: 0 10rpx;"></view>
					<u-button text="确定" type="primary" @click="endThree"></u-button>
				</view>
			</view>
			<template v-slot:footer>
			</template>
		</u-popup>

		<!-- 订单跟踪 -->
		<u-popup :show="stepShow" mode="center" :round="10" :customStyle='{width:"80%"}' @close="stepOpen">
			<view class="popup-start" style="min-height:auto">
				<u-steps :current="detail.orderEventHis.length || 0" direction="column" dot>
					<u-steps-item :title="`${item.operatorName}: ${item.evtDetContent} `"
						v-for='(item,idx) in detail.orderEventHis' :key="idx">
						<view slot="desc" style="margin-bottom: 20rpx;">{{item.operatorTimes}}</view>
					</u-steps-item>
				</u-steps>
				<view class="u-flex" style="margin-top: 10px;justify-content: space-around;">
					<u-button text="取消" type="info" @click="stepOpen"></u-button>
				</view>
			</view>
		</u-popup>

		<!-- 导航位置 -->
		<u-popup :show="mapShow" @close="mapShow=false" :mode='"bottom"'>
			<view class="mapBox">
				<view class="map_title">要去哪里呢?</view>
				<view class="map_ul" style="overflow-y: auto;height:90%;">
					<view class="u-flex u-row-between li">
						<view>{{ detail.fromAddrName}}</view>
						<view class="map_text" @click="goMap(detail.fromLng,detail.fromLat,detail.fromAddrDetail)">
							去这里
						</view>
					</view>
					<view class="u-flex u-row-between li" v-for="(item,index) in detail.throughAddrInfo" :key="index">
						<view>{{item.siteAddrName}}</view>
						<view class="map_text" @click="goMap(item.siteLng,item.siteLat,item.siteAddrDetail)">去这里</view>
					</view>
					<view class="u-flex u-row-between li">
						<view>{{ detail.toAddrName}}</view>
						<view class="map_text" @click="goMap(detail.toLng,detail.toLat,detail.toAddrDetail)">去这里
						</view>
					</view>
				</view>
			</view>
		</u-popup>
		<canvas canvas-id="watermarkCanvas"
			:style="'position: absolute; left: -9999px; width:' + wh.w + '; height:' + wh.h + ';'"></canvas>
	</view>
</template>

<script>
	import {
		driveracceptorder,
		startExecution,
		driverarrivefromaddr,
		driverarrivetoaddr,
		drivercomeback,
		tempvaluationinfo,
		costcalculate,
		driveroverorder,
		driverorderinfocopy
	} from '@/config/api.js';
	import {
		uploadImg,
	} from '@/config/consoler.js'
	import {
		overnightDay
	} from '@/common/utils.js'
	export default {
		props: {
			detail: {
				type: Object,
				default: () => {
					orderEventHis: []
				}
			},
			position: {
				type: String,
				default: () => ''
			}
		},
		mounted() {
			console.log(this.detail, 'Zq@123456.')
			// console.log(overnightDay('2025-07-03 08:22:16',"2025-07-04 17:50:55"),'----------')
		},
		data() {
			return {
				startShow: false,
				start: {},
				wh: {
					w: "300px",
					h: "300px"
				},
				endShow: false,
				end: {
					dayEndMileage: []
				},
				endParam: {},
				endTwoShow: false,
				costDetail: {}, //结束订单 第二次费用确认
				isShow: false,
				airShow: false, //点点点
				mapShow: false, //途径地
				stepShow: false, //订单跟踪
			}
		},
		methods: {

			openAir() {
				this.$set(this, 'airShow', true)
				setTimeout(() => {
					this.$set(this, 'airShow', false)
				}, 5000)
			},
			endThree() {
				// 结束订单-费用确认
				let obj = {
					id: this.detail.travelId,
					dispatchOverVo: {
						actualStartMileage: this.detail.actualStartMileage,
						startMileageImgUrl: this.detail.startMileageImgUrl,
						actualStartTime: this.detail.actualStartTime,
						actualEndTime: this.detail.actualEndTime,
						reduceFee: this.detail.reduceFee || 0,
						actualEndMileage: this.end.actualEndMileage,
						endMileageImgUrl: this.end.endMileageImgUrl,
						otherFee: JSON.stringify(this.endParam.otherFee || []),
						dayEndMileage: JSON.stringify(this.end.dayEndMileage || []),
						nights: this.end.nights || null
					}
				}
				driveroverorder(obj).then(res => {
					this.endShow = false
					this.endTwoShow = false
					uni.$u.toast('成功')
					this.setTimeoutRefresh()
				})
				console.log(obj, 'obj')
			},
			endTwoSave() {
				if (this.end.dayEndMileage && this.detail.overtakeTimeType == 5) {
					try {
						for (let i = 0; i < this.end.dayEndMileage.length; i++) {
							const item = this.end.dayEndMileage[i];
							if (!item.endMileage && item.endMileage !== 0) {
								uni.$u.toast(`第${i+1}天结束里程不能为空`);
								return;
							}
						}
					} catch (error) {
						// uni.$u.toast('请填写结束里程')
						//TODO handle the exception
					}
				}

				if (!this.end.actualEndMileage) return uni.$u.toast('请填写结束里程')
				if (!this.end.endMileageImgUrl) return uni.$u.toast('请上传结束里程图片')

				let otherFeeCopy = JSON.parse(JSON.stringify(this.endParam.otherFee))
				console.log(otherFeeCopy, 'otherFeeCopy')
				console.log(this.endParam.otherFee, 'otherFeeCopy')
				// 结束订单下一步
				otherFeeCopy.forEach(res => {
					res.value = res.value ? res.value : 0
				})
				let obj = {
					id: this.detail.travelId,
					costCalculateVo: {
						actualStartMileage: this.detail.actualStartMileage,
						startMileageImgUrl: this.detail.startMileageImgUrl,
						actualStartTime: this.detail.actualStartTime,
						actualEndTime: this.detail.actualEndTime,
						reduceFee: this.detail.reduceFee || 0,
						actualEndMileage: this.end.actualEndMileage,
						endMileageImgUrl: this.end.endMileageImgUrl,
						otherFee: JSON.stringify(otherFeeCopy || []),
						dayEndMileage: JSON.stringify(this.end.dayEndMileage || []),
						nights: this.end.nights
					}
				}

				costcalculate(obj).then(res => {
					res.feeDetail = JSON.parse(res.feeDetail)
					res.feeDetail.map((item, index) => {
						if (item.feeDetailCode == 200) {
							item.unit = JSON.parse(item.unit)
						}
					})
					this.costDetail = res
					if (res.reduceFee) {
						this.endParam.otherFee = res.reduceFee
					}
					this.endTwoShow = true
				})
			},
			async endOne() {
				try {
					let ress = await driverorderinfocopy({
						id: this.detail.travelId,
					})
					this.$set(this.end, 'nights', ress.nights)
					if (this.end.nights && this.end.nights != 0) {
						let list = [];
						for (let i = 1; i <= this.end.nights; i++) {
							list.push({
								day: i,
								endMileage: '',
							});
						}
						this.$set(this.end, 'dayEndMileage', list)
					} else {
						this.$set(this.end, 'dayEndMileage', [])
					}
				} catch (err) {

				}
				// 结束订单
				tempvaluationinfo({
					params: {
						id: this.detail.valuationId,
					}
				}).then(res => {
					this.endParam.otherFee = JSON.parse(res.otherFee)

					this.endShow = true
				})
			},
			backcourt() {
				// 回场
				uni.showModal({
					content: '是否确定回场？',
					success: (res) => {
						if (res.confirm) {
							drivercomeback({
								id: this.detail.travelId,
							}).then(ress => {
								uni.$u.toast('成功')
								this.setTimeoutRefresh()
							})
						}
					}
				})
			},
			arriveDes() {
				// 到达目的地
				uni.showModal({
					content: '是否确定到达目的地？',
					success: (res) => {
						if (res.confirm) {
							driverarrivetoaddr({
								id: this.detail.travelId,
							}).then(ress => {
								uni.$u.toast('成功')
								this.setTimeoutRefresh()
							})
						}
					}
				})
			},
			arriveSetOut() {
				// 到达出发地
				uni.showModal({
					content: '是否确定到达出发地？',
					success: (res) => {
						if (res.confirm) {
							driverarrivefromaddr({
								id: this.detail.travelId,
							}).then(ress => {
								uni.$u.toast('成功')
								this.setTimeoutRefresh()
							})
						}
					}
				})
			},
			startSave() {
				// 保存开始执行 出发信息
				if (!this.start.actualStartMileage) return uni.$u.toast('请输入开始里程')
				if (!this.start.startMileageImgUrl) return uni.$u.toast('请上传图片')
				startExecution({
					actualStartMileage: this.start.actualStartMileage,
					startMileageImgUrl: this.start.startMileageImgUrl,
					id: this.detail.travelId
				}).then(res => {
					uni.$u.toast('成功')
					this.setTimeoutRefresh()
					this.start.actualStartMileage = ''
					this.start.startMileageImgUrl = ''
					this.start.startImage = ''
					this.startShow = false
				})

			},
			receiving(type) {
				let text = type == 1 ? '是否确定接单' : '是否取消订单'
				// 确认接单
				uni.showModal({
					content: text,
					success: (res) => {
						if (res.confirm) {
							driveracceptorder({
								id: this.detail.travelId,
								driverAcceptOrderVo: {
									isAccept: type,
								},
							}).then(ress => {
								uni.$u.toast('成功')
								this.setTimeoutRefresh()
							})
						}
					}
				})
			},
			lookClick(url) {
				uni.previewImage({
					urls: [url],
				})
			},
			// 上传开始图片
			uploadStart(type) {
				let that = this
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original '], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['camera'], //从相册选择
					success: function(res) {
						// 获取到图片后添加水印
						that.addWatermark(res.tempFilePaths[0], 1).then(watermarkedImage => {
							// 使用返回的带水印图片
							if (watermarkedImage) {
								uploadImg({
									url: watermarkedImage
								}, 4).then(ress => {
									// console.log("上传添加水印图片成功：" + JSON.stringify(ress))
									if (type) {
										that.$set(that.end, 'endImage', watermarkedImage);
										that.$set(that.end, 'endMileageImgUrl', ress);
									} else {
										that.$set(that.start, 'startImage', watermarkedImage);
										that.$set(that.start, 'startMileageImgUrl', ress);
									}
								})
							}
						});
					}
				});
			},
			setTimeoutRefresh() {
				setTimeout(() => {
					this.$emit('refresh')
				}, 1000)
			},
			endBlur() {
				if (this.detail.overtakeTimeType == 5 && this.end.dayEndMileage.length != 0) {
					if (this.end.dayEndMileage) {
						let length = this.end.dayEndMileage.length - 1
						if (Number(this.end.dayEndMileage[length].endMileage) >= Number(this.end.actualEndMileage)) {
							uni.$u.toast('请输入大于上一天的里程')
							this.$set(this.end, 'actualEndMileage', '')
						}

					}
				} else {
					if (this.detail.actualStartMileage >= this.end.actualEndMileage) {
						uni.$u.toast('请输入大于开始里程')
						this.$set(this.end, 'actualEndMileage', '')
					}
				}

			},
			endOvernightBlur(item, idx) {
				if (idx == 0) {
					if (this.detail.actualStartMileage >= item.endMileage) {
						uni.$u.toast('请输入大于开始里程')
						this.$set(item, 'endMileage', '')
					}
				} else {
					if (Number(this.end.dayEndMileage[idx - 1].endMileage) >= Number(item.endMileage)) {
						uni.$u.toast('请输入大于上一天的里程')
						this.$set(item, 'endMileage', '')
					}
				}
			},
			mapClick() {
				let throughAddrInfoArr = this.detail.throughAddrInfo
				let type = this.detail.compOrderState
				let lngr = type == 60 ? this.detail.toLng : this.detail.fromLng
				let latr = type == 60 ? this.detail.toLat : this.detail.fromLat
				let namer = type == 60 ? this.detail.toAddrDetail : this.detail.fromAddrName
				if (type == 50) {
					this.goMap(lngr, latr, namer)
				} else {

					// re
					if (throughAddrInfoArr && throughAddrInfoArr.length != 0) {
						this.mapShow = true
					} else {
						this.goMap(lngr, latr, namer)
					}
				}
			},
			goMap(lngr, latr, namer) {
				// #ifdef H5
				window.open(
					`https://uri.amap.com/marker?position=${lngr},${latr}&name=${namer}&coordinate=gaode&callnative=1`,
					"_blank")
				// #endif
				// #ifdef MP-WEIXIN
				let objr = {
					latitude: Number(latr),
					longitude: Number(lngr),
					name: namer,
					address: namer,
				}
				uni.navigateTo({
					url: `/pageDriver/staging/staging?objr=${JSON.stringify(objr)}`,
				});
				// #endif
			},
			telClick(tel) {
				if (!tel) return uni.$u.toast('暂无联系方式')
				uni.makePhoneCall({
					phoneNumber: tel //仅为示例
				});
			},
			stepOpen() {
				this.$set(this, 'stepShow', !this.stepShow)
			},
			isShowClick() {
				console.log(this.detail)
				this.$set(this, 'isShow', !this.isShow)
			},
			startShowClick() {
				this.$set(this, 'startShow', !this.startShow)
			},
			endTwoShowClick() {
				this.$set(this, 'endTwoShow', !this.endTwoShow)
			},
			endShowClick() {
				this.$set(this, 'endTwoShow', false)
			},
			endClearClick(t) {
				if (t == 1) {
					this.start.actualStartMileage = ''
					this.start.startImage = ''
					this.startShow = false
				} else if (t == 2) {
					this.end.endImage = ''
					this.end.endMileageImgUrl = ''
				} else if (t == 3) {
					this.end.endImage = ''
					this.end.endMileageImgUrl = ''
					this.end.actualEndMileage = ''
					this.endShow = false
				} else if (t == 4) {
					this.start.startImage = ''
					this.start.startMileageImgUrl = ''
				}

			},
			// 添加水印方法
			addWatermark(imagePath, type) {
				let that = this;
				// 返回Promise以便外部获取结果
				return new Promise((resolve, reject) => {
					// 获取图片信息
					uni.getImageInfo({
						src: imagePath,
						success: function(imageInfo) {
							// 计算压缩后的尺寸
							let targetWidth, targetHeight;
							const MAX_SIZE = 1200; // 设置最大尺寸为1200像素

							if (imageInfo.width > MAX_SIZE || imageInfo.height > MAX_SIZE) {
								// 需要压缩
								if (imageInfo.width > imageInfo.height) {
									// 宽图
									targetWidth = MAX_SIZE;
									targetHeight = Math.floor(imageInfo.height * (MAX_SIZE / imageInfo
										.width));
								} else {
									// 长图
									targetHeight = MAX_SIZE;
									targetWidth = Math.floor(imageInfo.width * (MAX_SIZE / imageInfo
										.height));
								}
							} else {
								// 不需要压缩
								targetWidth = imageInfo.width;
								targetHeight = imageInfo.height;
							}

							const ctx = uni.createCanvasContext('watermarkCanvas', that);

							// 设置画布尺寸为压缩后的尺寸
							that.$set(that.wh, 'w', targetWidth + 'px');
							that.$set(that.wh, 'h', targetHeight + 'px');

							// 根据图片尺寸计算合适的字体大小
							const baseFontSize = Math.min(targetWidth, targetHeight) * 0.03;
							const fontSize = Math.max(16, Math.min(40, baseFontSize));

							// 计算水印位置的边距
							const margin = Math.min(targetWidth, targetHeight) * 0.02;

							// 绘制压缩后的原图
							ctx.drawImage(imagePath, 0, 0, targetWidth, targetHeight);

							// 设置水印文字样式
							ctx.setFontSize(fontSize);
							ctx.setFillStyle('rgba(255, 255, 255, 0.7)');
							ctx.setTextAlign('right');

							// 添加水印文字
							const watermarkText = that.mapText || '定位失败';
							// console.log('查看水印文字：' + that.mapText, this.position)
							const dateText = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd hh:MM:ss');

							// 添加文字背景色
							// 计算文字宽度（近似值）
							const watermarkTextWidth = ctx.measureText ? ctx.measureText(watermarkText)
								.width : watermarkText.length * fontSize * 0.6;
							const dateTextWidth = ctx.measureText ? ctx.measureText(dateText).width :
								dateText.length * fontSize * 0.6;
							const maxWidth = Math.max(watermarkTextWidth, dateTextWidth);
							const lineHeight = fontSize * 1.5;

							// 绘制背景矩形
							ctx.save();
							ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'; // 半透明黑色背景
							// 水印文字背景
							ctx.fillRect(targetWidth - margin - maxWidth - 10, targetHeight - margin -
								lineHeight - fontSize, maxWidth + 20, fontSize + 5);
							// 日期文字背景
							ctx.fillRect(targetWidth - margin - maxWidth - 10, targetHeight - margin -
								fontSize, maxWidth + 20, fontSize + 5);
							ctx.restore();

							// 恢复文字颜色
							ctx.setFillStyle('rgba(255, 255, 255, 0.9)'); // 增加不透明度，使水印更清晰
							ctx.fillText(watermarkText, targetWidth - margin, targetHeight - margin -
								lineHeight);
							ctx.fillText(dateText, targetWidth - margin, targetHeight - margin);

							setTimeout(() => {
								// 绘制完成
								ctx.draw(false, function() {
									// 将Canvas导出为图片
									uni.canvasToTempFilePath({
										canvasId: 'watermarkCanvas',
										width: targetWidth,
										height: targetHeight,
										destWidth: targetWidth,
										destHeight: targetHeight,
										quality: 0.5, // 设置导出图片的质量，0-1之间
										success: function(res) {
											// 返回带水印的图片路径
											resolve(res.tempFilePath);
										},
										fail: function(err) {
											console.error('导出图片失败', err);
											// 如果失败，返回原图
											resolve(imagePath);
										}
									}, that);
								});
							}, 1000)
						},
						fail: function(err) {
							console.error('获取图片信息失败', err);
							// 如果失败，返回原图
							resolve(imagePath);
						}
					});
				});
			}
		},
		computed: {
			mapText() {
				return this.position
			},
			mapWh() {
				return `width:${this.wh.w};height:${this.wh.h};`
			}

		},
		filters: {
			transDay(mss) {
				mss = mss * 1000;
				let days = Math.floor(mss / (1000 * 60 * 60 * 24));
				let hours = Math.floor((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
				let minutes = Math.floor((mss % (1000 * 60 * 60)) / (1000 * 60));
				let seconds = Math.round((mss % (1000 * 60)) / 1000);
				// return days + "天" + hours + "小时" + minutes + "分钟" + seconds + "秒";
				// return `${days?days+'天':''}${hours?hours+'小时':''}${minutes?minutes+'分钟':''}${seconds?seconds+'秒':''}`
				let timeStr = ''
				if (days > 0) {
					timeStr += days + "天"
				}
				if (hours > 0) {
					timeStr += hours + "小时"
				}
				let mr = Math.ceil(Number(minutes) + Number(`0.${seconds}`))
				timeStr += `${mr}分钟`
				return timeStr;
			},
			timeFormat(dateTime = null, fmt = 'yyyy-mm-dd') {
				// 如果为null,则格式化当前时间
				if (!dateTime) dateTime = Number(new Date())
				// 如果dateTime长度为10或者13，则为秒和毫秒的时间戳，如果超过13位，则为其他的时间格式
				if (dateTime.toString().length == 10) dateTime *= 1000
				const date = new Date(dateTime)
				let ret
				const opt = {
					'y+': date.getFullYear().toString(), // 年
					'm+': (date.getMonth() + 1).toString(), // 月
					'd+': date.getDate().toString(), // 日
					'h+': date.getHours().toString(), // 时
					'M+': date.getMinutes().toString(), // 分
					's+': date.getSeconds().toString() // 秒
					// 有其他格式化字符需求可以继续添加，必须转化成字符串
				}
				for (const k in opt) {
					ret = new RegExp(`(${k})`).exec(fmt)
					if (ret) {
						fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1]
							.length, '0')))
					}
				}
				return fmt
			},
		}
	}
</script>

<style lang="scss" scoped>
	.coverview {
		position: fixed;
		bottom: 40rpx;
		background: #fff;
		margin: 20rpx;
		width: calc(100vw - 40rpx);
	}

	.diandian {
		width: 65rpx;
		height: 65rpx;
	}

	.top {
		padding: 20rpx 30rpx;
		border-bottom: .5px solid #dfdfdf;
		font-size: 24rpx;
	}

	.top-_car_name {
		font-size: 26rpx;
		margin-top: 10rpx;
		color: #ccc;
	}

	.ordersBtm {
		padding: 10rpx;
	}

	.font {
		font-size: 24rpx;
	}

	.popup-start {
		padding: 30rpx;
		font-size: 24rpx;
		min-height: 800rpx;
		overflow-y: auto;
	}

	.s-title {
		text-align: center;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
	}

	.s-upload {
		width: 160rpx;
		height: 160rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border: 1px dashed #ccc;
		margin-top: 30rpx;
	}

	.images {
		width: 160rpx;
		height: 160rpx;
		object-fit: cover;
		position: relative;
	}

	.img-del {
		position: absolute;
		color: red;
		right: 20rpx;
		top: 10rpx;
	}

	.img-dels {
		position: absolute;
		color: red;
		right: 30rpx;
	}

	.item {
		margin: 10rpx 0;
		padding: 10rpx 0;
		padding-bottom: 20rpx;
		font-size: 26rpx;
	}

	.item-detail {
		color: #ccc;
		font-size: 24rpx;
		padding-bottom: 20rpx;
	}

	.b {
		border-bottom: 1px solid #efefef;
	}

	.pb {
		padding-bottom: 0 !important;
	}

	.mt {
		margin-top: 20rpx;
	}

	.font_bold {
		font-weight: bold;
	}

	.fee {
		margin: 14rpx;
		padding: 20rpx;
		background: rgb(249, 249, 249);
	}


	.map_detail_scoll {
		/* max-height: 0; */
		transition: all .3s linear 0s;
		overflow-y: auto;
	}

	.active {
		/* max-height: 200rem; */
		transition: all .3s linear 0s;
	}

	.psg-nums {
		padding: 4px 10px;
		border: 1px solid #199DFF;
		color: #199DFF;
		border-radius: 3px;
	}

	.c {
		border: 1px solid #9659f7;
		background: #eee5fe;
		padding: 0 2px;
		color: #9659f7;
		border-radius: 3px;
	}

	.trip-air {
		position: absolute;
		right: 80rpx;
		top: 10rpx;
		font-size: 26rpx;
		background: #fff;
		border-radius: 10rpx;
		box-shadow: 1px 1px 7px #ccc;
		z-index: 999;
	}

	.trip-item {
		padding: 20rpx 30rpx;
	}


	.mapBox {
		height: 30vh;
		background: #fff;
		margin-bottom: 30rpx;
		padding: 30rpx 30rpx 0;
		font-size: 24rpx;
		font-weight: bold;
		color: #000;

		.map_title {}

		.map_ul {
			padding: 0;
			margin-top: 20rpx;
			margin-bottom: 40rpx;

			.li {
				background-color: #EFEFEF;
				padding: 16rpx 32rpx;
				border-radius: 12rpx;
				line-height: 40rpx;
				margin-bottom: 20rpx;
			}

			.map_text {
				background-color: #fff;
				border-radius: 25px;
				padding: 4px 14px;
			}
		}
	}
</style>