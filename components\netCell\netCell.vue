<template>
	<view class="ner_c_item u-flex u-row-between" :class="{'bg':types}">
		<view class="ner_c_i_left u-flex u-flex-1" > 
			<view class="ner_c_i_icon">
				<slot name="icon"></slot>
			</view>
			<view class="ner_c_i_l_text">
				<slot name="name"></slot>
			</view>
		</view>
		<view class="ner_c_i_icon">
			<u-icon name="arrow-right"></u-icon>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			types: {
				type: Boolean,
				default: () => {
					return false
				}
			}
		},
		data() {
			return {

			}
		},
		methods: {

		}
	}
</script>

<style scoped>
	.ner_c_item {
		padding: 24rpx;
	}

	.ner_c_i_icon {
		width: 40rpx;
		margin: 0 10rpx;
	}

	.ner_c_i_l_text {
		margin-left: 20rpx;
		flex: 1;
	}

	.bg {
		background: #f4f4f4;
		margin: 0 24rpx;
		padding: 24rpx 0px !important;
		border-radius: 20rpx;
	}
</style>