<template>
	<view class="apply">
		<u-navbar :title="orderTitle" :autoBack="true" :placeholder="true"></u-navbar>
		<!-- <view class="nav_box" v-if="orderTyper==3">
			<u-tabs :list="navList" :scrollable="true" lineWidth="22" lineHeight="2" lineColor="#346CF2"
				:activeStyle="{color: '#346CF2'}" :inactiveStyle="{color: '#999999'}" @click="changeTab"
				itemStyle="height: 40px; padding:0 10px">
			</u-tabs>
		</view> -->
		<view class="filterBox u-flex">
			<view class="filter_name u-flex" @click="typeClick">
				<span style="margin-right: 10rpx;">{{searWash(orderState)}}</span>
				<u-icon name="arrow-down" size='12'></u-icon>
			</view>
			<u-search class="search" placeholder="搜索订单" shape='square' :showAction="false" v-model="keyword"
				@search="searchBtn">
			</u-search>
		</view>

		<view class="tabpage">
			<view class="" v-for="(item,index) in dataList" :key="index">

				<orderListItem :item="item" @confirm='confirm' @goDetail="goDetail" v-if="orderTyper==1||orderTyper==2">
				</orderListItem>

				<view class="app_list" v-if="orderTyper==3" @click="goDetail(item)">
					<view class="appbtnbox u-flex">
						<view class="appbtn colors" v-if="orderTyper==3">
							{{dataWash(item.compOrderStateName)}}
						</view>
						<view class="appbtn" @click.stop="confirm(item)" v-if="orderTyper!=3">
							{{item.compOrderState==10?"待审批":item.compOrderState==95?"行后待审批":item.compOrderState==20?"待派车":item.compOrderState==24?"租赁待审批":item.compOrderState==25?"租赁待派车":item.compOrderState==30?"待接单":item.compOrderState==40?"待执行":item.compOrderState==50?"执行中":item.compOrderState==60?"到达出发地":item.compOrderState==70?"进行中":item.compOrderState==80?"到达目的地":item.compOrderState==103?'费用待审批':item.compOrderState==120?"结算审核中":item.compOrderState==200?"已完成":item.compOrderState==210?"已取消":item.compOrderState==220?"审批驳回":item.compOrderState==230?"调度驳回":item.compOrderState==100?"确认费用":item.compOrderState==90?"已回场":item.compOrderState==110?"待结算":item.compOrderState==205?"去评价":"订单异常"}}
						</view>
					</view>

					<view class="u-flex u-row-between">
						<view class="u-flex">
							<view class="jiao_img" v-if="item.travelType==1">
								<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/qi.png">
								</u-image>
							</view>
							<view class="jiao_img" v-if="item.travelType==2">
								<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/zu.png">
								</u-image>
							</view>
							<view class="jiao_img" v-if="item.applyType==2">
								<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/si.png">
								</u-image>
							</view>
							<span class="text-dh" v-if="orderTyper==3"><span
									style="color: #000; margin-right: 8rpx;">{{item.netType}}</span>行程单号：{{item.travelCode}}</span>
							<span class="text-dh" v-else>申请单号：{{item.applyCode}}</span>
						</view>
						<view class="text-rt">
							<u-icon name="arrow-right"></u-icon>
						</view>
					</view>

					<view class="app_t_line u-flex">
						<u-image class="tab_icon" :width="14" :height="14"
							src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon.png"></u-image>
						<span>{{new Date(item.reserveStartTime).getTime() | date('mm月dd日 hh:MM')}}
							<!-- - {{new Date(item.reserveEndTime ).getTime()| date('mm月dd日 hh:MM')}} -->
						</span>
					</view>

					<view class="app_t_line u-flex">
						<view class="spot"></view>
						<span>{{item.fromAddrName}}</span>
					</view>

					<view class="app_t_line u-flex" v-if="item.throughAddrInfoName">
						<view class="spot" style="background-color: #5ac725;"></view>
						<span style="width: 97%;">{{item.throughAddrInfoName}}</span>
					</view>

					<view class="app_t_line u-flex">
						<view class="spot spottwo"></view>
						<span>{{item.toAddrName}}</span>
					</view>

				</view>
			</view>
		</view>

		<!-- 更多 -->
		<u-loadmore v-if="dataList.length>0" :status="status" />

		<u-empty v-if="dataList.length==0" mode="order" icon="http://cdn.uviewui.com/uview/empty/order.png">
		</u-empty>

		<!-- 去评价 -->
		<u-popup :show="evaluaShow" :round="10" mode="bottom" @close="evaluaShow=false" :customStyle="styler">
			<view class="evalua">
				<text class="evaluaText">你的评价会让司机做的更好</text>
				<u-rate active-color="#ffd917" inactive-color="#b2b2b2" :gutter="40" :size='40' :count="5"
					v-model="evaluaVal"></u-rate>
				<view style='width: 120rpx;margin-top: 40rpx;'>
					<u-button type="primary" text="确定" size='mini' @click='evaluaChange'></u-button>
				</view>
			</view>
		</u-popup>

		<!-- 费用 -->
		<u-popup class="popup_bg" :round="5" :show="endTwoShow" mode="center" @close="endTwoShow=false"
			:customStyle="styleObjr">
			<view class="popup_tit ">
				<!-- <u-icon name="arrow-left" size="24" @click='endTwoShow=false,endOneShow=true'></u-icon> -->
				<view>订单费用信息确认</view>
				<view></view>
			</view>

			<ul class="ui-list">
				<li>
					<div class="ui-list-info">
						<h5 class="ui-nowrap">服务里程：</h5>
						<div class="ui-txt-info" style="color: #262626;width: 80%;text-align: right;">
							{{endData.actualDistance}}公里
						</div>
					</div>
				</li>

				<li>
					<div class="ui-list-info">
						<h5 class="ui-nowrap">服务时长：</h5>
						<div class="ui-txt-info" style="color: #262626;width: 80%;text-align: right;">
							{{endData.actualDuration}}
						</div>
					</div>
				</li>

				<!-- 记录 -->
				<li v-for="(item,index) in endData.feeDetail" :key="index">
					<div class="ui-list-info">
						<h5 class="ui-nowrap">{{item.key}}：</h5>
						<div class="ui-txt-info" style="color: #262626;">{{item.value?item.value:0}}元</div>
					</div>
				</li>


				<li>
					<div class="ui-list-info">
						<h5 class="ui-nowrap font_bold">订单金额：</h5>
						<div class="ui-txt-info" style="color: #262626;">{{endData.totalFee}}元</div>
					</div>
				</li>

				<li v-if="endData.discountReduceFee ">
					<div class="ui-list-info">
						<h5 class="ui-nowrap font_bold">企业折扣：</h5>
						<div class="ui-txt-info" style="color: #262626;">{{endData.discountReduceFee}}元</div>
					</div>
				</li>

				<li v-if="endData.reduceFee">
					<div class="ui-list-info">
						<h5 class="ui-nowrap font_bold" style="color: red;">优惠金额：</h5>
						<div class="ui-txt-info">
							<div class="ui-txt-info" style="color: #262626;">{{endData.reduceFee}}元</div>
						</div>
					</div>
				</li>

				<li>
					<div class="ui-list-info">
						<h5 class="ui-nowrap font_bold">实付金额：</h5>
						<div class="ui-txt-info" style="color: #262626;">
							{{endData.totalFee - endData.reduceFee - endData.discountReduceFee}}元
						</div>
					</div>
				</li>

			</ul>

			<view class="u-flex u-row-around popup_btn_box">
				<view class="popup_btn">
					<u-button color="#346CF2" type="primary" text="确定" @click="endThree()"></u-button>
				</view>
				<view class="popup_btn two">
					<u-button color="#E9ECF7" type="primary" text="取消" @click="endTwoShow=false">
					</u-button>
				</view>
			</view>
		</u-popup>

		<u-picker :show="stateShow" :columns="navList" keyName="name" @confirm='stateClick'
			@cancel='typeClick'></u-picker>
	</view>
</template>

<script>
	import orderListItem from "@/pagec/components/orderListItem/orderListItem.vue"
	import {
		comporderlist,
		comporderinfo,
		comfirmorder,
		comSaveAluated,
		comCostdetails,
		privateorderlist
	} from '@/config/api.js';
	import {
		tabsTwo,
		tabsThrre
	} from './order.js'
	import {
		applyNetcarlist
	} from '@/config/consoler.js';
	export default {
		components: {
			orderListItem
		},
		data() {
			return {
				stateVal: '',
				keyword: '',
				dataList: [],
				status: 'loadmore',
				pageNum: 1,
				pageSize: 20,
				hasNext: true,
				orderState: '',
				navList: [
					[{
							name: '全部状态',
							val: ''
						}, {
							name: '待派车',
							val: '20'
						}, {
							name: '待执行',
							val: '40'
						}, {
							name: '执行中',
							val: '50'
						}, {
							name: '待确认',
							val: '100'
						},
						{
							name: '待评价',
							val: '205'
						},
						{
							name: '驳回/取消',
							val: '2000'
						}
						// , {
						// 	name: '申诉',
						// 	val:'20'
						// },
					]
				],
				evaluaShow: false,
				evaluaObj: {},
				evaluaVal: 0,
				orderTyper: null,
				orderTitle: null,
				endTwoShow: false,
				endData: {},
				endId: null,
				endType: null,
				styleObjr: {
					width: '85%'
				},
				styler: {
					width: '100%'
				},
				stateShow: false
			};
		},
		onLoad(option) {
			this.orderTyper = option.typer
			this.orderTitle = option.namer
			this.queryList()
			if (this.orderTyper == 2) {
				this.navList = [tabsTwo()]
			} else if (this.orderTyper == 3) {
				this.navList = [tabsThrre()]
			}
		},
		filters: {
			transDay(time) {
				console.log(time, 'time');
				let d = parseInt(time / 60 / 60 / 24)
				let h = parseInt(time / 60 / 60 % 24)
				h = h < 10 ? '0' + h : h
				let m = parseInt(time / 60 % 60)
				m = m < 10 ? '0' + m : m
				let s = parseInt(time % 60)
				s = s < 10 ? '0' + s : s
				// 作为返回值返回
				return d + "天" + h + "小时" + m + "分钟"
			}
		},
		onReachBottom() {
			if (this.hasNext) {
				this.status = 'loading';
				this.pageNum = ++this.pageNum;
				setTimeout(() => {
					this.queryList()
				}, 1000)
			}
		},
		methods: {
			stateClick(e) {
				this.pageNum = 1
				this.dataList = []
				this.orderState = e.value[0].val
				this.queryList()
				this.typeClick()
			},
			typeClick() {
				this.stateShow = !this.stateShow
			},
			searchBtn() {
				this.pageNum = 1
				this.dataList = []
				this.queryList()
			},
			searWash(v) {
				let arrs = this.navList[0]
				let text = ''
				arrs.forEach(vs => {
					if (vs.val == v) {
						text = vs.name
					}
				})
				return text
			},
			searchFun() {
				this.pageNum = 1
				this.dataList = []
				this.queryList()
			},
			dataWash(v) {
				return v ? v : '-'
			},
			endThree() {
				let that = this
				console.log(that.endData.travelId, ' that.endData.travelId');
				comfirmorder({
					params: {
						id: that.endData.travelId,
					}
				}).then((data) => {
					uni.showToast({
						title: '操作成功！'
					})
					setTimeout(() => {
						this.endTwoShow = false
						that.endData = {}
						that.dataList = []
						that.queryList()
					}, 1000)
				})
			},
			confirm(item) {
				let that = this
				if (item.compOrderState == 100) {
					comCostdetails({
						params: {
							id: item.travelId,
						}
					}).then((data) => {
						data.travelType = item.travelType
						data.travelId = item.travelId
						this.endData = data;
						console.log(this.endData);
						if (this.endData.feeDetail) {
							this.endData.feeDetail = JSON.parse(this.endData.feeDetail)
							this.endData.feeDetail.forEach((item, index) => {
								if (item.feeDetailCode == 200) {
									item.unit = JSON.parse(item.unit)
									item.unit.forEach(r => {
										this.endData.feeDetail.push(r)
									})

								}
								if (item.key == "自定义项目费用") {
									this.endData.feeDetail.splice(index, 1)
								}
							})
						}
						this.endTwoShow = true
					})

				} else if (item.compOrderState == 205) {
					this.evaluaObj = item
					this.evaluaShow = true
				}
			},
			evaluaChange(e) {
				comSaveAluated({
					id: this.evaluaObj.travelId,
					evaluateStar: this.evaluaVal
				}).then((data) => {
					uni.showToast({
						title: '操作成功！'
					})
					this.evaluaObj = {}
					this.evaluaVal = null
					this.evaluaShow = false
					this.$nextTick(() => {
						this.dataList = []
						this.queryList()
					})
				})
			},
			changeTab(item) {
				this.pageNum = 1
				this.dataList = []
				this.orderState = item.val
				this.queryList()
			},
			// 跳转详情
			goDetail(item) {
				if (this.orderTyper == 1) {
					uni.$u.route('/pager/orderDetail/orderDetail', {
						id: item.travelId
					});
				} else if (this.orderTyper == 2) {
					if (!item.travelId) return uni.$u.toast('暂无行程，请先审批')
					uni.$u.route('/pages/map/mapSi', {
						url: "/hybrid/html/privateCar.html",
						orderType: item.orderType,
						id: item.applyId,
					});
				} else if (this.orderTyper == 3) {
					if (!item.travelId) return uni.$u.toast('暂无行程，请先审批')
					if (item.compOrderState == 220 || item.compOrderState == 220) return uni.$u.toast('取消或超时订单无法查看')
					let types = true
					if (item.compOrderState == 9) {
						types = false
					}
					uni.$u.route('/pager/networkCar/networkCar', {
						id: item.travelId,
						type: types
					})
				}

			},
			queryList() {
				if (this.orderTyper == 1) {
					this.getEnterp()
				} else if (this.orderTyper == 2) {
					this.getPrivateCar()
				} else if (this.orderTyper == 3) {
					this.getRideCompany()
				}

			},
			getRideCompany() {
				applyNetcarlist({
					params: {
						compOrderState: this.orderState,
						pageNum: this.pageNum,
						pageSize: this.pageSize,
					}
				}).then((data) => {
					data.pageList.forEach(vr => {
						if (vr.throughAddrInfo) {
							vr.throughAddrInfo = JSON.parse(vr.throughAddrInfo)
							this.$set(vr, 'throughAddrInfoName', this.througWash(vr.throughAddrInfo))
						}
					})
					if (this.pageNum == 1) {
						this.dataList = data.pageList
					} else {
						this.dataList.push(...data.pageList)
					}
					this.hasNext = data.hasNext
					if (data.hasNext) {
						this.status = 'loadmore'
					} else {
						this.status = 'nomore'
					}
				})
			},
			getPrivateCar() {
				privateorderlist({
					params: {
						compOrderState: this.orderState ? this.orderState : 1,
						pageNum: this.pageNum,
						pageSize: this.pageSize,
						content: this.keyword,
					}
				}).then((data) => {
					data.pageList.forEach(vr => {
						if (vr.throughAddrInfo) {
							vr.throughAddrInfo = JSON.parse(vr.throughAddrInfo)
							this.$set(vr, 'throughAddrInfoName', this.througWash(vr.throughAddrInfo))
						}
					})
					if (this.pageNum == 1) {
						this.dataList = data.pageList
					} else {
						this.dataList.push(...data.pageList)
					}
					this.hasNext = data.hasNext
					if (data.hasNext) {
						this.status = 'loadmore'
					} else {
						this.status = 'nomore'
					}
				})
			},
			getEnterp() {
				comporderlist({
					params: {
						compOrderState: this.orderState,
						pageNum: this.pageNum,
						pageSize: this.pageSize,
						content: this.keyword,
					}
				}).then((data) => {
					data.pageList.forEach(vr => {
						if (vr.throughAddrInfo) {
							vr.throughAddrInfo = JSON.parse(vr.throughAddrInfo)
							this.$set(vr, 'throughAddrInfoName', this.througWash(vr.throughAddrInfo))
						}
					})
					if (this.pageNum == 1) {
						this.dataList = data.pageList
					} else {
						this.dataList.push(...data.pageList)
					}
					this.hasNext = data.hasNext
					if (data.hasNext) {
						this.status = 'loadmore'
					} else {
						this.status = 'nomore'
					}
				})
			},
			througWash(arrs) {
				return arrs.map(v => v
						.siteAddrName)
					.join(
						' → ')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.apply {
		.nav_box {
			background-color: #fff;
		}

		.tab_top {
			width: 520rpx;
			margin: 23rpx auto;

			.tab_top_btn {
				width: 133rpx;
				height: 53rpx;
				line-height: 53rpx;
				text-align: center;
				border-radius: 30rpx;
				font-size: 28rpx;
			}

			.tab_top_btn.active {
				background-color: #346CF2;
				color: #fff;
			}
		}

		.tabpage {

			.app_list {
				background-color: #fff;
				margin: 14rpx 11rpx;
				border-radius: 11rpx;
				padding: 30rpx;
				font-size: 28rpx;
				position: relative;

				.jiao_img {
					position: absolute;
					left: 0;
					top: 0;
				}

				.appbtnbox {
					position: absolute;
					right: 27rpx;
					bottom: 27rpx;
				}

				.appbtn {
					background-color: #346CF2;
					padding: 4rpx 18rpx;
					font-size: 28rpx;
					color: #fff;
					border-radius: 8rpx;
					margin-left: 30rpx;
				}

				.colors {
					background-color: #fff !important;
					border: 1px solid #aa00ff;
					color: #aa00ff !important;
				}



				.icon-right {
					position: absolute;
					top: 50%;
					right: 27rpx;
					margin-top: -16rpx;
				}

				.tab_icon {
					margin-right: 16rpx;
				}

				.text-dh {
					font-size: 24rpx;
					color: #999999;
				}

				.text-rt {
					color: #346CF2;
				}

				.app_t_line {
					margin-top: 14rpx;
				}

				.spot {
					width: 16rpx;
					height: 16rpx;
					background-color: #239EFC;
					border-radius: 50%;
					margin: 0 24rpx 0 8rpx;
				}

				.spottwo {
					background-color: #FF7031;
				}

				.spotthree {
					background-color: #28D79F;
				}
			}
		}

		.evalua {
			height: 30%;
			display: flex;
			flex-direction: column;
			align-items: center;
			margin: 80rpx;

			.evaluaText {
				margin-bottom: 40rpx;
			}
		}

		.ui-list {
			padding: 0 30rpx;
			list-style: none;

			li {
				height: 80rpx;
				line-height: 80rpx;
				border-bottom: 1px solid #E9ECF7;

				.ui-list-info {
					display: flex;
					justify-content: space-between;
				}

				.ui-nowrap {
					font-weight: inherit;
					font-size: 14px;
					width: 70%;
				}

				.mileage_input {
					color: #346CF2;
					font-size: 14px;
					height: 80rpx;
				}
			}
		}

		.popupbox {
			.popupbox_top {
				font-size: 28rpx;
				height: 102rpx;
				padding: 0 32rpx;
				// border-bottom: 1px solid #E9ECF7;
				justify-content: space-between;

				.define {
					color: #346CF2;
				}
			}

			.radio_type {
				padding: 24rpx 0;

				.u-radio-group {
					justify-content: space-around;
				}

				.u-radio {
					padding: 18rpx 28rpx;
					border: 1px solid #999999;
					border-radius: 7rpx;
				}

				.u-radio.active {
					border: 1px solid #346CF2;
				}
			}

			.list_one {
				margin: 10rpx 32rpx;
				border: 1rpx solid #dadbde;
				padding: 10rpx;
				border-radius: 10rpx;
			}

			.list_two {
				margin: 10rpx 32rpx;
				overflow-y: auto;
			}

			.inputBox {
				padding: 20rpx 0;

				// border-top: 1px solid #E9ECF7;
				.s_input_box {
					width: 200rpx;
					margin-left: 10rpx;
					// margin-right: 50rpx;
				}

				.s_input {
					padding: 0 9px !important;
				}

				.s_btn_box {
					.del {
						background-color: #999999;
						border-color: #999999;
						// height: 100rpx;
					}

					.yes {
						// height: 100rpx;
					}
				}
			}

			.inputBoxTwo {
				padding: 13rpx 0;
				padding: 13rpx 26rpx;
				border: 1rpx solid #dadbde;
				border-radius: 10rpx;
				margin-bottom: 10rpx;

				.s_input_box {
					width: 200rpx;
					margin-left: 10rpx;
					margin-right: 5rpx;
				}

				.s_input {
					padding: 0 9px !important;
				}

				.s_btn_box {
					.del {
						background-color: #999999;
						border-color: #999999;
						// height: 100rpx;
					}

					.yes {
						// height: 100rpx;
					}
				}
			}
		}

		.popupcbox {
			padding: 0 53rpx;
		}

		.select_input {
			height: 60rpx;
			border: 1px solid #CCCCCC;
			border-radius: 8rpx;
			padding-left: 20rpx;
			line-height: 60rpx;
			color: #999;
			margin: 20rpx 0;
		}

		.selectbox {
			padding: 0 53rpx;
			font-size: 28rpx;
		}

		.popup_btn_box {
			padding-bottom: 27rpx;
			margin-top: 30rpx;
		}

		.popup_tit {
			font-size: 36rpx;
			text-align: center;
			padding: 40rpx 0 30rpx 0;
		}

		.fonts {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.popup_txt {
			font-size: 28rpx;
			text-align: center;
			margin-bottom: 40rpx;
		}

		// /deep/.u-popup__content {
		// 	width: 84%;
		// }

		.popup_btn {
			width: 230rpx;
			height: 80rpx;

			/deep/ .u-button__text {
				font-size: 36rpx !important;
			}
		}

		.two {
			color: #666666 !important;
		}

	}

	.filterBox {
		margin-top: 20rpx;
		padding: 0 32rpx;

		position: sticky;
		top: 10rpx;
		z-index: 33;
	}

	.filter_name {
		font-size: 24rpx;
		padding: 0 20rpx;
		background: #fff;
		margin-right: 20rpx;
		border-radius: 10rpx;
		height: 68rpx;
		line-height: 68rpx;
	}

	.search {
		/deep/.u-search__content {
			background-color: #E9ECF7 !important;
		}

		/deep/.u-search__content__input {
			background-color: #E9ECF7 !important;
		}
	}
</style>