<!doctype html>
<html>
	<head>
		<meta charset="utf-8">
		<title>行程</title>
		<meta name="keywords" content="" />
		<meta name="description" content="" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
		<meta name="format-detection" content="telephone=no" />
		<!-- 	<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black"> -->
		<meta name="author" content="CSS5, css5.com.cn" />
		<link rel="stylesheet" href="http://i.gtimg.cn/vipstyle/frozenui/2.0.0/css/frozen.css">
		<link rel="stylesheet" href="css/font-awesome.min.css" />
		<link rel="stylesheet" type="text/css" href="css/hybird.css">
		<script src="https://cdn.bootcss.com/vue/2.6.11/vue.js"></script>
		<script type="text/javascript">
			window._AMapSecurityConfig = {
				securityJsCode: 'a0ec29ceac10861c863c75bcf78565a4',
			}
		</script>

		<script src="https://webapi.amap.com/loader.js"></script>
		<!-- 引入样式和js文件 -->
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		<script type="text/javascript" src="js/axios.js"></script>
	</head>
	<style>
		/* body {
			overflow: hidden !important;
			touch-action: none !important;
		} */
	</style>
	<body>
		<div id="app">
			<div class="backbtn" @click="goBack()">
				<i class="fa fa-angle-left"></i>
			</div>
			<div class="mapdown">
				<div class="mapdown-top">
					订单正在进行中，正在前往目的地
				</div>

				<div class="mapdown-down-apl">
					<ul class="ui-list ui-list-function aplbox">
						<li class="mapdownbox">
							<div class="ui-avatar posIcon">
								<span style="background-image:url(http://placeholder.qiniudn.com/100x100)"></span>
							</div>
							<div class="ui-list-info">
								<h5 class="ui-nowrap">京A A0022蓝</h5>
								<p>奥迪 A6L 黑色</p>
							</div>
							<div class="aplavat">
								<div class="ui-avatar aplavatimg">
									<span style="background-image:url(http://placeholder.qiniudn.com/100x100)"></span>
								</div>
								<div>
									李大婶
								</div>

							</div>
						</li>
					</ul>
					<div class="d_txt ui-row-flex ui-whitespace">
						<div class="ui-col ui-col-2">
							行程单号：L2021020211222330004
						</div>
						<div class="ui-col text-right">
							<span>订单详情 <i class="fa fa-angle-double-right"></i></span>
						</div>
					</div>
					<div class="d_txt ui-row-flex ui-whitespace">
						<div class="ui-col">
							<i class="fa fa-clock-o"></i>
							7月7日 9:00 - 7月9日 08:45
						</div>
					</div>

				</div>

				<div class="u-flex aplbtn_box">
					<div class="u-flex-8 u-m-r-8">
						<button class="ui-btn-lg ui-btn-primary pribtn ">结束</button>
					</div>
					<div class="u-flex-4">
						<button class="ui-btn-lg ui-btn-primary cancelbtn">导航</button>
					</div>
					<!-- <div class="u-flex-12">
						<button class="ui-btn-lg ui-btn-primary pribtn ">开始</button> -->
				</div>
			</div>

		</div>

		<div id="container"></div>

		<!-- 组件 -->
		<div class="ui-dialog">
			<div class="ui-dialog-cnt-m">
				<div class="popup_tit">
					是否开始用车
				</div>
				<div class="u-flex u-row-between popup_btn_box">
					<button class="popup_btn_yes">确定</button>
					<button class="popup_btn_no">取消</button>
				</div>
			</div>
		</div>

		<div class="ui-dialog show">
			<div class="ui-dialog-cnt-m">
				<div class="mileage_tit">
					里程费用确认
				</div>
				<div class="mileage_tips">
					如订单行驶里程有差异，可按实际情况自行修改；
				</div>
				<ul class="ui-list ui-list-single ui-border-b">
					<li>
						<div class="ui-list-info">
							<h5 class="ui-nowrap">预估里程（公里）：</h5>
							<div class="ui-txt-info u-m-r-15">109.8</div>
						</div>
					</li>

					<li>
						<div class="ui-list-info">
							<h5 class="ui-nowrap">实际里程（公里）：</h5>
							<div class="ui-txt-info">
								<input class="mileage_input" value="109.8" type="text">
								<i class="fa fa-edit"></i>
							</div>
						</div>
					</li>
				</ul>

				<div class="mileage_tit_s">
					订单费用：
				</div>

				<ul class="ui-list ui-list-single ui-border-t u-m-b-10">
					<li class="ui-border-t">
						<div class="ui-list-info">
							<h5 class="ui-nowrap">补贴费（1元/公里）：</h5>
							<div class="ui-txt-info u-m-r-15">109.8</div>
						</div>
					</li>

					<li class="ui-border-t">
						<div class="ui-list-info">
							<h5 class="ui-nowrap">停车费（元）：</h5>
							<div class="ui-txt-info u-m-r-15">
								<input class="mileage_input" value="109.8" type="text" placeholder="请输入实际产生费用">
							</div>
						</div>
					</li>

					<li class="ui-border-t">
						<div class="ui-list-info">
							<h5 class="ui-nowrap">高速费（元）：</h5>
							<div class="ui-txt-info u-m-r-15">22.20</div>
						</div>
					</li>

					<li class="ui-border-t">
						<div class="ui-list-info">
							<h5 class="ui-nowrap">总费用（元）：</h5>
							<div class="ui-txt-info u-m-r-15" style="color: #262626;">109.8</div>
						</div>
					</li>
				</ul>

				<div class="u-flex u-row-between popup_btn_box">
					<button class="popup_btn_yes">确定</button>
					<button class="popup_btn_no">取消</button>
				</div>

			</div>
		</div>


		</div>
	</body>
	<!-- 微信 JS-SDK 如果不需要兼容小程序，则无需引用此 JS 文件。 -->
	<script type="text/javascript" src="//res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
	<!-- uni 的 SDK -->
	<script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script>
	<script>
		document.addEventListener('UniAppJSBridgeReady', function() {});
	</script>

	<script>
		new Vue({
			el: '#app',
			data: {
				currentPos: "加载中...",
				objective: "",
				listdata: [],
				isshow: false,
				starcount: 5,
				star: 0
			},
			computed: {
				heightStyle() {
					return {
						height: this.currentHeight + 'vh',
					}
				},
			},
			mounted() {
				this.init()
			},
			methods: {
				init() {
					let that = this
					// 高德地图控件
					AMapLoader.load({
						"key": "1cb53fa0e69dc44036161409f1d4039c", // 申请好的Web端开发者Key，首次调用 load 时必填
						"version": "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
						"plugins": ['AMap.Driving'], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
						"AMapUI": { // 是否加载 AMapUI，缺省不加载
							"version": '1.1', // AMapUI 版本
							"plugins": ['overlay/SimpleMarker'], // 需要加载的 AMapUI ui插件
						},
						"Loca": { // 是否加载 Loca， 缺省不加载
							"version": '2.0' // Loca 版本
						},
					}).then((AMap) => {
						///基本地图加载
						var map = new AMap.Map("container", {
							resizeEnable: true,
							zoom: 13 //地图显示的缩放级别
						});
						//构造路线导航类
						var driving = new AMap.Driving({
							map: map
						});
						// 根据起终点经纬度规划驾车导航路线
						driving.search(new AMap.LngLat(116.379028, 39.865042), new AMap.LngLat(116.427281,
							39.903719), function(status, result) {
							// result 即是对应的驾车导航信息，相关数据结构文档请参考  https://lbs.amap.com/api/javascript-api/reference/route-search#m_DrivingResult
							if (status === 'complete') {
								//构建自定义信息窗体
								var position = [116.427281, 39.903719];
								//实例化信息窗体
								var distance = '方恒假日酒店';
								var time = '20';
								var infoWindow = new AMap.InfoWindow({
									isCustom: true, //使用自定义窗体
									content: that.createInfoWindow(distance, time),
									offset: new AMap.Pixel(0, -40)
								});


								infoWindow.open(map, position);

							} else {

							}
						});

					}).catch((e) => {
						console.error(e); //加载错误提示
					});

				},

				goBack(item) {
					uni.navigateBack();
				},

				//构建自定义信息窗体
				createInfoWindow(distance, time) {
					var info = '<div class="mapTip">' +
						'<div class="mapTipt">' +
						'预计里程<span>200</span>公里' +
						'</div>' +
						'<div class="mapTipd">' +
						'预估用时<span>2小时13分钟</span>' +
						'</div>' +
						'</div>';
					return info;
				},

				switchFun() {
					this.isshow = !this.isshow
				},

				clickStars(i) {
					this.star = i + 1
				},


			}
		});
	</script>

</html>
