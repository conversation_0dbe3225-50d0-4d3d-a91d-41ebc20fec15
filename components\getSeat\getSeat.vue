<template>
	<view class="getSeatr">
		<u-navbar title="选择定位" :autoBack="false" @leftClick="closeBtn"></u-navbar>
		<view class="seat-search  u-flex">
			<view class="search-bar u-flex">
				<text class="search-bar-text" @click="currentOpen">{{currentPos}}</text>
				<u-icon name="arrow-down" size="14" color='#000'></u-icon>
				<view class="line-s"></view>
				<view class="search-bar-input">
					<input v-model="keywords" @input="aMapSearchNearBy" placeholder="请输入要搜索的地址" autocapitalize="off">
				</view>
			</view>
		</view>
		<view class="seat-conten">
			<view class="seat-item u-flex" v-for="item in seatList" :key="item.id" @click="itemClick(item)">
				<u-icon name="https://zqcxdev.di-digo.com/app/image/weizir.png" size="35"></u-icon>
				<view class="seat-item-right  u-flex-direction">
					<span class="seat-item-name"> {{item.name}}</span>
					<span class="seat-item-address"> {{`${item.address}`}}</span>
				</view>
			</view>
			<u-empty v-if="seatList.length == 0" text="搜索地址为空" icon="http://cdn.uviewui.com/uview/empty/order.png">
			</u-empty>
		</view>

		<getArea v-if="getAreaShow" @AreaBtn='AreaBtn' :cityr='currentPos'></getArea>
	</view>
</template>

<script>
	import getArea from '../getArea/getArea.vue'
	import {
		getGdparameters,
	} from '@/config/api.js';
	export default {
		name: "getSeat",
		components: {
			getArea
		},
		props: ['seatIndex'],
		data() {
			return {
				currentPos: '加载中...',
				keywords: '',
				seatList: [],
				regeocoder: {},
				getAreaShow: false,
			};
		},
		mounted() {
			let that = this

			uni.showLoading({
				title: '正在定位',
				mask: true
			})

			uni.getLocation({
				type: 'gcj02',
				success: function(res) {
					let obj=`${res.longitude},${res.latitude}`
					uni.request({
						url: 'https://restapi.amap.com/v3/geocode/regeo',
						method: 'GET',
						data: {
							location: res.longitude + ',' + res.latitude,
							key: 'bf69cc6accad992ab11ab314032ad3bd', //逆解析专用key
						},
						success(resr) {
							if (resr.statusCode == 200) {
								// alert(resr.data.regeocode.formatted_address)
								let data = resr.data.regeocode.addressComponent
								data.adcode = data.adcode.substr(0, 4) + "00"
								that.currentPos = data.city
								that.regeocoder = data
								that.getOne()
							} else {
								uni.$u.toast('获取位置请手动选择')
								uni.hideLoading()
							}
						},
						fail(err) {
							uni.$u.toast('获取位置请手动选择')
							uni.hideLoading()
						}
					})
				}
			});

		},
		methods: {
			itemClick(item) {
				let objr = {
					cityCode:item.adcode,
					location: item.location,
					pcode: item.pcode,
					name: item.name,
					address: item.address,
					cityname: item.cityname,
					idx: this.seatIndex
				}
				this.$emit("seatClose", objr)
			},
			aMapSearchNearBy() {
				let objr = {
					region: this.currentPos,
					keywords: this.keywords,
				}
				getGdparameters(objr).then(res => {
					this.seatList = res ? res : []
					uni.hideLoading()
				})
			},
			currentOpen() {
				this.getAreaShow = true
			},
			closeBtn() {
				this.$emit("seatClose")
			},
			AreaBtn(item) {
				if (item) {
					this.$set(this, 'currentPos', item.city_name)
					this.$set(this, 'keywords', ' ')
					this.getOne()
					this.getAreaShow = false
				} else {
					this.getAreaShow = false
				}
			},

			getOne() {
				let objr = {
					region: this.currentPos,
					keywords: ' ',
				}
				getGdparameters(objr).then(res => {
					this.seatList = res ? res : []
					uni.hideLoading()
				})
			},
		}
	}
</script>

<style scoped>
	.getSeatr {
		transition-duration: 350ms;
		transition-timing-function: ease-out;
		position: fixed;
		inset: 0px;
		z-index: 1000;
		background-color: #F9FAFE;
	}

	.seat-conten {
		overflow-y: auto;
		width: 100%;
		/* #ifdef H5 */
		height: calc(100vh - 188rpx);
		margin-top: 188rpx;
		/* #endif */
		/* #ifdef MP-WEIXIN */
		margin-top: 280rpx;
		height: calc(100vh - 280rpx);
		/* #endif */
		background: #fff;
	}


	.seat-search {
		position: absolute;
		height: 100rpx;
		background: #F9FAFE;
		width: 100%;
		/* #ifdef H5 */
		top: 88rpx;
		/* #endif */
		/* #ifdef MP-WEIXIN */
		top: 180rpx;
		/* #endif */
	}

	.search-bar {
		display: flex;
		background-color: #f3f3f3;
		padding: 12rpx 20rpx;
		width: 100%;
		margin: 0 30rpx;
		border-radius: 60rpx;
		font-size: 26rpx;
	}

	.search-bar-text {
		width: 120rpx;
		padding: 0;
		text-align: center;
		max-width: 100%;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.seat-item {
		margin: 20rpx;
		padding: 10rpx 20rpx;
	}

	.seat-item-right {
		display: flex;
		margin-left: 30rpx;
	}

	.seat-item-name {
		font-size: 28rpx;
	}

	.seat-item-address {
		font-size: 24rpx;
		color: #777;
		line-height: 60rpx;
	}

	.line-s {
		width: 4rpx;
		height: 30rpx;
		display: inline-block;
		background-color: #B5BCD6;
		margin: 0 16rpx;
	}

	.search-bar-input {
		flex: 1;
	}

	/deep/.uni-input-placeholder {
		font-size: 26rpx;
	}

	/deep/.uni-input-input {
		font-size: 26rpx;
	}
</style>