import Vue from 'vue'
import Vuex from 'vuex'
import persistedState from "vuex-persistedstate"
Vue.use(Vuex)
const store = new Vuex.Store({
	plugins: [
			persistedState({
				storage: {
					getItem: key => uni.getStorageSync(key),
					setItem: (key, value) => uni.setStorageSync(key, value),
					removeItem: key => uni.removeStorageSync(key)
				}
			})
		],
	state: {
		userInfo: {},
		hasLogin: false,
		dicVoList: [],
		jurisdicList: {},
		consoleMenu:''
	},
	mutations: {
		login(state, provider) { //改变登录状态  
			state.hasLogin = true
			state.userInfo = provider
			uni.setStorageSync('userInfo', JSON.stringify(provider));
		},

		setMenu(state, value) {
			state.consoleMenu = value
		},
		// 代码词典
		setDicr(state, arrs) {
			state.dicVoList = arrs
			uni.setStorageSync('dicVoList', JSON.stringify(arrs));
		},

		setJurisdic(state, arrs) {
			state.jurisdicList = arrs
			uni.setStorageSync('jurisdicList', JSON.stringify(arrs));
		},

		//退出登录    务必清除登录状态 以及 本地用户信息，防止下次打开app重现、以及出现不正确状态
		logout(state) {
			state.hasLogin = false
			state.userInfo = {}
			state.dicVoList = []
			state.jurisdicList = {}
			uni.removeStorageSync('userInfo');
			uni.removeStorageSync('dicVoList');
		}

	}
})
export default store