<template>
	<view class="loginr">
		<u-toast ref="uToast"></u-toast>
		<u-navbar title="登录" :autoBack="true" :placeholder="true">
			<span slot="left"></span>
		</u-navbar>
		<!-- 注意，如果需要兼容微信小程序，最好通过setRules方法设置rules规则 -->
		<view class="logo u-center">
			<u--image :showLoading="true" src="/static/blogo.png" width="357rpx" height="146rpx"></u--image>
		</view>

		<view class="loginForm">
			<u--form labelPosition="left" :model="loginmodel" ref="loginform" labelWidth="0">
				<u-form-item prop="name" class="formitem">

					<u--input v-model="loginmodel.name" border="none" placeholder="请输入手机号" id="inputr"
						@focus="inputFocus" @blur="inputBlur" :focus="firstFocus">
					</u--input>

					<view class="select" :style="[styler]" v-if="loginList.length!=0 ">
						<block v-for="(item,i) in loginList" :key="i">
							<span class="selectbox" v-if="spanShowr">
								<text @click="selectTion(item)" style="flex: 1;">{{item}}</text>
								<u-icon name="close" @click="delTion(item)"></u-icon>
							</span>
						</block>
					</view>
				</u-form-item>


				<u-form-item prop="password" class="formitem">
					<u--input v-model="loginmodel.password" border="none" :password="lookShow" placeholder="请输入登录密码">
					</u--input>
					<template slot="right">
						<i class="iconfont icon-kejian" v-if="lookShow" @click="lookShow=!lookShow"></i>
						<i class="iconfont icon-bukejian" v-else @click="lookShow=!lookShow"></i>
					</template>
				</u-form-item>
				<u-form-item prop="captchaCode" class="formitem codeitem">
					<u--input v-model="loginmodel.captchaCode" border="none" placeholder="请输入验证码"></u--input>
					<u--image slot="right" :showLoading="true" :src="codeimgsrc" width="80px" height="40px"
						@click="changeCode()"></u--image>
				</u-form-item>

			</u--form>
			<u-button type="primary" color="#346CF2" text="登录" customStyle="margin-top: 50px" @click="submit">
			</u-button>

			<!-- #ifdef MP-WEIXIN -->
			<u-button type="primary" color="#c4c4c5" text="一键授权登录" customStyle="margin-top: 10px" @click="getLogin">
			</u-button>
			<!-- #endif -->
		</view>

		<!-- #ifdef MP-WEIXIN -->
		<u-popup :show="phoneShow" mode="center" :round="10" @close="()=>{phoneShow=false}">
			<view class="get-phone">
				<view class="p2">申请获取您绑定的手机号</view>
			</view>
			<view class="u-safe-bottom">
				<button class="phone-button" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
					同 意
				</button>
			</view>
		</u-popup>
		<!-- #endif -->

	</view>
</template>

<script>
	import {
		mapMutations,
	} from 'vuex';
	import {
		loginFun,
		wechatLoginFun,
		getcodeSession,
		getCaptcha,
		getuserinfo,
		appgivecod,

	} from '@/config/api.js';
	import WXBizDataCrypt from "@/common/WXBizDataCrypt.js"
	import {
		SET_TOKEN,
		GET_TOKEN,
		REMOVE_TOKEN
	} from '@/common/token.js'
	export default {
		data() {
			return {
				lookShow: true,
				codeimgsrc: "",
				codeIdr: null,
				loginList: [],
				loginmodel: {
					// 17711111111
					// 18777777777
					// 17733333333
					// 18650098048
					// 18889023006
					// 16666666662
					// 16666666668   123456
					// 18889023001   123456
					// 18886662220
					name: '',
					captchaCode: '',
					captchaId: '',
					password: '',
					opneId: '',
				},
				inputShowed: true,
				rules: {
					name: [{
							required: true,
							message: '请输入手机号',
							trigger: ['change', 'blur'],
							validator: (rule, value, callback, bnts) => {
								if (!this.loginmodel.name) {
									return false
								} else {
									return true
								}
							}
						},
						{
							validator: (rule, value, callback) => {
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							trigger: ['change', 'blur'],
						}
					],
					password: [{
						required: true,
						message: '请输入登录密码',
						trigger: ['blur']
					}],
					captchaCode: [{
						required: true,
						message: '请输入验证码',
						trigger: ['blur']
					}],

				},
				styler: {},
				firstFocus: false,
				spanShowr: false,
				phoneShow: false,
				userInfo: {}, //用户信息
				userCts: {} //用户秘钥
			}
		},
		onReady() {
			// 如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则
			this.$refs.loginform.setRules(this.rules)
		},

		computed: {
			getNumber() {
				return this.loginmodel.name
			}
		},
		watch: {
			getNumber(newVal, oldVal) {
				if (newVal && newVal.length == 11) {
					this.inputBlur()
				}
			}
		},

		onLoad() {
			let v = this.$common.getItem('userInfo')
			// alert(JSON.stringify(`${v.cacheRoleType}---${v.appTerminal}`))

			// 如果是登录状态直接跳转首页
			if (this.$common.getItem('userInfo')) {
				if (this.$common.getItem('userInfo').cacheRoleType == '2') {
					uni.$u.route('/pageDriver/driveIndex/driveIndex');
				} else if (this.$common.getItem('userInfo').cacheRoleType == '1') {
					uni.switchTab({
						url: '/pages/wayPassenger/index/index'
					})
				} else {
					uni.$u.route('/pages/login/selectRole/selectRole');
				}
			} else {
				uni.$u.route('/pages/login/login');
			}
			this.changeCode()
			// #ifdef H5
			this.codeTion()
			// #endif
			this.huoqu()
		},
		methods: {
			getAppid(data) {
				let that = this
				getcodeSession({
					params: {
						jsCode: data.code
					}
				}).then(cts => {
					let objr = {
						'session_key': cts.sessionKey,
						code: data.code,
						openid: cts.openid,
						zqCode: cts.zqCode,
					}
					that.userCts = objr
					that.phoneShow = true
				})
				// uni.request({
				// 	url: 'https://api.weixin.qq.com/sns/jscode2session',
				// 	method: 'GET',
				// 	data: {
				// 		appid: 'wxff2f3f97c4c7252a',
				// 		secret: '3526c73b6888d014aa205381ecc447b6',
				// 		js_code: data.code,
				// 		grant_type: 'authorization_code',
				// 	},
				// 	success: (cts) => {
				// 		cts.data.code = data.code
				// 		that.userCts = cts.data
				// 		that.phoneShow = true
				// 		console.log('获取openid======:', that.userCts)
				// 	}
				// })
			},
			// 3、调用getPhoneNumber
			async getPhoneNumber(e) {
				// console.log('获取手机号码======code:', e.detail)
				// console.log('获取用户信息======code:', this.userInfo)
				// console.log('获取用户隐私======code:', this.userCts)
				let pc = new WXBizDataCrypt('wxff2f3f97c4c7252a', this.userCts.session_key);
				let data = pc.decryptData(e.detail.encryptedData, e.detail.iv);
				this.userInfo.phoneNumber = data.phoneNumber
				this.getLoginr()
			},
			getLoginr() {
				wechatLoginFun({
					mobile: this.userInfo.phoneNumber,
					openId: this.userCts.openid,
					zqCode: this.userCts.zqCode,
				}).then(res => {
					this.initr()
					this.login(res)
					this.getTyper(res)
					this.changeCode()
					this.loginChange()
				})
				this.phoneShow = false
			},
			getLogin() {
				uni.showModal({
					title: '授权登录',
					content: '是否授权登录微信小程序？',
					success: () => {
						// 1、uni.getUserProfile 获取用户信息 
						uni.getUserProfile({
							desc: '登录后可同步数据',
							lang: 'zh_CN',
							success: (infoRes) => {
								this.userInfo = infoRes.userInfo;
								uni.login({
									provider: 'weixin',
									success: async (loginRes) => {
										// console.log(loginRes, 'loginRes');
										this.getAppid(loginRes)
									},
									fail: function(err) {}
								});
							},
							fail: function(err) {}
						});
					}
				})
			},
			firstChange() {
				this.firstFocus = false; // 每次都要初始化 focus 属性
				setTimeout(() => {
					this.firstFocus = true; //自动获取焦点
				}, 0)
			},
			delTion(nubr) {
				uni.showModal({
					content: `确定删除${nubr}的账号记录？`,
					success: (res) => {
						if (res.confirm) {
							this.loginList.forEach((v, index) => {
								if (nubr == v) {
									this.loginList.splice(index, 1)
									uni.$u.toast('操作成功')

								}
							})
							this.setSynsr()
						}
					}
				})


			},
			selectTion(nubr) {
				this.$nextTick(() => {
					this.$set(this.loginmodel, 'name', nubr)
					this.inputBlur()
					this.firstChange()
				})
			},
			inputFocus() {
				this.spanShowr = true
				if (!this.firstFocus) {
					this.styler = {
						// "max-height": `${this.loginList.length*40}px`,
						"max-height": `${this.loginList.length*40*2+4}rpx`,
						'z-index': 9999,
						'background-color': '#ffffff',
					}
				}

			},
			inputBlur() {
				this.styler = {
					// 'height':0,
					"max-height": 0,
					'z-index': 0,
				}
				this.firstFocus = false

				setTimeout(() => {
					this.spanShowr = false
				}, 200)
			},

			loginChange() {
				let attr = uni.getStorageSync('userList') ? JSON.parse(uni.getStorageSync('userList')) : []
				if (!attr.includes(this.loginmodel.name)) {
					if (this.loginmodel.name == '') return
					console.log(attr.length < 3, 'attr')
					if (attr.length < 3) {
						this.loginList.splice(0, 0, this.loginmodel.name)
					} else {
						this.loginList.splice(2, 1)
						this.loginList.splice(0, 0, this.loginmodel.name)
					}
				}
				this.setSynsr()
			},
			huoqu() {
				this.loginList = uni.getStorageSync('userList') ? JSON.parse(uni.getStorageSync('userList')) : []
				console.log(this.loginList);
			},
			setSynsr() {
				uni.setStorageSync('userList', JSON.stringify(this.loginList))
				this.huoqu()
			},

			...mapMutations(['login', 'setDicr', 'setJurisdic']),
			codeTion() {

				let that = this
				let text = window.location.search
				if (text) {
					let id = text.split("=")[1].split("&")[0]
					appgivecod({
						code: id
					}).then(res => {
						that.codeIdr = res
					})
				}
			},
			changeCode() {
				getCaptcha({}).then(data => {
					this.codeimgsrc = data.captchaCode
					this.loginmodel.captchaId = data.captchaId
					this.loginmodel.captchaCode = null
				})
			},

			submit() {
				this.$refs.loginform.validate().then(res => {
					let param = {
						captchaCode: this.loginmodel.captchaCode,
						captchaId: this.loginmodel.captchaId,
						loginMethod: "1",
						password: this.loginmodel.password,
						userName: this.loginmodel.name,
						openId: this.codeIdr
					}

					loginFun(param, {
						custom: {
							catch: true,
							toast: false
						}
					}).then(data => {
						SET_TOKEN(data.accessToken || '')
						this.initr()
						this.login(data)
						this.getTyper(data)

						this.changeCode()
						this.loginChange()

						this.loginmodel.captchaCode = null
						this.loginmodel.captchaId = null
						this.loginmodel.password = null
						this.loginmodel.name = null
					}).catch((errors) => {
						let that = this;
						this.$refs.uToast.show({
							message: errors.msg,
							complete() {
								that.changeCode()
							}
						})
					})
				}).catch(errors => {})
			},

			getTyper(data) {
				if (data.appTerminal.includes('2')) {
					// alert(`${data.cacheRoleType}--1`)
					if (data.cacheRoleType == '2') {
						uni.reLaunch({
							url: '/pageDriver/driveIndex/driveIndex'
						})
						// uni.$u.route('/pageDriver/driveIndex/driveIndex');
					} else if (data.cacheRoleType == '1') {
						// uni.reLaunch({
						// 	url: '/pages/wayPassenger/index/index'
						// })
						uni.switchTab({
							url: '/pages/wayPassenger/index/index'
						})
					} else {
						uni.reLaunch({
							url: '/pages/login/selectRole/selectRole'
						})
						// uni.$u.route('/pages/login/selectRole/selectRole');
					}
				} else {
					let v = this.$common.getItem('userInfo')
					// alert(`${data.cacheRoleType}--2--${v.cacheRoleType} ** ${data.appTerminal} **${v.appTerminal}`)
					uni.switchTab({
						url: '/pages/wayPassenger/index/index'
					})
				}
			},
			// 获取代码词典
			initr() {
				getuserinfo({
					param: {}
				}).then(res => {
					this.setDicr(res.dicVoList)
					this.setJurisdic(res)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.loginr {

		.logo {
			margin-top: 124rpx;
			margin-bottom: 98rpx;
		}

		.loginForm {
			padding: 0 90rpx;

			/deep/.u-form-item {
				background-color: #F9FAFE;
				border: 1px solid #CCCCCC;
				padding: 0 27rpx;
				border-radius: 5rpx;
				margin-bottom: 40rpx;
				position: relative;
				height: 80rpx;
				display: flex;
				justify-content: center;
			}

			/deep/.u-form-item__body__right__message {
				position: absolute;
				left: 0;
				bottom: -34rpx;
			}

			.codeitem {
				/deep/.u-form-item__body {
					padding: 2px 0;
				}
			}

		}

		.loginBox {}




		.select {
			overflow: hidden;
			position: absolute;
			background: #ffffff00;
			width: calc(100% - 38rpx);
			left: -1rpx;
			top: 83rpx;
			border-bottom-left-radius: 20rpx;
			border-bottom-right-radius: 20rpx;
			// border: 1px solid #dedede;
			padding: 10rpx 20rpx;
			display: flex;
			flex-direction: column;
			transition: 0.5s;
			max-height: 0;
		}

		.selectbox {
			height: 70rpx;
			line-height: 70rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 20rpx;
			border-bottom: 1px solid #ededed;
		}

		.phone-button {
			// height: 50rpx;
			margin-top: 20rpx;
		}

		/deep/.u-safe-area-inset-bottom {
			padding-bottom: 0
		}

		/deep/.u-popup__content {
			padding: 40rpx;
		}


	}
</style>