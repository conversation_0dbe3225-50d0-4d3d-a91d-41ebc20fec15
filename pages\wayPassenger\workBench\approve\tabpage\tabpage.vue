<template>
	<view>
		<view class="filterBox">
			<u-search class="search" placeholder="搜索订单" :showAction="false" v-model="keyword" @search="searchFun">
			</u-search>
		</view>
		<scroll-view class="scroll-content" scroll-y="true" refresher-enabled="true" :refresher-triggered="triggered"
			:refresher-threshold="100" refresher-background="#F9FAFE" @scrolltolower="onReflowe"
			@refresherrefresh="onRefresh">
			<view class="app_list u-flex" v-for="(item,index) in dataList" :key="index">
				<template v-if="useCarType==2">
					<networkCar :item='item' :index="index" :tipsList='tipsList' @showType="showType"
						@tipsSelect='tipsSelect' :tabtype='tabtype' @goClick='goClick' style="width: 100%;"></networkCar>
				</template>
				<template v-else>
					<view class="jiao_img">
						<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/qi.png"
							v-if="item.travelType==1 &&useCarType!=4">
						</u-image>
					</view>
					<view class="jiao_img">
						<u-image class="" :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/zu.png"
							v-if="item.travelType==2&&useCarType!=4">
						</u-image>
					</view>
					<view class="jiao_img">
						<u-image class="" :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/si.png"
							v-if="useCarType==4">
						</u-image>
					</view>

					<view class="radio-b-r u-flex-11">
						<view class="" @click="goDetail(item)">
							<view class="icon-right">
								<u-icon name="arrow-right"></u-icon>
							</view>
							<view class="u-flex u-row-between">
								<view class="u-flex">
									<span class="text-dh">申请单号：{{item.applyCode}}</span>
								</view>
								<!-- <view class="text-rt" v-if="tabtype==3">
								{{item.stepReject==1?'逐级驳回':item.stepReject==2?'驳回申请人':"已驳回"}}
							</view>
							<view class="text-rt" v-else>
								{{tabtype==1?"待审批":tabtype==2?"已通过":""}}
							</view> -->
								<view class="text-rt" v-if="useCarType!=4">
									{{orderWash(item.compOrderState)}}
								</view>

							</view>

							<view class="app_t_line u-flex">
								<u-image :width="14" :height="14"
									src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon.png"></u-image>
								<span
									class="tab_icon">{{new Date(item.reserveStartTime).getTime()| date('mm月dd日 hh:MM')}}
									-
									{{new Date(item.reserveEndTime).getTime() | date('mm月dd日 hh:MM')}}</span>
							</view>

							<view class="app_t_line u-flex">
								<view class="spot"></view>
								<span>{{item.fromAddrName}}</span>
							</view>

							<view class="app_t_line u-flex"
								v-if="item.throughAddrInfo && item.throughAddrInfo.length > 0">
								<view class="spot" style="background-color: chartreuse;"></view>
								<span style="width: 90%;">{{item.throughAddrInfoName}}</span>
							</view>

							<view class="app_t_line u-flex">
								<view class="spot" style="background-color: #757575;"></view>
								<span>{{item.toAddrName}}</span>
							</view>

							<view class="app_t_line u-flex" v-if="item.psgNums">
								<u-image :width="14" :height="14"
									src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png">
								</u-image>
								<span class="tab_icon">乘车人数：{{item.psgNums}}</span>
							</view>

							<!-- <view class="app_t_line u-flex">
							<u-image class="tab_icon" :width="14" :height="14"  src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon2.png"></u-image>
							<span>是否匹配司机：{{item.isAllotDriver==1? "是" : "否"}}</span>
						</view> -->

							<view class="app_t_line u-flex" v-if="item.wantCarTypeFullName">
								<u-image :width="14" :height="14"
									src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png"></u-image>
								<span class="tab_icon">{{item.wantCarTypeFullName}}</span>
							</view>

							<view class="opinion" v-if="item.rejectReason">
								上级驳回：{{item.rejectReason}}
							</view>
						</view>

						<view class="u-flex" style="margin-top: 20rpx;" v-if="tabtype==1">
							<view class="agreebtn"
								v-if="item.approvalButton.isLeaseApprovalButton==1||item.approvalButton.isApprovalButton==1||item.approvalButton.isAppPrivateButton==1">
								<u-button type="primary" color="#5ac725" text="通过" @click="trueClick(item,true)"
									style="height: 50rpx;"></u-button>
							</view>
							<view class="refusebtn"
								v-if="item.approvalButton.isLeaseApprovalRejectedButton==1||item.approvalButton.isApprovalRejectedButton==1||item.approvalButton.isAppPrivateRejectedButton==1">
								<u-button type="primary" color="#cccccc" text="驳回" @click="falseClick(item,false)"
									style="height: 50rpx;"></u-button>
							</view>
						</view>

					</view>
				</template>
			</view>

			<view style="text-align: center;" v-if="total==dataList.length &&dataList.length!=0">———— 到底了 ————</view>

			<u-empty v-if="dataList.length==0" mode="order" text='暂无审批'
				icon="http://cdn.uviewui.com/uview/empty/order.png">
			</u-empty>
		</scroll-view>


		<!-- 驳回弹窗组件 -->
		<u-popup :round="5" :show="rejectshow" mode="center" @close="typeClose(false)" :customStyle="styleObjr">
			<rejectCom v-if="rejectshow" :rejectObj="transmitObjr" :rejectName="rejectName" @adoptFun="adoptFun">
			</rejectCom>
		</u-popup>

		<!-- 审批人下拉框 -->
		<u-picker :show="adoptShow" :columns="transmitObjr.nextListr" keyName="name" @cancel="adoptShow=false"
			@confirm="approvalBtn" @close="adoptShow=false"></u-picker>


		<u-popup :show="tiraShow" mode="center" :round="10" @close="()=>{tiraShow=false}" :customStyle="styleObjr">
			<orderTracking :list="tiraList" v-if="tiraShow">
				<template #conten="{row}">
					{{`${row.operatorName}${row.operatorMobile}`}}
				</template>
			</orderTracking>
		</u-popup>

	</view>
</template>

<script>
	import {
		checkcomporderlist,
		checkcomporderinfo,
		checkcomporder,
		checktoleaseor,
		checkpriceteorder
	} from '@/config/api.js';
	import rejectCom from '../../componentr/rejectCom.vue'
	import networkCar from '../../componentr/networkCar.vue'
	import orderTracking from '../../componentr/orderTracking.vue'
	export default {
		data() {
			return {
				keyword: null,
				triggered: false,
				dataList: [],
				pageNum: 1,
				pageSize: 20,
				compOrderList: [],
				total: 0,
				rejectshow: false,
				adoptShow: false,
				transmitObjr: {},
				styleObjr: {
					width: '85%'
				},
				rejectName: '',
				tipsList: [],
				tiraShow: false,
				tiraList: false,
			}
		},
		components: {
			rejectCom,
			networkCar,
			orderTracking
		},
		props: ["tabtype", "useCarType"],
		watch: {
			tabtype(newVal, oldVal) {
				this.dataList = []
				this.pageNum = 1
				this.queryList()
				this.approvevalId = null
				this.apptravelId = null
			},
			useCarType(newVal, oldVal) {
				this.pageNum = 1
				this.dataList = []
				this.queryList()
			}
		},
		mounted() {
			this.queryList()
			let arrs = this.$common.getItem('dicVoList')
			arrs.forEach(item => {
				if (item.dicCode == "comp_order_state") {
					this.compOrderList = item.dicValueList
				}
			})
		},
		methods: {
			goClick(item) {
				uni.$u.route('/pager/networkCarDetais/networkCarDetais', {
					id: item.applyId,
					type: 2,
				})
			},
			tipsSelect(item, val) {
				if (item.text == '同意申请') {
					this.trueClick(val, true)
				} else if (item.text == '驳回申请') {
					this.falseClick(val, false)
				} else if (item.text == '联系申请') {
					if (!val.mobile) return uni.$u.toast('暂无联系方式')
					uni.makePhoneCall({
						phoneNumber: val.mobile //仅为示例
					});
				} else if (item.text == '订单跟踪') {
					if (!val.orderEventHis) return uni.$u.toast('暂无订单跟踪')
					this.tiraShow = true

					val.orderEventHis.forEach(v => {
						v.titles = `${v.evtDetContent} - ${v.operatorTimes}`
					})
					this.tiraList = val.orderEventHis
				}
			},
			showType(item) {
				let arr = [{
						text: '同意申请',
						icon: 'reload',
						id: 1,
					},
					{
						text: '驳回申请',
						icon: 'close',
						id: 2
					},
					{
						text: '联系申请',
						icon: 'server-fill',
						id: 3
					},
					{
						text: '订单跟踪',
						icon: 'list-dot',
						id: 4
					},
				]

				if (item.approvalButton && item.approvalButton.isNetCarButton == 0) {
					arr.splice(0, 1)
				}
				if (item.approvalButton && item.approvalButton.isNetCarRejectedButton == 0) {
					arr.splice(0, 1)
				}
				this.tipsList = arr

				this.dataList.forEach(v => {
					this.$set(v, 'show', false)
					if (v.applyCode == item.applyCode) {
						this.$set(v, 'show', true)
						setTimeout(() => {
							this.$set(v, 'show', false)
						}, 2500)
					}
				})

			},
			getOperate() {
				this.pageNum = 1
				this.dataList = []
				this.keyword = null
				this.queryList()
			},
			adoptFun(objr) {
				if (!objr) return this.rejectshow = false
				if (this.useCarType == 1 || this.useCarType == 2) {
					if (this.useCarType == 2) {
						objr.isNetCar = 1
					}
					checkcomporder({
						checkCompOrderVo: objr,
						id: this.transmitObjr.applyId,
					}).then((data) => {
						this.pageNum = 1
						this.dataList = []
						this.queryList()
						setTimeout(() => {
							this.rejectshow = false
							this.adoptShow = false
							uni.$u.toast('操作成功')
							this.$emit('sendBtn')
						}, 1000)
					})
				} else if (this.useCarType == 3) {
					checktoleaseor({
						checkCompOrderVo: objr,
						id: this.transmitObjr.travelId,
					}).then((data) => {
						this.pageNum = 1
						this.dataList = []
						this.queryList()
						setTimeout(() => {
							uni.$u.toast('操作成功')
							this.$emit('sendBtn')
							this.rejectshow = false
							this.adoptShow = false
						}, 1000)
					})
				} else if (this.useCarType == 4) {
					checkpriceteorder({
						checkCompOrderVo: objr,
						id: this.transmitObjr.applyId,
					}).then((data) => {
						this.pageNum = 1
						this.dataList = []
						this.queryList()
						setTimeout(() => {
							uni.$u.toast('操作成功')
							this.$emit('sendBtn')
							this.rejectshow = false
							this.adoptShow = false
						}, 1000)
					})
				}
			},
			approvalBtn(e) {
				let objr = {
					isAgree: 1,
					checkRemark: null,
					nextCheckUserId: e.value[0].userId
				}
				this.adoptFun(objr)
			},
			trueClick(item) {
				let that = this
				item.useCarType = that.useCarType
				that.transmitObjr = item
				let text = this.useCarType == 4 ? '是否确定通过审批？' : '是否确定通过用车申请？'
				uni.showModal({
					content: text,
					success: function(res) {
						if (res.confirm) {
							if (item.nextCheckUserList && item.nextCheckUserList.length > 0) {
								that.transmitObjr.nextListr = [item.nextCheckUserList]
								that.adoptShow = true
							} else {
								let objr = {
									isAgree: 1,
									checkRemark: null,
									nextCheckUserId: null
								}
								that.adoptFun(objr)
							}
						}
					}
				})

			},
			falseClick(item, type) {
				item.useCarType = this.useCarType
				this.transmitObjr = item
				this.rejectName = '驳回'
				this.rejectshow = true
			},
			typeClose(type) {
				this[`${type?'adoptShow':'rejectshow'}`] = false
			},
			queryList() {
				checkcomporderlist({
					params: {
						orderStateType: this.tabtype,
						useCarType: this.useCarType,
						pageNum: this.pageNum,
						pageSize: this.pageSize,
						content: this.keyword
					}
				}).then((data) => {
					data.pageList.forEach(v => {
						if (v.throughAddrInfo) {
							v.throughAddrInfo = JSON.parse(v.throughAddrInfo)
							v.throughAddrInfoName = v.throughAddrInfo.map(v => v.siteAddrName)
								.join(
									' → ')
						}
						v.startTimer = uni.$u.timeFormat(new Date(v.reserveStartTime).getTime(),
							'mm月dd日 hh:MM')
						v.endTimer = uni.$u.timeFormat(new Date(v.reserveEndTime).getTime(),
							'mm月dd日 hh:MM')
						v.show = false
					})

					this.total = data.total
					if (this.pageNum == 1) {
						this.dataList = data.pageList
					} else {
						this.dataList.push(...data.pageList)
					}
					this.triggered = false
				})
			},
			orderWash(e, t) {
				this.compOrderList.forEach(v => {
					if (v.dicValue == e) t = v.dicDescribe
				})
				return t
			},
			onRefresh(e) {
				this.pageNum = 1
				this.triggered = true
				this.dataList = []
				this.queryList()
			},
			onReflowe(e) {
				if (this.total == this.dataList.length) return
				this.pageNum++
				this.queryList()
			},
			searchFun(value) {
				this.pageNum = 1
				this.dataList = []
				this.queryList()
			},
			// 跳转详情
			goDetail(item) {
				uni.$u.route('/pages/wayPassenger/workBench/approve/detail/detail', {
					applyId: item.applyId,
					travelId: item.travelId,
					type: this.useCarType
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.filterBox {
		padding: 0 32rpx;
	}

	.search {
		/deep/.u-search__content {
			background-color: #E9ECF7 !important;
		}

		/deep/.u-search__content__input {
			background-color: #E9ECF7 !important;
		}
	}

	.scroll-content {
		margin-top: 10rpx;
		height: calc(100vh - 280rpx);
	}

	.app_list {
		background-color: #fff;
		margin: 14rpx 11rpx;
		border-radius: 11rpx;
		padding: 30rpx;
		font-size: 28rpx;
		position: relative;

		.jiao_img {
			position: absolute;
			left: 0;
			top: 0;
		}

		.icon-right {
			position: absolute;
			top: 50%;
			right: 27rpx;
			margin-top: -16rpx;
		}

		.tab_icon {
			margin-left: 16rpx;
		}

		.text-dh {
			font-size: 24rpx;
			color: #999999;
		}

		.text-rt {
			color: #346CF2;
		}

		.app_t_line {
			margin-top: 14rpx;
		}

		.spot {
			width: 16rpx;
			height: 16rpx;
			background-color: #239EFC;
			border-radius: 50%;
			margin: 0 19rpx 0 8rpx;
		}

		.opinion {
			border-top: 1px dashed #999999;
			padding-top: 10rpx;
			margin-top: 16rpx;
		}

		.agreebtn {
			margin-right: 30rpx;
		}
	}
</style>