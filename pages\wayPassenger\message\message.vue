<template>
	<view class="message">
		<u-navbar title="消息" :placeholder="true">
			<!-- <view slot="left"></view> -->
			<view slot="left" @click="getMsgAll">
				全部已读
			</view>
		</u-navbar>
		<view class="utabsr">
			<u-tabs :list="tabsList" :scrollable="false" lineWidth="22" lineHeight="2" lineColor="#346CF2"
				:activeStyle="{ color: '#262626' }" :inactiveStyle="{ color: '#999999' }" @change="changeTab"
				:current="tabSum" itemStyle="width:50%; height: 40px; padding:0">
			</u-tabs>
		</view>
		<scroll-view class="scroll-content" scroll-y="true" refresher-enabled="true" :refresher-triggered="triggered"
			:refresher-threshold="100" refresher-background="#F9FAFE" @scrolltolower="onReflowe"
			@refresherrefresh="onRefresh">
			<u-swipe-action>
				<u-swipe-action-item :options="item.options" v-for="(item, index) in dataList" :key="index"
					@click="(v) => optionsBtn(v, item)">
					<view class="item" @click="lookBtn(item)" :class="{ 'item_color': item.isRead == 1 }">

						<view class="item_icon" v-if="item.isTop == 1">
							<u-icon size="40" name="https://zqcx.di-digo.com/app/image/zhiding.png">
							</u-icon>
						</view>
						<u-icon name="chat" size="40" color="#ccc" v-if="item.isRead == 1"></u-icon>
						<u-icon name="chat" size="40" v-else></u-icon>
						<view class="u-flex-direction item-textr">
							<view>{{ item.messageContent }}</view>
							<text style="margin-top: 10rpx;font-size: 26rpx;">{{ washTiem(item.createTime) }}</text>
						</view>
						<view class="u-flex">
							<text class="item-single " v-if="item.isRead == 0"></text>
						</view>
					</view>
				</u-swipe-action-item>
				<view style="text-align: center;margin: 20rpx 0;" v-if="dataList.length != 0">———— 到底了 ————</view>
				<u-empty v-if="dataList.length == 0" text="消息为空" icon="http://cdn.uviewui.com/uview/empty/order.png">
				</u-empty>
			</u-swipe-action>


		</scroll-view>
		<!-- <tabBart :current='3'></tabBart> -->
	</view>
</template>

<script>
	import {
		msgList,
		msgInfo,
		msgAll,
		msgTopr,
		msgDel,
		messagecount
	} from '@/config/consoler.js';

	import tabBart from '@/components/tabBart/tabBart.vue'
	export default {
		components: {
			// tabBart
		},
		data() {
			return {
				triggered: false,
				tabSum: 0,
				tabsList: [{
					name: '消息通知',
					badge: {
						// value: 5,
					}
				}, {
					name: '推送通知',
					badge: {
						// value: 5,
					}
				}],
				dataList: [],
				paramsr: {
					messageType: 1,
					pageNum: 1,
					pageSize: 20,
				},
				unreadSum: {},
				total: 0
			};
		},
		onLoad() {
			// uni.hideTabBar()
		},
		onShow() {
			this.getUnread()
			this.getListr()
		},
		methods: {
			optionsBtn(v, item) {
				let that = this
				if (v.index == 1) {
					uni.showModal({
						title: '提示',
						content: '确认删除？',
						success: function(res) {
							if (res.confirm) {
								that.getMsgDel(item)
							}
						}
					})
				} else {
					this.getMsgTop(item)
				}
			},
			getMsgDel(item) {
				msgDel({
					id: item.messageId
				}).then(res => {
					this.getListr()
					uni.$u.toast('操作成功')
				})
			},
			getMsgTop(item) {
				msgTopr({
					id: item.messageId
				}).then(res => {
					this.dataList = []
					this.getListr()
					uni.$u.toast('操作成功')
				})
			},
			lookBtn(item) {
				if (item.isRead == 1) return
				let that = this
				uni.showModal({
					title: '提示',
					content: item.messageContent,
					showCancel: false,
					success: function(res) {
						if (res.confirm) {
							that.getMsgInfo(item)
						}
					}
				})
			},
			washTiem(tiem) {
				return this.$DateUtil.toShowTimeFormat(tiem)
			},
			getUnread() {
				messagecount().then(res => {
					this.$set(this.tabsList[0].badge, 'value', res.countSystem)
					this.$set(this.tabsList[1].badge, 'value', res.countPush)
					let gentle = res.countPush + res.countSystem
					let sum = gentle > 99 ? '99+' : `${gentle}`
					uni.setTabBarBadge({
						index: 3,
						text: sum ? sum : '0'
					})
				})
			},
			getListr() {
				msgList({
					params: this.paramsr
				}).then(res => {
					res.pageList.map(v => {
						if (v.isTop == 1) {
							v.options = [{
								text: '取消置頂',
								name: '取消置頂',
								style: {
									backgroundColor: '#ffaa00',
								}
							}, {
								text: '删除',
								name: '删除',
								style: {
									backgroundColor: '#cccccc',
								}
							}, ]
						} else {
							v.options = [{
								text: '置頂',
								name: '置頂',
								style: {
									backgroundColor: '#ffaa00',
								}
							}, {
								text: '删除',
								name: '删除',
								style: {
									backgroundColor: '#cccccc',
								}
							}, ]
						}
					})
					if (this.paramsr.pageNum == 1) {
						this.dataList = res.pageList
					} else {
						this.dataList.push(...res.pageList)
					}
					this.total = res.total
					this.triggered = false
				})
			},
			getMsgInfo(item) {
				msgInfo({
					id: item.messageId
				}).then(res => {
					this.paramsr.pageNum = 1
					this.dataList = []
					this.getListr()
					this.getUnread()
				})
			},
			getMsgAll() {
				let that = this
				uni.showModal({
					title: '提示',
					content: '确定设置全部已读？',
					success: function(res) {
						if (res.confirm) {
							msgAll().then(res => {
								that.getListr()
								that.getUnread()
							})
						}
					}
				})

			},
			changeTab(e) {
				this.dataList = []
				this.paramsr.messageType = e.name == '消息通知' ? 1 : 2
				this.getListr()

			},
			onRefresh() {
				this.paramsr.pageNum = 1
				this.triggered = true
				this.getListr()
			},
			onReflowe() {
				if (this.total == this.dataList.length) return console.log('相等啦')
					++this.paramsr.pageNum
				this.getListr()
			}
		}
	}
</script>

<style lang="scss">
	.message {
		// padding-bottom: 80rpx;
	}

	.utabsr {
		background-color: #fff;
		border-top: 1px solid #f1f1f1;
		position: fixed;
		z-index: 11;
		width: 100%;
	}

	.scroll-content {
		margin-top: 50px;
		height: calc(100vh - 300rpx);
		padding-bottom: 20px;
	}

	.item {
		display: flex;
		padding: 26rpx 40rpx;
		border-top: 1px solid #f1f1f1;
		position: relative;
	}

	.item_icon {
		position: absolute;
		top: 0;
		left: 0;
	}

	.item_color {
		color: #cccccc;
	}

	.item-textr {
		display: flex;
		margin: 0 40rpx;
		flex: 1;
		word-wrap: break-word;
		word-break: break-all;
	}

	.item-single {
		width: 15rpx;
		height: 15rpx;
		border-radius: 50%;
		background: red;
	}

	/deep/ .u-swipe-action-item {
		touch-action: auto !important;
	}
</style>