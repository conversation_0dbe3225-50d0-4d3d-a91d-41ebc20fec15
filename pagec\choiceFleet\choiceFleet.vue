<template>
	<view class="choice">
		<u-navbar title="选择车队" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="filterBox">
			<u-search class="search" placeholder="请输入车队名称" :showAction="false" v-model="keyword"
				@search="queryList(pageParams)">
			</u-search>
		</view>
		<view class="cell_list">
			<view class="u-flex car u-row-between" v-for="(item,index) in dataList" :key="index" @click="choice(item)">
				<view class="u-flex u-flex-6">
					<u-icon class="iconStyle" size="40" name="https://zqcx.di-digo.com/app/image/gwgp_img1.png">
					</u-icon>
					<view class="car_name_sbox">
						<view class="car_t">
							{{item.carTeamName}}
						</view>
						<view class="car_txt">
							{{item.carTeamCategory=='1'?'企业车辆':'员工车辆'}}
						</view>
					</view>
				</view>

				<view class="car_type u-flex-3">
					<view class="car_name_sbox">
						<view class="car_t">
							空闲车辆
						</view>
						<view class="car_txt">
							{{item.carCount}}
						</view>
					</view>
				</view>

				<view class="u-flex-3">
					<view class="car_name_sbox">
						<view class="car_t">
							空闲司机
						</view>
						<view class="car_txt">
							{{item.driverCount}}
						</view>
					</view>
				</view>
			</view>

		</view>

		<!-- 更多 -->
		<u-loadmore v-if="dataList.length>0" :status="status" />

		<u-empty v-if="dataList.length==0" mode="order" text="车队为空" icon="http://cdn.uviewui.com/uview/empty/order.png">
		</u-empty>

		<!-- 转车队组件 -->
		<u-popup class="popup_bg" :round="5" :show="cancelOrdershow" mode="center" @close="cancelOrderclose">
			<view class="popup_tit">
				确定转车队
			</view>
			<view class="popup_txt">
				是否确定转车队派车？
			</view>
			<view class="u-flex u-row-around popup_btn_box">
				<view class="popup_btn">
					<u-button color="#346CF2" type="primary" text="确定" @click="fleetFun()"></u-button>
				</view>
				<view class="popup_btn two">
					<u-button color="#E9ECF7" type="primary" text="取消" @click="cancelOrderclose()">
					</u-button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		carteamlist,
		dispatchleasechangefleet
	} from '@/config/api.js';
	export default {
		data() {
			return {
				pageParams: {},
				keyword: '',
				dataList: [],
				choiceOrder: {},
				status: 'loadmore',
				cancelOrdershow: false,
				hasNext: true,
				pageNum: 1,
				pageSize: 20,
			};
		},
		onLoad(option) {
			this.pageParams = option
			this.queryList(option)
		},
		onReachBottom() {
			if (this.hasNext) {
				this.status = 'loading';
				this.pageNum = ++this.pageNum;
				setTimeout(() => {
					this.queryList(this.pageParams)
				}, 1000)
			}
		},
		methods: {
			// 转车队
			fleetFun() {
				let that = this
				dispatchleasechangefleet({
					dispatchFleetVo: {
						carTeamId: that.choiceOrder.carTeamId
					},
					id: that.pageParams.travelId,
				}).then((data) => {
					uni.$u.toast('操作成功')
					setTimeout(() => {
						let pages = getCurrentPages(); //获取跳转的所有页面
						let nowPage = pages[pages.length - 1]; //当前页
						let prevPage = pages[pages.length - 2]; //上一页

						uni.redirectTo({
							url: '/pager/dispatch/dispatch?typer=1'
						});
						// uni.navigateBack({
						// 	delta: 1,
						// 	success: function() {
						// 		if (that.pageParams.typer=='list') {
						// 			console.log(1);
						// 			prevPage.$vm.loadPage(); // 执行前一个页面的刷新
						// 		} else {
						// 			console.log(2);
						// 			prevPage.$vm.getDetail(); // 执行前一个页面的刷新
						// 		}
						// 	}
						// });
					}, 1000)
				})
			},
			cancelOrderclose() {
				this.cancelOrdershow = false
			},
			queryList(option) {
				carteamlist({
					params: {
						travelId: option.travelId,
						pageNum: this.pageNum,
						pageSize: this.pageSize,
						content: this.keyword,
					}
				}).then((data) => {
					this.dataList = data.pageList
					this.hasNext = data.hasNext
					if (data.hasNext) {
						this.status = 'loadmore'
					} else {
						this.status = 'nomore'
					}
				})
			},
			choice(item) {
				this.choiceOrder = item
				this.cancelOrdershow = true
			}
		}
	}
</script>

<style lang="scss" scoped>
	/deep/.popupcbox {
		padding: 0 53rpx;
	}

	/deep/.select_input {
		height: 60rpx;
		border: 1px solid #CCCCCC;
		border-radius: 8rpx;
		padding-left: 20rpx;
		line-height: 60rpx;
		color: #999;
		margin: 20rpx 0;
	}

	/deep/.selectbox {
		padding: 0 53rpx;
		font-size: 28rpx;
	}

	/deep/.popup_btn_box {
		padding-bottom: 27rpx;
		margin-top: 30rpx;
	}

	/deep/.popup_tit {
		font-size: 36rpx;
		text-align: center;
		padding: 40rpx 0 30rpx 0;
	}

	/deep/.popup_txt {
		font-size: 28rpx;
		text-align: center;
		margin-bottom: 40rpx;
	}

	/deep/.u-popup__content {
		width: 84%;
	}

	/deep/.popup_btn {
		width: 230rpx;
		height: 80rpx;

		/deep/ .u-button__text {
			font-size: 36rpx !important;
		}
	}

	.choice {
		.popup_bg {


			.two {
				color: #666666 !important;
			}
		}

		.cell_list {
			background-color: #fff;
		}

		.car {
			padding: 15rpx 0;
			margin: 0 20rpx;
			border-bottom: 1px solid #E9ECF7;

			.car_name_sbox {
				margin-left: 20rpx;

				.car_t {
					font-size: 28rpx;
					margin-bottom: 6rpx;
				}

				.car_txt {
					font-size: 20rpx;
					color: #999999;
				}

				.car_type {
					font-size: 28rpx;
				}
			}

		}

		.filterBox {
			padding: 14rpx 20rpx;
			background-color: #fff;
			border-bottom: 1px solid #E9ECF7;
			border-top: 1px solid #E9ECF7;

			.search {
				/deep/.u-search__content {
					background-color: #E9ECF7 !important;
				}

				/deep/.u-search__content__input {
					background-color: #E9ECF7 !important;
				}
			}
		}
	}
</style>
