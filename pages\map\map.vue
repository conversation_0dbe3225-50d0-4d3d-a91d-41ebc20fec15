<template>
	<view>
		<web-view :src="src" @message="handleMessage" v-if="webShow" ref='webview'></web-view>
	</view>
</template>

<script>
	import qs from 'qs'
	import {
		updateorderplace,
	} from '@/config/api.js'
	export default {
		data() {
			return {
				src: '',
				webShow: false
			};
		},
		methods: {
			handleMessage(evt) {
				uni.showLoading({
					title: '加载中',
					mask: true
				})
				let pages = getCurrentPages(); //获取跳转的所有页面
				let nowPage = pages[pages.length - 1]; //当前页
				let prevPage = pages[pages.length - 2]; //上一页
				console.log('接收到的消息2：' + JSON.stringify(evt.detail.data));
				if (evt.detail.data[0].page == 'postion') {
					prevPage.$vm.fromAddrName = evt.detail.data[0].fromAddrName
					prevPage.$vm.fromAddrDetail = evt.detail.data[0].fromAddrDetail
					prevPage.$vm.fromAreaCode = evt.detail.data[0].fromAreaCode
					prevPage.$vm.fromLat = evt.detail.data[0].fromLat
					prevPage.$vm.fromLng = evt.detail.data[0].fromLng
					this.getBack()
				} else if (evt.detail.data[0].page == 'postionend') {
					prevPage.$vm.toAddrName = evt.detail.data[0].toAddrName
					prevPage.$vm.toAddrDetail = evt.detail.data[0].toAddrDetail
					prevPage.$vm.toAreaCode = evt.detail.data[0].toAreaCode
					prevPage.$vm.toLat = evt.detail.data[0].toLat
					prevPage.$vm.toLng = evt.detail.data[0].toLng
					this.getBack()
				} else if (evt.detail.data[0].page == 'channel') {
					let data = prevPage.$vm
					let objr = evt.detail.data[0]
					data.channeList.splice(objr.idxr, 1, objr)
					this.getBack()
				} else if (evt.detail.data[0].page == "address") {
					this.setAddress(evt.detail.data[0])
					this.getBack()
				}
			},
			h5handleMessage(evt) {
				let pages = getCurrentPages(); //获取跳转的所有页面
				let nowPage = pages[pages.length - 1]; //当前页
				let prevPage = pages[pages.length - 2]; //上一页
				console.log('接收到的消息3：' + JSON.stringify(evt.data.data));
				if (evt.data.data.arg.page == 'postion') {
					prevPage.$vm.fromAddrName = evt.data.data.arg.fromAddrName
					prevPage.$vm.fromAddrDetail = evt.data.data.arg.fromAddrDetail
					prevPage.$vm.fromAreaCode = evt.data.data.arg.fromAreaCode
					prevPage.$vm.fromLat = evt.data.data.arg.fromLat
					prevPage.$vm.fromLng = evt.data.data.arg.fromLng
					this.getBack()
				} else if (evt.data.data.arg.page == 'postionend') {
					prevPage.$vm.toAddrName = evt.data.data.arg.toAddrName
					prevPage.$vm.toAddrDetail = evt.data.data.arg.toAddrDetail
					prevPage.$vm.toAreaCode = evt.data.data.arg.toAreaCode
					prevPage.$vm.toLat = evt.data.data.arg.toLat
					prevPage.$vm.toLng = evt.data.data.arg.toLng
					this.getBack()
				} else if (evt.data.data.arg.page == 'channel') {
					let data = prevPage.$vm
					let objr = evt.data.data.arg
					data.channeList.splice(objr.idxr, 1, objr)
					this.getBack()
				} else if (evt.data.data.arg.page == "address") {
					this.setAddress(evt.data.data.arg)
					this.getBack()
				}

			},
			setAddress(data) {
				data.urlObjr = JSON.parse(data.urlObjr)
				let objr = {
					oldLat: Number(data.urlObjr.toLat),
					oldLng: Number(data.urlObjr.toLng),
					toAddrDetail: data.toAddrDetail,
					toAddrName: data.toAddrName,
					toAreaCode: data.toAreaCode,
					toLat: data.toLat,
					toLng: data.toLng,
					id: data.urlObjr.useCarInfoId,
				}
				updateorderplace(objr).then(v => {
					uni.$u.toast(v)
				})
			},

			getBack() {
				setTimeout(() => {
					uni.hideLoading()
					uni.navigateBack({
						delta: 1,
					})
				}, 500)
			},
			
			getLoad(option) {
				if (JSON.stringify(option) == '{}') {} else {
					uni.showLoading({
						title: '加载中',
						mask: true
					})
					this.webShow = true
					setTimeout(() => {
						// let urlr = 'https://zqcxdev.di-digo.com/app-h5/'
						let urlr = 'https://zqcx.di-digo.com/app-h5/'
						option.accessToken = JSON.parse(uni.getStorageSync("userInfo")).accessToken
						// #ifdef H5
						option.typer = "H5"
						this.src = option.url + '?' + qs.stringify(option)
						// #endif
						// #ifdef MP-WEIXIN
						option.typer = "WEIXIN"
						this.src = `${urlr}${option.url}?${qs.stringify(option)}`
						// #endif

						uni.hideLoading()

					}, 500)
				}
			},
		},
		onLoad(option) {
			console.log(11111);
			this.getLoad(option)
		},
		created() {
			// #ifdef H5
			//监听并接收消息
			window.addEventListener('message', this.h5handleMessage);
			// #endif
		},

		beforeDestroy() {
			// #ifdef H5
			window.removeEventListener('message', this.h5handleMessage);
			// #endif
		}
	}
</script>

<style lang="scss">

</style>