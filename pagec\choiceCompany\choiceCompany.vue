<template>
	<view class="choice">
		<u-navbar title="选择租赁公司" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="filterBox">
			<u-search class="search" placeholder="请输入名称" :showAction="false" v-model="keyword" @search="queryList">
			</u-search>
		</view>
		<view class="cell_list">
			<view class="u-flex car u-row-between" v-for="(item,index) in dataList" :key="index" @click="choice(item)">
				<view class="u-flex">
					<u-icon class="iconStyle" size="40" name="https://zqcx.di-digo.com/app/image/gwgp_img1.png">
					</u-icon>
					<view class="car_name_sbox">
						<view class="car_t">
							{{item.supplierCompName}}
						</view>
					</view>
				</view>

			</view>

		</view>

		<!-- 更多 -->
		<u-loadmore v-if="dataList.length>0" :status="status" />

		<u-empty v-if="dataList.length==0" mode="order" text="公司为空" icon="http://cdn.uviewui.com/uview/empty/order.png">
		</u-empty>
	</view>
</template>

<script>
	import {
		suppliercoopelist
	} from '@/config/api.js';
	export default {
		data() {
			return {
				pageParams: {},
				keyword: '',
				dataList: [],
				status: 'loadmore',
				hasNext: true,
				pageNum: 1,
				pageSize: 20,
			};
		},
		onLoad(option) {
			this.pageParams = option
			this.queryList(option)
		},
		onReachBottom() {
			if (this.hasNext) {
				this.status = 'loading';
				this.pageNum = ++this.pageNum;
				setTimeout(() => {
					this.queryList()
				}, 1000)
			}
		},
		methods: {
			queryList(option) {
				suppliercoopelist({
					params: {
						// travelId:option.travelId,
						// pageNum:this.pageNum,
						// pageSize:this.pageSize,
						content: this.keyword,
						// runState:'',
					}
				}).then((data) => {
					this.dataList = data
					// this.hasNext=data.hasNext
					// if(data.hasNext){
					// 	this.status='loadmore'
					// }else{
					// 	this.status='nomore'
					// }
				})
			},
			choice(item) {
				let pages = getCurrentPages(); //获取跳转的所有页面
				let nowPage = pages[pages.length - 1]; //当前页
				let prevPage = pages[pages.length - 2]; //上一页

				// 统一用此方法接收
				prevPage.$vm.receive(item, this.pageParams.index)

				uni.navigateBack({
					delta: 1
				});

			}
		}
	}
</script>

<style lang="scss">
	.choice {
		.cell_list {
			background-color: #fff;
		}

		.car {
			padding: 15rpx 0;
			margin: 0 20rpx;
			border-bottom: 1px solid #E9ECF7;

			.car_name_sbox {
				margin-left: 20rpx;

				.car_t {
					font-size: 28rpx;
					margin-bottom: 6rpx;
				}

				.car_txt {
					font-size: 20rpx;
					color: #999999;
				}

				.car_type {
					font-size: 28rpx;
				}
			}

		}

		.filterBox {
			padding: 14rpx 20rpx;
			background-color: #fff;
			border-bottom: 1px solid #E9ECF7;
			border-top: 1px solid #E9ECF7;

			.search {
				/deep/.u-search__content {
					background-color: #E9ECF7 !important;
				}

				/deep/.u-search__content__input {
					background-color: #E9ECF7 !important;
				}
			}
		}
	}
</style>
