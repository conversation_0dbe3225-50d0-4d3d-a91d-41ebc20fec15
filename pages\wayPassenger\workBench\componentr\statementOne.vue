<template>
	<view class="statement">
		<view class="popuptit ">
			<!-- <u-icon name="arrow-left" size="24"></u-icon> -->
			<view>订单费用信息确认</view>
			<!-- <view></view> -->
		</view>
		<view class="state-list">
			<ul class="ui-list">
				<li v-if="datar.travelType==2">
					<view class="ui-list-info">
						<h5 class="ui-nowrap">车型 :</h5>
						<view class="ui-txt-info">
							<u--input class="mileage_input" :customStyle="heightr" v-model="datar.carTypeFullName"
								inputAlign='right' border="none" :readonly='true'> </u--input>
						</view>
					</view>
				</li>
				<li v-if="datar.travelType==2">
					<view class="ui-list-info">
						<h5 class="ui-nowrap" style="width: 30%;">计费套餐 :</h5>
						<view class="ui-txt-info textnowrap" style="width: 70%;">
							<!-- <u--input class="mileage_input" :customStyle="heightr" v-model="datar.valuationFullName"
							inputAlign='right' border="none" :readonly='true'> </u--input> -->
							{{datar.valuationFullName}}
						</view>
					</view>
				</li>

				<li>
					<view class="ui-list-info">
						<h5 class="ui-nowrap" style="width: 40%;">实际开始时间：</h5>
						<view class="ui-txt-info" @click="openTiem('start')">
							<u--input class="mileage_input" :customStyle="heightr" v-model="datar.actualStartTime"
								inputAlign='right' border="none" placeholder="请选择" readonly suffixIcon="edit-pen">
							</u--input>
						</view>
					</view>
				</li>
				<li>
					<view class="ui-list-info">
						<h5 class="ui-nowrap" style="width: 40%;">实际结束时间：</h5>
						<view class="ui-txt-info" @click="openTiem('end')">
							<u--input class="mileage_input" :customStyle="heightr" v-model="datar.actualEndTime"
								inputAlign='right' border="none" placeholder="请选择" readonly suffixIcon="edit-pen">
							</u--input>
						</view>
					</view>
				</li>
				<!-- -->
				<li v-if="datar.overtakeTimeType==5">
					<view class="ui-list-info">
						<h5 class="ui-nowrap">跨夜数 :</h5>
						<view class="ui-txt-info">
							<u--input class="mileage_input" :customStyle="heightr" v-model="datar.nights"
								inputAlign='right' border="none" :readonly='true'>
								<!-- <template slot="suffix">
									<text style="color: #000;">夜</text>
								</template> -->
							</u--input>
						</view>
					</view>
				</li>
				<li>
					<view class="ui-list-info">
						<h5 class="ui-nowrap">实际开始里程(公里):</h5>
						<view class="ui-txt-info">
							<u--input class="mileage_input" :customStyle="heightr" v-model="datar.actualStartMileage"
								inputAlign='right' type="number" border="none" placeholder="请输入" suffixIcon="edit-pen">
							</u--input>
						</view>
					</view>
				</li>
				<template v-if="datar.overtakeTimeType==5">
					<template v-for="(item,idx) in datar.dayEndMileage">
						<li v-if="isChange ? idx != datar.dayEndMileage.length-1 : true">
							<view class="ui-list-info">
								<h5 class="ui-nowrap" style="width: 100%;">第{{idx+1}}天结束里程(公里):</h5>
								<view class="ui-txt-info">
									<u--input class="mileage_input" :customStyle="heightr" v-model="item.endMileage"
										@blur="endOvernightBlur(item,idx)" inputAlign='right' type="number"
										border="none" placeholder="请输入" suffixIcon="edit-pen">
									</u--input>
								</view>
							</view>
						</li>
					</template>
				</template>
				<li>
					<view class="ui-list-info">
						<h5 class="ui-nowrap">最后结束里程(公里):</h5>
						<view class="ui-txt-info">
							<u--input class="mileage_input" :customStyle="heightr" v-model="datar.actualEndMileage"
								inputAlign='right' type="number" border="none" placeholder="请输入" suffixIcon="edit-pen"
								@blur="endBlur(datar.actualEndMileage,datar.actualStartMileage)">
							</u--input>
						</view>
					</view>
				</li>
			</ul>
			<view class="mileage_tit_s" v-if="datar.otherFee&&datar.otherFee.length>0">
				费用信息：
			</view>
			<ul class="ui-list">
				<li v-for="(item,index) in datar.otherFee" :key="index">

					<view class="ui-list-info" v-if="item.feedatarCode==200">
						<h5 class="ui-nowrap-l">{{item.key}}:</h5>
						<view class="ui-txt-info width100">
							<u--input class="mileage_input" :customStyle="heightr" v-model="item.value"
								inputAlign='right' type="number" border="none" placeholder="请输入"
								suffixIcon="edit-pen"></u--input>
						</view>
					</view>

					<view class="ui-list-info" v-else>
						<h5 class="ui-nowrap-l">{{item.key}}:</h5>
						<view class="ui-txt-info width100 " style="text-align: end;">
							<u--input class="mileage_input" :customStyle="heightr" v-model="item.value"
								inputAlign='right' type="number" border="none" readonly></u--input>
						</view>
					</view>
				</li>
			</ul>
		</view>
		<view class="u-flex u-row-around popupbtn_box">
			<view class="popupbtn">
				<u-button color="#346CF2" type="primary" text="确定" @click="determine()"></u-button>
			</view>
			<view class="popupbtn two">
				<u-button color="#E9ECF7" type="primary" text="取消" @click="$emit('stateOne',null,stateObj)">
				</u-button>
			</view>
		</view>
		<!-- 开始时间 -->
		<u-datetime-picker :show="timeShow" v-model="choiceTime" @cancel="timeShow=false" @confirm="confirmstartEnd"
			mode="datetime"></u-datetime-picker>
	</view>
</template>

<script>
	import {
		costcalculate
	} from '@/config/api.js';
	import {
		overnightDay
	} from '@/common/utils.js'
	export default {
		data() {
			return {
				heightr: {
					height: '80rpx'
				},
				datar: {
					dayEndMileage: []
				},
				choiceTime: Number(new Date()),
				timeShow: false,
				timeType: ""
			}
		},
		props: ['stateObj','isChange'],
		mounted() {
			this.datar = this.stateObj
			console.log(this.datar, 'this.datar')
		},
		methods: {
			determine() {
				if (this.datar.dayEndMileage && this.datar.overtakeTimeType == 5) {
					try {
						for (let i = 0; i < this.datar.dayEndMileage.length; i++) {
							const item = this.datar.dayEndMileage[i];
							if (!item.endMileage && item.endMileage !== 0) {
								uni.$u.toast(`第${i+1}天结束里程不能为空`);
								return;
							}
						}
					} catch (error) {
						// uni.$u.toast('请填写结束里程')
						//TODO handle the exception
					}
				}

				let param = {
					...this.datar,
				}
				param.otherFee = JSON.stringify(param.otherFee)
				// param.otherFee = JSON.stringify(param.valuationTemplate)
				param.actualEndTime = param.actualEndTime + ':00'
				param.actualStartTime = param.actualStartTime + ':00'
				param.dayEndMileage = JSON.stringify(param.dayEndMileage || [])
				param.nights = param.nights
				costcalculate({
					costCalculateVo: param,
					id: this.datar.travelId,
				}).then((data) => {
					if (data.feeDetail) {
						data.feeDetail = JSON.parse(data.feeDetail)
						data.feeDetail.forEach((item, index) => {
							if (item.feeDetailCode == 200) {
								if (item.unit) {
									item.unit = JSON.parse(item.unit)
									item.unit.forEach(r => {
										r.feeDetailCode = 200
										data.feeDetail.push(r)
									})
								}
							}
							if (item.key == "自定义项目费用") {
								data.feeDetail.splice(index, 1)
							}
						})
					}
					// if (param.otherFee == '[]') {
					// 	data.otherFee = param.valuationTemplate
					// } else {
					// 	data.otherFee = JSON.parse(param.otherFee)
					// }
					data.travelId = this.datar.travelId
					data.actualStartTime = new Date(data.actualStartTime).getTime()
					data.actualEndTime = new Date(data.actualEndTime).getTime()
					this.$emit('stateOne', data, this.datar)
				})
			},
			// 打开时间
			openTiem(type) {
				this.timeType = type
				this.timeShow = true
			},
			// 选择时间
			confirmstartEnd(e) {
				let valr = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM')

				if (this.timeType == 'start') {
					this.datar.actualStartTime = valr
				} else {
					if (!this.datar.actualStartTime) return uni.$u.toast('请选择开始时间')
					if (this.datar.actualStartTime > valr) return uni.$u.toast('请选择大于开始时间')
					this.datar.actualEndTime = valr
				}
				this.timeShow = false

				// 获取跨夜天数
				this.getOvernightDay()
			},
			getOvernightDay() {
				if (this.datar.actualStartTime && this.datar.actualEndTime) {
					this.$set(this.datar, 'nights', overnightDay(this.datar.actualStartTime, this.datar.actualEndTime))

					if (this.datar.nights) {
						let list = [];
						for (let i = 1; i <= this.datar.nights; i++) {
							list.push({
								day: i,
								endMileage: null
							});
						}
						this.$set(this.datar, 'dayEndMileage', list)
					}else{
						this.$set(this.datar, 'dayEndMileage', [])
					}

				}
			},
			endOvernightBlur(item, idx) {
				if (idx == 0) {
					if (this.datar.actualStartMileage >= item.endMileage) {
						uni.$u.toast('请输入大于开始里程')
						this.$set(item, 'endMileage', '')
					}
				} else {
					if (Number(this.datar.dayEndMileage[idx - 1].endMileage) >= Number(item.endMileage)) {
						uni.$u.toast('请输入大于上一天的里程')
						this.$set(item, 'endMileage', '')
					}
				}
			},
			// 验证
			endBlur(e1, e2) {
				if (this.datar.overtakeTimeType == 5) {
					if (this.datar.dayEndMileage) {
						let length = this.datar.dayEndMileage.length - 1
						let endMileage = this.datar.dayEndMileage[length].endMileage || null;
						if (endMileage && Number(endMileage) >= Number(this.datar.actualEndMileage)) {
							// if (this.datar.dayEndMileage[length].endMileage >= this.datar.actualEndMileage) {
							uni.$u.toast('请输入大于上一天的里程')
							this.$set(this.datar, 'actualEndMileage', null)
						}
					}
				} else {
					if (Number(e1) <= Number(e2)) {
						uni.$u.toast('实际结束里程要大于实际开始里程！')
						this.$set(this.datar, 'actualEndMileage', null)
					}
				}

			},
		}
	}
</script>

<style lang="scss" scoped>
	.statement {
		.popupcbox {
			padding: 0 53rpx;
		}

		.select_input {
			height: 60rpx;
			border: 1px solid #CCCCCC;
			border-radius: 8rpx;
			padding-left: 20rpx;
			line-height: 60rpx;
			color: #999;
			margin: 20rpx 0;
		}

		.selectbox {
			padding: 0 53rpx;
			font-size: 28rpx;
		}

		.popupbtn_box {
			padding-bottom: 27rpx;
			margin-top: 30rpx;
		}

		.popuptit {
			font-size: 36rpx;
			text-align: center;
			padding: 40rpx 10rpx 30rpx 10rpx;
		}

		.fonts {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}


		.popuptxt {
			font-size: 28rpx;
			text-align: center;
			margin-bottom: 40rpx;
		}

		/deep/.u-popupcontent {
			width: 84%;
		}

		.popupbtn {
			width: 230rpx;
			height: 80rpx;

			/deep/ .u-button__text {
				font-size: 36rpx !important;
			}
		}

		.two {
			color: #666666 !important;
		}

		.mileage_tit_s {
			font-size: 14px;
			color: #999999;
			margin-left: 12px;
			line-height: 40px;
		}

		.ui-list {
			padding: 0 30rpx;
			list-style: none;

			li {
				height: 80rpx;
				line-height: 80rpx;
				border-bottom: 1px solid #E9ECF7;

				.ui-list-info {
					display: flex;
					justify-content: space-between;
				}

				.ui-nowrap {
					font-weight: inherit;
					font-size: 14px;
					width: 70%;
				}

				.ui-nowrap-d {
					font-weight: inherit;
					font-size: 14px;
					width: 50%;
				}

				.ui-nowrap-l {
					font-weight: inherit;
					font-size: 13px;
					width: 76%;
				}

				.width100 {
					width: 100px;
					height: 80rpx;
				}

				/deep/ .u-input {
					height: 80rpx !important;
				}


				.mileage_input {
					color: #346CF2;
					font-size: 14px;
					height: 80rpx;
				}
			}
		}
	}

	.textnowrap {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.state-list {
		max-height: 800rpx;
		overflow-y: auto;
	}
</style>