* {
	margin: 0;
	padding: 0;
	font-size: 13rem;
}

.private {}

#container {
	height: 100vh;
	width: 100vw;
}

.contenrs {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 15rem;
	padding: 5rem;
}

.cont-top {}

.cont-tips {
	background: rgba(255, 255, 255, .85);
	margin-bottom: 2rem;
	padding: 4rem 15rem;
	border-top-left-radius: 5rem;
	border-top-right-radius: 5rem;
}

.cont-box {
	/* padding: 5rem 15rem; */
	border-radius: 3rem;
	background: rgba(255, 255, 255, .85);
	color: #666666;
	font-size: 14rem;
}

.cont-btm {
	margin-top: 5rem;
}

.cont-box .posIcon {
	width: 30rem;
	height: 30rem;
}

.cont-box .cont-tiemr {
	/* font-size: 13rem; */
}

.goMapBtn {
	width: 30% !important;
	margin-left: 2% !important;
	background: #FF7F19 !important;
	font-size: 14rem !important;
	color: #ffffff !important;
}

.allBtn {
	color: #ffffff !important;
	background: #3894FF !important;
	font-size: 14rem !important;
}

.borar {
	border-bottom: 1px solid #E9ECF7;
	padding: 5rem 15rem;
}

.end_box {
	background: #ffffff !important;
	width: 80%;
	border-radius: 4rem;
	padding: 0 10rem !important;
}

.ui-nowrap {
	font-size: 13rem !important;
}

.end_title {
	margin: 10rem;
	font-size: 15rem;
}

.end_title span {
	font-size: 15rem;
}


.popup_btn_box {
	margin: 10rem 0;
}


.amap-copyright {
	font-size: 0;
}

.amap-mcode {
	font-size: 0;
}

.amap-marker-label {
	padding: 4rem 10rem;
	border-radius: 7rem;
	line-height: 14rem;
	border: 1px solid #ccc !important;
}

.makarInfo {
	font-size: 11rem;
}

.makarInfo i {
	margin-left: 6rem;
}

.martb {
	/* margin: 10rem 0; */
	line-height: 25rem;
}

.fontw {
	font-weight: 600;
}

.fontmin {
	font-size: 10rem;
}

.fonts {
	font-size: 12rem;
}

.text-right {
	text-align: right;
}

.colorb {
	color: #346CF2;
}

.color-bg {
	background: #346CF2 !important;
	color: #ffffff;
}

.colorh {
	color: #666666;
}


/* 订单详情 */
.mapTip {
	background-color: #fff;
	padding: 4rem 10rem;
	border-radius: 3rem;
}

.mapTip span {
	color: #346CF2;
}

.u-flex {
	display: flex !important;
	/* justify-content: center; */
	align-items: center !important;
}

.u-flex-direction {
	flex-direction: column;
}

.u-row-around {
	justify-content: space-around;
}

.u-row-between {
	justify-content: space-between;
}
