## 1.0.8（2022-02-18）
1、提高保存图片的质量；2、解决部分安卓、IOS手机图片无法保存的问题；3、新年快乐
## 1.0.7（2021-09-24）
适配APP，解决APP无法获取到图片问题
## 1.0.6（2021-09-01）
解决上传大尺寸大图，绘制失败的问题
## 1.0.5（2021-08-20）
解决图片高大于宽，规定宽高比例 [5,2]，裁切时实际比例变成了[2,5]，不能实现按照比例来裁切
## 1.0.4（2021-08-19）
增加image图片地址watch，组件外更改图片地址时实时变更，非常感谢大佬（330***@163.com）的支持
## 1.0.3（2021-08-18）
增加属性original （默认值true） 是否按照原始大小裁切图片
## 1.0.2（2021-08-17）
新增属性
1. fileType 目标文件的类型，只支持 'jpg' 或 'png'。默认为 'jpg'；
2. quality 图片的质量，取值范围为 (0, 1]，不在范围内时当作1.0处理（微信小程序：目前仅对 jpg 有效）。默认为 1；
## 1.0.1（2021-08-17）
1. 增加image属性，首次打开可以使用默认图
2. 增加maxCropper属性，打开时是否展示最大裁剪框
## 1.0.0（2021-08-03）
方便后期使用，发布头像上传裁切组件，不定时更新
