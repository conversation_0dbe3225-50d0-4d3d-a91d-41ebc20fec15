<template>
	<view class="changePass">
		<u-navbar title="修改密码" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="form_box">
			<u--form labelPosition="left" labelWidth='0' :model="passForm" :rules="rules" ref="form">
				<u-form-item prop="oldPassword" borderBottom>
					<u--input type="password" v-model="passForm.oldPassword" border="none" placeholder="输入旧密码">
					</u--input>
				</u-form-item>

				<u-form-item prop="newPassword" borderBottom>
					<u--input type="password" v-model="passForm.newPassword" border="none" placeholder="输入新密码">
					</u--input>
				</u-form-item>
				<!--  -->
				<u-form-item prop="newpasst" borderBottom>
					<u--input type="password" v-model="passForm.newpasst" border="none" placeholder="确认新密码"></u--input>
				</u-form-item>
			</u--form>
			<view class="tips">
				密码最少6位，必须包括大写字母、小写字母、数字、特殊字符四种类型
			</view>
		</view>

		<view class="f_box">
			<u-button type="primary" color="#346CF2" text="提交确认" @click="submit()"></u-button>
		</view>
	</view>
</template>

<script>
	import {
		resetpassword
	} from '@/config/api.js';
	export default {
		data() {
			// 密码验证
			const pwdCheck = async (rule, value, callback) => {
				let reg = /^.*(?=.{6,})(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*?. ]).*$/
				if (value.length < 1) {
					return callback(new Error("新密码不能为空！"));
				} else if (value.length < 6) {
					return callback(new Error("新密码不能少于6位！"));
				} else if (value.length > 32) {
					return callback(new Error("新密码最长不能超过32位！"));
				} else if (!reg.test(value)) {
					return callback(new Error("新密码输入有误，请检查格式是否正确！"));
				} else {
					callback()
				}
			}
			// 重复密码验证
			const pwdAgainCheck = async (rule, value, callback) => {
				if (value.length < 1) {
					return callback(new Error("重复密码不能为空！"));
				} else if (this.passForm.newPassword != this.passForm.newpasst) {
					return callback(new Error("两次输入密码不一致！"));
				} else {
					callback()
				}
			};
			return {
				passForm: {
					oldPassword: '',
					newPassword: '',
					newpasst: ''
				},
				rules: {
					'oldPassword': {
						type: 'string',
						required: true,
						message: '请输入旧密码',
						trigger: ['blur']
					},
					'newPassword': {
						type: 'string',
						required: true,
						validator: pwdCheck,
						trigger: ['blur']
					},
					'newpasst': {
						type: 'string',
						required: true,
						validator: pwdAgainCheck,
						trigger: ['blur']
					},

				},
			};
		},
		methods: {
			submit() {
				let that = this
				that.$refs.form.validate().then(res => {
					if (that.passForm.newPassword != that.passForm.oldPassword) return uni.$u.toast('请检查新密码是否一致')
					resetpassword(that.passForm).then(data => {
						uni.$u.toast('操作成功')
						setTimeout(() => {
							uni.navigateBack()
						}, 1000)
					})
				}).catch(errors => {})
			},
		}
	}
</script>

<style lang="scss">
	.changePass {
		.form_box {
			margin: 20rpx 32rpx;
			background-color: #fff;
			padding: 0 20rpx;
		}

		.tips {
			color: #999999;
			font-size: 28rpx;
			padding: 30rpx 0;
		}

		.f_box {
			padding: 20rpx 32rpx;
		}
	}
</style>
