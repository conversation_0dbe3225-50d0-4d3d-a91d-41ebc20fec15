<template>
	<view class="cost">
		<u-navbar title="选择成本中心" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="filterBox">
			<u-search placeholder="搜索成本中心" :showAction="false" v-model="keyword" @search="queryList">></u-search>
		</view>
		<view class="cost_box">
			<view class="cost_box_tit">
				按部门选择
			</view>
			<view class="cost_box_content" v-for="(item,index) in dataList" :key="index">
				<u-radio-group
					size='14'
				    v-model="radiosj"
				    placement="column">
					<u-radio  class="cost_radio" :name="item" :label="item.orgName"></u-radio>
				</u-radio-group>
			</view>
			<u-empty v-if="dataList.length==0" mode="order" text='列表为空'
				icon="http://cdn.uviewui.com/uview/empty/order.png">
			</u-empty>
		</view>
		
		<view class="footer_box">
			<u-button type="primary" color="#346CF2" text="确认" @click="submit()"></u-button>
		</view>
	</view>
</template>

<script>
	import {
		costcenterorgbyregulation
	} from '@/config/api.js';
	export default {
		data() {
			return {
				pageParams:{},
				dataList:[],
				radiosj:{},
				keyword:''
			};
		},
		methods:{
			queryList() {
				costcenterorgbyregulation({
					params: {
						regulationId: this.pageParams.regulationId,
						userId: this.pageParams.userId,
						orgName: this.keyword
					}
				}).then((data) => {
					this.dataList=data
				})
			},
			submit(){
				let pages = getCurrentPages();  //获取跳转的所有页面
				let nowPage = pages[ pages.length - 1]; //当前页
				let prevPage = pages[ pages.length - 2 ]; //上一页
				 
				prevPage.$vm.costCenterOrgId = this.radiosj.orgId
				prevPage.$vm.costCenterOrgName = this.radiosj.orgName
				
				uni.navigateBack({
				    delta: 1
				});
			}
		},
		onLoad(option) {
			this.pageParams=option
			this.queryList()
		},
	}
</script>

<style lang="scss">
	.cost{
		.filterBox {
			padding: 27rpx 32rpx;
		}
		.cost_box{
			height:calc(100vh - 240rpx) ;
			padding: 0 32rpx;
			background-color: #fff;
			overflow: hidden;
			.cost_box_tit{
				font-size: 32rpx;
				margin-top: 30rpx;
			}
			/deep/.cost_box_content{
				margin:60rpx 0;
				/deep/.cost_radio{
					margin-bottom: 40rpx;
					/deep/.u-radio__text{
						margin-left: 30rpx;
						font-size: 28rpx;
					}
				}
			}
		}
	}

</style>
