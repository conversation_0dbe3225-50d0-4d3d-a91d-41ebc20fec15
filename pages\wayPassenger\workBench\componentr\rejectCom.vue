<template>
	<view class="popup_bg">
		<view class="popup_tit">
			{{rejectName}}申请
		</view>
		<view class="popup_txt">
			是否确定{{rejectName}}用车申请
		</view>
		<view class="popupcbox">
			<u--textarea :placeholder="'请输入'+rejectName+'原因'" v-model="checkCompOrderVo.checkRemark" height="107"
				confirmType="done">
			</u--textarea>
		</view>
		<view class="selectbox" v-if="rejectObj.useCarType==1 || rejectObj.useCarType==2 ||rejectObj.useCarType==3">
			<view class="u-flex">
				<view class="u-flex-4" @click="selectshow=true">
					驳回方式：
				</view>
				<view class="u-flex-9 select_input" @click="selectshow=true">
					<u--input placeholder="请选择驳回方式" border="surround" v-model="checkCompOrderVo.checkvalue" readonly>
					</u--input>
				</view>
			</view>
		</view>
		<view class="u-flex u-row-around popup_btn_box select_input">
			<view class="popup_btn">
				<u-button color="#346CF2" type="primary" text="确定" @click="adoptFun()">
				</u-button>
			</view>
			<view class="popup_btn">
				<u-button class="two" color="#E9ECF7" type="primary" text="取消" @click="rejectclose()">
				</u-button>
			</view>
		</view>
		<!-- 下拉组件 -->
		<u-picker :show="selectshow" :columns="modearray" keyName="name" @cancel="selectshow=false" @confirm="qdselect"
			style="width: 100vw;">
		</u-picker>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				checkCompOrderVo: {
					checkRemark: null,
					checkvalue: '',
					rejectType: null
				},
				modearray: [
					[{
							name: '逐级驳回',
							val: 1
						},
						{
							name: '申请人',
							val: 2
						}
					],
				],
				selectshow: false
			};
		},
		props: ['rejectObj', 'rejectName'],
		mounted() {},
		methods: {
			qdselect(e) {
				this.checkCompOrderVo.checkvalue = e.value[0].name;
				this.checkCompOrderVo.rejectType = e.value[0].val;
				this.selectshow = false
			},
			adoptFun() {
				if (!this.checkCompOrderVo.checkRemark) return uni.$u.toast('请填写驳回原因')
				let objr = {
					checkRemark: this.checkCompOrderVo.checkRemark,
					isAgree: 0,
					rejectType: this.checkCompOrderVo.rejectType,
					applyId: this.rejectObj.applyId
				}
				this.$emit('adoptFun', objr)
				this.checkCompOrderVo = {}
			},
			rejectclose() {
				this.$emit('adoptFun')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.popupcbox {
		padding: 0 53rpx;
	}

	.select_input {

		margin: 20rpx 0;
	}

	.selectbox {
		padding: 0 53rpx;
		font-size: 28rpx;
	}

	.popup_btn_box {

		padding-bottom: 27rpx;
	}

	.popup_tit {
		font-size: 36rpx;
		text-align: center;
		padding: 40rpx 0 30rpx 0;
	}

	.popup_txt {
		font-size: 28rpx;
		text-align: center;
		margin-bottom: 40rpx;
	}

	/deep/.u-popup__content {
		width: 84%;
	}

	.popup_btn {
		width: 230rpx;
		height: 80rpx;
	}

	.two {
		color: #666666 !important;
	}

	/deep/ .u-popup__content {
		width: 100% !important;
	}
</style>