<template>
	<view>
		<view class="text-center">
			请选择您的能源类型
		</view>
		<view class="conten u-center ">
			<view class="left u-center u-flex-direction" @click="goBack(1)">
				<u-icon name="https://zqcx.di-digo.com/app/image/jiayou.png" size="50"></u-icon>
				<span>加油</span>
			</view>
			<view class="right u-center u-flex-direction" @click="goBack(8)">
				<u-icon name="https://zqcx.di-digo.com/app/image/chongdian.png" size="50"></u-icon>
				<span>充电</span>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {
			goBack(t) {
				if(t==8) return uni.$u.toast('开发中！~~')
				this.$emit('changeBack',t)
			},
		}
	}
</script>

<style scoped>
	.text-center {
		text-align: center;
		font-size: 40rpx;
		margin: 15% 0;
		font-weight: bold;
	}

	.conten {
		/* width: 100%;
		height: 100vh; */
		height: 50vh;
		font-weight: bold;
		font-size: 50rpx;
	}

	.left {
		height: 100%;
		width: 30%;
		background-color: rgba(147, 210, 243, 0.5);
		border-top-right-radius: 120rpx;
		border-bottom-left-radius: 120rpx;
		color: rgba(16, 16, 16, 1);
	}

	.right {
		height: 100%;
		width: 30%;
		background-color: #f9e6cb;
		margin-top: 100rpx;
		margin-left: 20rpx;
		border-top-right-radius: 120rpx;
		border-bottom-left-radius: 120rpx;
	}

	span {
		margin-top: 40rpx;
	}
</style>