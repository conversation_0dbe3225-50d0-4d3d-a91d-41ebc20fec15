<template>
	<view class="contenr">
		<u--form :model="form" :rules="rulesr" ref="uForm" class="is-bottom" label-width="100" labelAlign="right">
			<view class="formBg">
				<u-form-item :required="true" label="选择车辆 :" prop="carNumber" borderBottom @click="opneBtn(1)">
					<u--input v-model="form.carNumber" disabled disabledColor="#ffffff" placeholder="请选择车辆"
						border="none"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item label="品牌型号 :" prop="brandModelName" borderBottom>
					<u-input v-model="form.brandModelName" border="none" placeholder="根据选择车辆自动填充品牌型号" disabled
						disabledColor="#ffffff" />
				</u-form-item>
			</view>
			<view class="formBg">
				<u-form-item :required="true" :label="`${tr==2?'送修':'送保'}日期 :`" prop="maintainTime" borderBottom
					@click="openTiem(1)">
					<u--input v-model="form.maintainTime" disabled disabledColor="#ffffff"
						:placeholder="`请选择${tr==2?'送修':'送保'}日期`" border="none">
					</u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item :required="true" :label="`${tr==2?'送修':'送保'}里程 :`" prop="maintainMileage" borderBottom>
					<u-input v-model="form.maintainMileage" border="none" type='number'
						:placeholder="`请选择${tr==2?'送修':'送保'}里程`" />
					<text slot="right">公里</text>
				</u-form-item>
				<u-form-item :required="true" label="提车日期 :" prop="takeCarTime" borderBottom @click="openTiem(2)">
					<u--input v-model="form.takeCarTime" disabled disabledColor="#ffffff" placeholder="请选择提车日期"
						border="none"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item :required="true" label="提车里程 :" prop="dashboardMileage" borderBottom>
					<u-input v-model="form.dashboardMileage" border="none" type='number' placeholder="请输入提车里程" />
					<text slot="right">公里</text>
				</u-form-item>
			</view>
			<view class="formBg">
				<u-form-item label="维修商 :" prop="maintainComp" borderBottom>
					<u-input v-model="form.maintainComp" border="none" placeholder="请输入维修商" />
				</u-form-item>
				<u-form-item :required="true" label="维修单号 :" prop="maintainCode" borderBottom>
					<u-input v-model="form.maintainCode" border="none" placeholder="请输入维修单号" />
				</u-form-item>
				<u-form-item :label="`${tr==2?'维修':'保养'}费用 :`" prop="maintainPayCost" borderBottom>
					<u-input v-model="form.maintainPayCost" border="none" placeholder="请输入实付金额" type='number' />
					<text slot="right">元</text>
				</u-form-item>
			</view>
			<view class="formBg">
				<u-form-item :label="`${tr==2?'维修':'保养'}明细 :`" prop="timer" borderBottom>
					<u-icon name="plus" slot="right" @click="popuprOpen('add')"></u-icon>
				</u-form-item>
				<view>
					<u-collapse :value="popupList.map((e,idx)=>{return idx})">
						<u-collapse-item v-for="(item,index) in popupList" :key="index">
							<view slot="title" class="u-align-items u-row-between">
								<text>项目{{index+1}} :</text>
								<text>{{item.itemCost}}元</text>
							</view>
							<view class="collBox">
								<u-cell-group>
									<u-cell :title="`${tr==2?'维修':'保养'}项目 :`" :value="item.maintainItem">
										<u-icon name="edit-pen" slot='right-icon' :size='20'
											@click='popuprEdit(item,index)'>
										</u-icon>
									</u-cell>
									<u-cell title="材料费 :" :value="`${item.materialCost} 元`">
										<u-icon name="edit-pen" slot='right-icon' :size='20'
											@click='popuprEdit(item,index)'>
										</u-icon>
									</u-cell>
									<u-cell title="工时费 :" :value="`${item.timeCost} 元`">
										<u-icon name="edit-pen" slot='right-icon' :size='20'
											@click='popuprEdit(item,index)'>
										</u-icon>
									</u-cell>
									<u-cell title="其他费用 :" :value="`${item.otherCost} 元`">
										<u-icon name="edit-pen" slot='right-icon' :size='20'
											@click='popuprEdit(item,index)'>
										</u-icon>
									</u-cell>
									<u-cell title=" ">
										<u-icon name='trash-fill' slot='right-icon' :size='22' color='red'
											@click="delBtn(index)"></u-icon>
									</u-cell>
								</u-cell-group>
							</view>
						</u-collapse-item>
					</u-collapse>
				</view>
			</view>
			<view class="formBg ">
				<u-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic" multiple :maxCount="8"
					class="pding"></u-upload>
			</view>

			<view class="formBg pding wx_pd" >
				<text>备注说明 :</text>
				<u--textarea v-model="form.remark" placeholder="请输入备注说明" confirmType="done"></u--textarea>
			</view>
			<!--保养是否开启提醒 -->
			<view class="formBg" v-if="tr==3">
				<u-form-item label="是否开启保养提醒 :" prop="isOpen" borderBottom label-width="160">
					<u-switch slot="right" v-model="form.isOpen" @change="switchChange"></u-switch>
				</u-form-item>
				<view class="map_detail_scoll" :class="{'active':form.isOpen}" v-if="form.isOpen">
					<u-form-item :required="true" label="保养日期 :" prop="nextMaintainDate" borderBottom
						@click="openTiem(3)">
						<u--input v-model="form.nextMaintainDate" disabled disabledColor="#ffffff" placeholder="请选择保养日期"
							border="none"></u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
					<u-form-item :required="true" label="保养公里 :" prop="nextMaintainMileage" borderBottom>
						<u-input v-model="form.nextMaintainMileage" border="none" type='number' placeholder="请输入保养公里" />
						<text slot="right">公里</text>
					</u-form-item>
					<u-form-item :required="true" label="提醒人员 :" prop="userNamer" borderBottom @click='peopleShow=true'>
						<u--input v-model="form.userNamer" disabled disabledColor="#ffffff" placeholder="请选择提醒人员"
							border="none"></u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
					<u-form-item :required="true" label="提醒天数 :" prop="Daysr" borderBottom>
						<u-input v-model="form.Daysr" type='number' border="none" placeholder="请输入提醒天数" />
					</u-form-item>
					<u-form-item :required="true" label="提醒时间 :" prop="tiemr" borderBottom @click='remindShow=true'>
						<u--input v-model="form.tiemr" disabled disabledColor="#ffffff" placeholder="请选择提醒时间"
							border="none"></u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>
			</view>
		</u--form>
		<view class="formBg pding btn u-align-items u-row-around">
			<view class="" style="width: 70%;">
				<u-button type="primary" color="#346CF2" text="提交确认" @tap="submit()"></u-button>
			</view>
			<view class="" style="width: 25%;">
				<u-button type="primary" color="#346CF2" text="上报列表" @click="goRouter()"></u-button>
			</view>
		</view>


		<!-- 弹窗 -->
		<popupr :show="popupShow" :title="`添加${tr==2?'维修':'保养'}项目`" @close="popuprClose">
			<slot>
				<u--form :model="popupForm" :rules="popupRules" ref="popupFormr" label-width="90" class="is-popup">
					<u-form-item :label="`${tr==2?'维修':'保养'}项目 :`" prop="maintainItem">
						<u-input v-model="popupForm.maintainItem" placeholder="请输入保养项目" />
					</u-form-item>
					<u-form-item label="材料费 :" prop="materialCost">
						<u-input v-model="popupForm.materialCost" placeholder="请输入材料费" type="number" @input="sumBtn" />
						<text slot="right">元</text>
					</u-form-item>
					<u-form-item label="工时费 :" prop="timeCost">
						<u-input v-model="popupForm.timeCost" placeholder="请输入工时费" type="number" @input="sumBtn" />
						<text slot="right">元</text>
					</u-form-item>
					<u-form-item label="其他费用 :" prop="otherCost">
						<u-input v-model="popupForm.otherCost" placeholder="请输入其他费用" type="number" @input="sumBtn" />
						<text slot="right">元</text>
					</u-form-item>
					<u-form-item label="小计 :" prop="itemCost">
						<u--input v-model="popupForm.itemCost" disabled disabledColor="#ffffff" placeholder="自动计算"
							border="none"></u--input>
						<text slot="right">元</text>
					</u-form-item>
				</u--form>
				<u-button type="primary" color="#346CF2" text="保存" @click="popuprBtn()"></u-button>
			</slot>
		</popupr>

		<popupr :show="peopleShow" title="选择提醒人员" @close="peopleShow=false">
			<slot>
				<people :list='peopleList' @peopleBtn='peopleVal'></people>
			</slot>
		</popupr>

		<!-- 日期选择 -->
		<u-datetime-picker :show="timeShow" v-model="timeMode" mode="date" :formatter="formatter" @cancel="tiemBtn"
			:closeOnClickOverlay='true' @close="tiemBtn" @confirm='confirms'>
		</u-datetime-picker>

		<u-datetime-picker :show="remindShow" v-model="remindTiem" mode="time" @cancel="closeBtn" @close="closeBtn"
			@confirm='remindConf'></u-datetime-picker>

		<!-- 车辆 选择器 -->
		<u-picker :show="pickerShow" :columns="columnsr" keyName="carNumber" :closeOnClickOverlay='true'
			@confirm="selectConfirm" @cancel="pickerBtn" @close="pickerShow=false"></u-picker>
	</view>
</template>

<script>
	import {
		uploadImg
	} from '@/config/consoler.js'
	import popupr from '../popupr/popupr.vue'
	import people from '../people/people.vue'
	export default {
		props: ['carList', 'typers'],
		components: {
			popupr,
			people
		},
		data() {
			let tiemrs = () => {
				let myDate = new Date()
				let er = (v) => {
					return v < 10 ? `0${v}` : v
				}
				return `${myDate.getHours()}:${er(myDate.getMinutes())}`
			}
			return {
				tr: null,
				form: {
					// name: "",
					isOpen: false,
				},
				rulesr: {
					'carNumber': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'maintainTime': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'maintainMileage': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'takeCarTime': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'dashboardMileage': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'maintainCode': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'nextMaintainDate': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'nextMaintainMileage': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
				},
				fileList: [],
				// customStyle: ,
				timeShow: false,
				timeMode: Number(new Date()),
				pickerShow: false,
				tiemType: null,
				coluType: null,
				columnsr: [],
				maintain: [],
				popupShow: false,
				popupForm: {
					maintainItem: '',
					otherCost: '',
					timeCost: '',
					materialCost: '',
					itemCost: null
				},
				popupRules: {
					'maintainItem': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'otherCost': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'timeCost': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'materialCost': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
				},
				popupList: [],
				idxr: null,
				typer: null,
				peopleShow: false,
				peopleList: [],
				keyword: null,
				remindTiem: tiemrs(),
				remindShow: false,
			}
		},
		mounted() {
			this.tr = this.typers
			console.log(this.typers, 'this.carList');
			this.initr()
		},
		methods: {
			goRouter() {
				uni.$u.route({
					url: '/pager/vehicleEscala/vehicleEscala',
					params: {
						id: this.typers,
					},
				})
			},
			// 车辆 打开
			opneBtn(t) {

				this.coluType = t
				if (t == 1) {
					this.columnsr = this.carList
				}
				this.pickerBtn()
			},
			// 车辆 选择
			selectConfirm(e) {
				let o = e.value[0]
				if (this.coluType == 1) {
					this.$set(this.form, 'carId', o.carId)
					this.$set(this.form, 'brandModelName', o.brandModelName)
					this.$set(this.form, 'carNumber', o.carNumber)
				}
				this.pickerShow = false
			},
			// 是否开启保养提醒
			switchChange(e) {
				if (!e) {
					this.form.nextMaintainDate = null
					this.form.nextMaintainMileage = null
					this.form.userNamer = null
					this.form.Daysr = null
					this.form.tiemr = null
				}
			},
			// 删除
			delBtn(idx) {
				this.popupList.splice(idx, 1)
			},
			// 保存保养项目
			popuprBtn() {
				this.$refs.popupFormr.validate().then(res => {
					if (this.typer == 'add') {
						this.popupList.push(JSON.parse(JSON.stringify(this.popupForm)))
					} else if (this.typer == 'edit') {
						this.popupList.splice(this.idxr, 1, JSON.parse(JSON.stringify(this.popupForm)))
					}
					this.popuprClose(null)
				}).catch(errors => {})

			},
			// 修改保养项目
			popuprEdit(item, idx) {
				this.$set(this, 'popupForm', item)
				this.$set(this, 'idxr', idx)
				this.popuprOpen('edit')

			},
			closeBtn() {
				this.remindShow = false
			},
			popuprClose() {
				this.popupForm = {
					maintainItem: null,
					otherCost: null,
					timeCost: null,
					materialCost: null,
					itemCost: null
				}
				this.popupShow = false
				// this.popuprOpen(null)
			},
			popuprOpen(type) {
				let that = this
				that.typer = type
				that.popupShow = true

				console.log(this.popupShow, 'this.popupShow');
			},
			// 计算
			sumBtn() {
				let zero = (e) => {
					return e ? Number(e) : 0
				}
				this.popupForm.itemCost = zero(this.popupForm.otherCost) + zero(this.popupForm.timeCost) + zero(this
					.popupForm.materialCost)
			},
			// 打开时间
			openTiem(t) {
				this.tiemType = t
				this.tiemBtn()
			},
			// 选中时间
			confirms(e) {
				if (this.tiemType == 1) {
					this.$set(this.form, 'maintainTime', uni.$u.timeFormat(e.value, 'yyyy-mm-dd'))
				}
				if (this.tiemType == 2) {
					this.$set(this.form, 'takeCarTime', uni.$u.timeFormat(e.value, 'yyyy-mm-dd'))
				}
				if (this.tiemType == 3) {
					this.$set(this.form, 'nextMaintainDate', uni.$u.timeFormat(e.value, 'yyyy-mm-dd'))
				}
				this.tiemBtn()
			},
			remindConf(e) {
				this.$set(this.form, 'tiemr', e.value)
				this.remindShow = false
			},
			// 时间开关
			tiemBtn() {
				this.timeShow = !this.timeShow
			},
			// 车辆选择
			pickerBtn() {
				this.pickerShow = true
			},

			// 时间格式化
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				return value
			},
			// 保存
			submit() {
				this.$refs.uForm.validate().then(res => {
					if (this.tr == 3) {
						this.form.isOpenPush = this.form.isOpen ? 1 : 0
						let obj = {
							date: this.form.Daysr,
							time: this.form.tiemr
						}
						this.form.advancePushTime = JSON.stringify(obj)
						this.form.pushUserIds = this.peopleList.filter(v => v.radio == true).map(e => e.userId)
							.join()
					}
					if (this.popupList.length > 0) {
						this.form.maintainCost = 0
						this.popupList.forEach(v => {
							this.form.maintainCost += v.itemCost
						})
					}
					this.form.maintainType = this.tr == 2 ? 1 : 2
					this.form.maintainDetail = JSON.stringify(this.popupList)
					this.form.imgUrls = this.fileList.map(res => {
						return res.urlr
					}).join()
					uni.$u.toast('提交成功')
					setTimeout(() => {
						this.$emit('preserva', this.form)
					}, 500)
				}).catch(errors => {
					console.log(errors)
					uni.$u.toast('校验失败')
				})
			},
			// 选择人员
			peopleVal(arrs) {
				let that = this
				if (arrs.length == 0) return uni.$u.toast('请选择人员')
				let listr = that.peopleList.map((v) => {
					return v.empId
				})
				let newListr = []
				arrs.forEach((v, index) => {
					if (!listr.includes(v.empId)) {
						newListr.push(v)
					}
				})
				let text = newListr.map((e) => {
					return e.name
				}).join()
				console.log(text, 'text');
				if (!text) return uni.$u.toast('请勿选择重复提醒人员')
				uni.showModal({
					title: '提示',
					content: `您本次选择提醒的人员：${text}`,
					success: function(res) {
						if (res.confirm) {
							that.peopleList.push(...newListr)
							let namer = that.peopleList.map((e) => {
								return e.name
							}).join()
							that.$set(that.form, 'userNamer', namer)
							that.peopleShow = !that.peopleShow
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				})
			},
			// 获取
			initr() {

			},
			// 上传附件
			afterRead(event) {
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList`].length
				lists.map((item) => {
					this[`fileList`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				lists.forEach(r => {
					uploadImg(r, 4).then(res => {
						if (res) {
							let item = this[`fileList`][fileListLen]
							this[`fileList`].splice(fileListLen, 1, Object.assign(item, {
								status: 'success',
								message: '',
								urlr: res
							}))
							fileListLen++
						} else {
							this[`fileList`].splice(fileListLen, 1)
							uni.$u.toast('上传失败')
						}
					})
				})
			},
			// 删除附件
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},

		}
	}
</script>

<style lang="scss" scoped>
	.is-bottom {
		padding-bottom: 120rpx;
	}

	.formBg {
		margin: 20rpx 0;
		background-color: #fff;

		/deep/.u-form-item__body__left__content__label {
			display: flex;
			justify-content: flex-end !important;
			padding-right: 10rpx;
		}
		
		/deep/.item__body__right__content__icon {
			padding-right: 30rpx;
		}
		
		/deep/.u-form-item__body {
			padding: 30rpx 0;
		}
	}

	.btn {
		position: fixed;
		bottom: 0;
		width: calc(100% - 60rpx);
		margin: 0 !important;
		border-top: 4rpx solid #f3f3f3;
		z-index: 99;
	}

	.pding {
		padding: 20rpx 30rpx;
		
	}
	.wx_pd {
		/* #ifdef MP-WEIXIN */
		padding-bottom: 120rpx;
		/* #endif */
	}

	.map_detail_scoll {
		height: 0;
		transition: all .3s linear 0s;
		overflow-y: auto;
	}

	.map_detail_scoll.active {
		height: 540rpx;
		transition: all .3s linear 0s;
	}

	/deep/.u-form-item__body__left__content__label {
		display: flex;
		justify-content: end !important;
		padding-right: 10rpx;
	}

	/deep/.u-collapse-item__content__text {
		padding: 0;
	}

	/deep/uni-modal {
		z-index: 10099 !important;
	}

	.is-popup {
		/deep/.item__body__right__content__icon {
			padding-left: 20rpx;
		}

		/deep/.u-form-item__body__left__content__required {
			left: 0;
		}
	}
	

	.is-bottom {
		/deep/.u-form-item__body {
			padding: 30rpx 0;
		}

		/deep/.item__body__right__content__icon {
			padding-right: 30rpx;
		}

		/deep/.u-form-item__body__left__content__required {
			left: 10rpx;
		}
	}
</style>
