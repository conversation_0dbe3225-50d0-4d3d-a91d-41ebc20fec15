<template>
	<view class="statementTwo">
		<view class="popuptit fonts">
			<u-icon name="arrow-left" size="24" @click="backClick(1)"></u-icon>
			<view>订单费用信息确认</view>
			<view></view>
		</view>
		<ul class="ui-list" style="overflow-y: auto;max-height: 800rpx;">
			<li style="background-color: #f5f5f5;border-top-left-radius: 10px;border-top-right-radius: 10px;">
				<view class="ui-list-info">
					<h5 class="ui-nowrap-d">服务里程：</h5>
					<view class="ui-txt-info" style="color: #262626;">{{costDetail.actualDistance /1000}}公里</view>
				</view>
			</li>

			<li style="background-color: #f5f5f5;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px; ">
				<view class="ui-list-info">
					<h5 class="ui-nowrap-d">服务时长：</h5>
					<view class="ui-txt-info" style="color: #262626;">{{costDetail.actualDuration | transDay}}</view>
				</view>
			</li>

			<!-- 记录 -->
			<li v-for="(item,index) in costDetail.feeDetail" :key="index">
				<view class="ui-list-info">
					<h5 class="ui-nowrap">{{item.key}}：</h5>
					<view class="ui-txt-info" style="color: #262626;">{{item.value?item.value:0}}元</view>
				</view>
			</li>


			<li>
				<view class="ui-list-info">
					<h5 class="ui-nowrap font_bold">订单金额：</h5>
					<view class="ui-txt-info" style="color: #262626;">{{costDetail.totalFee}}元</view>
				</view>
			</li>

			<li v-if="costDetail.discountReduceFee">
				<view class="ui-list-info">
					<h5 class="ui-nowrap font_bold">企业折扣：</h5>
					<view class="ui-txt-info" style="color: #262626;">{{costDetail.discountReduceFee}}元</view>
				</view>
			</li>

			<li>
				<view class="ui-list-info">
					<h5 class="ui-nowrap font_bold" style='color: red;'>优惠金额：</h5>
					<view class="ui-txt-info" style="width: 65%;">
						<u--input class="mileage_input" :customStyle="heightr" v-model="costDetail.reduceFee"
							inputAlign='right' border="none" placeholder="请输入" type="digit"></u--input>
					</view>
				</view>
			</li>

			<li>
				<view class="ui-list-info">
					<h5 class="ui-nowrap font_bold">实付金额：</h5>
					<view class="ui-txt-info" style="color: #262626;">
						{{moneyr(costDetail.totalFee,costDetail.discountReduceFee,costDetail.reduceFee)}}元
					</view>
				</view>
			</li>

			<li>
				<view class="ui-list-info">
					<h5 class="ui-nowrap font_bold">备注：</h5>
					<view class="ui-txt-info" style="width: 65%;">
						<u--input class="mileage_input" :customStyle="heightr" v-model="costDetail.remark"
							inputAlign='right' border="none" placeholder="请输入修改费用备注"></u--input>
					</view>
				</view>
			</li>


			<!-- <li v-for="(item,index) in costDetail.otherFee" :key="index">
				<view class="ui-list-info">
					<h5 class="ui-nowrap font_bold">{{item.key}}：</h5>
					<view class="ui-txt-info" style="width: 65%;">
						<u--input class="mileage_input" :customStyle="heightr" v-model="item.value" inputAlign='right'
							border="none" :placeholder="`请输入${item.key}`" type="digit"></u--input>
					</view>
				</view>
			</li> -->

		</ul>

		<view class="u-flex u-row-around popupbtn_box">
			<view class="popupbtn">
				<u-button color="#346CF2" type="primary" text="确定" @click="endThree()"></u-button>
			</view>
			<view class="popupbtn">
				<u-button class="popupbtn two" color="#E9ECF7" type="primary" text="取消" @click="backClick(2)">
				</u-button>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				heightr: {
					height: '80rpx'
				},
				costDetail: {}
			}
		},
		props: ['stateObj', 'oneStateObj', 'stateType'],
		filters: {
			transDay(time) {
				let d = parseInt(time / 60 / 60 / 24)
				let h = parseInt(time / 60 / 60 % 24)
				h = h < 10 ? '0' + h : h
				let m = parseInt(time / 60 % 60)
				m = m < 10 ? '0' + m : m
				let s = parseInt(time % 60)
				s = s < 10 ? '0' + s : s
				// 作为返回值返回
				return d + "天" + h + "小时" + m + "分钟"
			}
		},
		mounted() {
			this.costDetail = this.stateObj
		},
		methods: {
			endThree() {
				let param = {
					...this.costDetail,
				}
				param.otherFee = param.feeDetail.filter(v => {
					return v.feeDetailCode == 200
				})
				param.otherFee = JSON.stringify(param.otherFee)
				param.actualStartTime = uni.$u.timeFormat(param.actualStartTime, 'yyyy-mm-dd hh:MM:ss')
				param.actualEndTime = uni.$u.timeFormat(param.actualEndTime, 'yyyy-mm-dd hh:MM:ss')
				//  ture 修改费用 false 结单
				if (this.stateType) {
					this.$emit('stateTwo', 4, param)
				} else {
					this.$emit('stateTwo', 3, param)
				}
			},
			backClick(typer) {
				if (typer == 1) this.$emit('stateTwo', 1, null, this.oneStateObj)
				if (typer == 2) this.$emit('stateTwo', 2, null)
			},
			moneyr(m1, m2, m3) {
				m1 = m1 ? m1 : 0
				m2 = m2 ? m2 : 0
				m3 = m3 ? m3 : 0
				let sumr = m1 - m2 - m3
				return sumr.toFixed(2)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.popupcbox {
		padding: 0 53rpx;
	}

	.select_input {
		height: 60rpx;
		border: 1px solid #CCCCCC;
		border-radius: 8rpx;
		padding-left: 20rpx;
		line-height: 60rpx;
		color: #999;
		margin: 20rpx 0;
	}

	.selectbox {
		padding: 0 53rpx;
		font-size: 28rpx;
	}

	.popupbtn_box {
		padding-bottom: 27rpx;
		margin-top: 30rpx;
	}

	.popuptit {
		font-size: 36rpx;
		text-align: center;
		padding: 40rpx 10rpx 30rpx 10rpx;
	}

	.fonts {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}


	.popuptxt {
		font-size: 28rpx;
		text-align: center;
		margin-bottom: 40rpx;
	}

	/deep/.u-popupcontent {
		width: 84%;
	}

	.popupbtn {
		width: 230rpx;
		height: 80rpx;

		/deep/ .u-button__text {
			font-size: 36rpx !important;
		}
	}

	.two {
		color: #666666 !important;
	}

	.mileage_tit_s {
		font-size: 14px;
		color: #999999;
		margin-left: 12px;
		line-height: 40px;
	}

	.ui-list {
		padding: 0 30rpx;
		list-style: none;

		li {
			height: 80rpx;
			line-height: 80rpx;
			border-bottom: 1px solid #E9ECF7;



			.ui-list-info {
				display: flex;
				justify-content: space-between;
			}

			.ui-nowrap {
				font-weight: inherit;
				font-size: 14px;
				// width: 40%;
			}

			.ui-nowrap-d {
				font-weight: inherit;
				font-size: 14px;
				width: 50%;
			}

			.ui-nowrap-l {
				font-weight: inherit;
				font-size: 13px;
				width: 76%;
			}

			.width100 {
				width: 100px;
				height: 80rpx;
			}

			/deep/ .u-input {
				height: 80rpx !important;
			}


			.mileage_input {
				color: #346CF2;
				font-size: 14px;
				height: 80rpx;
			}
		}
	}

	/deep/ .u-input {
		height: 80rpx !important;
	}
</style>