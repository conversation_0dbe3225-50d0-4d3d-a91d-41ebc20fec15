<!doctype html>
<html>
	<head>
		<meta charset="utf-8">
		<title>地址</title>
		<meta name="keywords" content="" />
		<meta name="description" content="" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
		<meta name="format-detection" content="telephone=no" />
		<!-- 	<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black"> -->
		<meta name="author" content="CSS5, css5.com.cn" />
		<link rel="stylesheet" href="https://i.gtimg.cn/vipstyle/frozenui/2.0.0/css/frozen.css">
		<link rel="stylesheet" href="css/font-awesome.min.css" />
		<link rel="stylesheet" type="text/css" href="css/mobileSelect.css">
		<link rel="stylesheet" type="text/css" href="css/hybird.css">
		<script src="https://cdn.bootcss.com/vue/2.6.11/vue.js"></script>
		<script type="text/javascript">
			window._AMapSecurityConfig = {
				securityJsCode: 'a0ec29ceac10861c863c75bcf78565a4',
			}
		</script>

		<script src="https://webapi.amap.com/loader.js"></script>
		<!-- 引入样式和js文件 -->
		<script src="js/mobileSelect.js" type="text/javascript"></script>
		<script type="text/javascript" src="js/area.js"></script>
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		<script type="text/javascript" src="js/axios.js"></script>
	</head>
	<style>
		/* body{
			overflow: hidden !important;
			touch-action: none !important;
		} */
	</style>

	<body>
		<div id="app">
			<div class="ui-searchbar-wrap ui-border-b focus">
				<div class="ui-searchbar ui-border-radius">
					<div id="area" class="selcet_lon ui-nowrap ui-whitespace">{{currentPos}}</div>
					<i class="fa fa-angle-down"></i>
					<div class="line-s"></div>
					<div class="ui-searchbar-input"><input v-model="objective" @input="aMapSearchNearBy"
							placeholder="搜索" autocapitalize="off">
					</div>
				</div>
			</div>

			<ul class="ui-list ui-list-function posBox">
				<li v-for="(item,index) in listdata" :key="index" @click="goBack(item)">
					<div class="ui-avatar posIcon">
						<span style="background-image:url(http://placeholder.qiniudn.com/100x100)"></span>
					</div>
					<div class="ui-list-info">
						<h5 class="ui-nowrap">{{item.name}}</h5>
						<p>{{item.address}}</p>
					</div>
				</li>
			</ul>

		</div>
	</body>
	<!-- 微信 JS-SDK 如果不需要兼容小程序，则无需引用此 JS 文件。 -->
	<script type="text/javascript" src="//res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
	<!-- uni 的 SDK -->
	<script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script>
	<script>
		document.addEventListener('UniAppJSBridgeReady', function() {});
	</script>

	<script>
		new Vue({
			el: '#app',
			data: {
				currentPos: "加载中...",
				objective: "",
				toAreaCode: '',
				listdata: [],
				urlObj: {}
			},
			mounted() {
				this.urlObj = this.getQueryString()
				this.init()

				console.log(this.urlObj)
			},
			methods: {
				init() {
					let that = this
					// 高德地图控件
					AMapLoader.load({
						"key": "1cb53fa0e69dc44036161409f1d4039c", // 申请好的Web端开发者Key，首次调用 load 时必填
						"version": "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
						"plugins": [], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
						"AMapUI": { // 是否加载 AMapUI，缺省不加载
							"version": '1.1', // AMapUI 版本
							"plugins": ['overlay/SimpleMarker'], // 需要加载的 AMapUI ui插件
						},
						"Loca": { // 是否加载 Loca， 缺省不加载
							"version": '2.0' // Loca 版本
						},
					}).then((AMap) => {
						// var map = new AMap.Map('container');
						this.getLoction()
					}).catch((e) => {
						console.error(e); //加载错误提示
					});

					// 地区选择控件
					var mobileSelect4 = new MobileSelect({
						trigger: '#area',
						title: '地区选择',
						wheels: areaArr,
						keyMap: {
							id: 'id',
							value: 'value',
							childs: 'childs'
						},
						transitionEnd: function(indexArr, data) {
							console.log(data);
						},
						callback: function(indexArr, data) {
							console.log(data);
							that.currentPos = data[1].value
							that.toAreaCode = data[1].id
						}
					});

				},
				aMapSearchNearBy() {
					let that = this
					AMap.plugin(['AMap.PlaceSearch'], function() {
						var PlaceSearchOptions = { //设置PlaceSearch属性
							city: that.currentPos, //城市
							citylimit: true,
							pageSize: 10, //每页结果数,默认10
							pageIndex: 1, //请求页码，默认1
						};
						var MSearch = new AMap.PlaceSearch(PlaceSearchOptions); //构造PlaceSearch类
						MSearch.search(that.objective, function(status, result) {
							console.log(result)
							if (result.info === 'OK') {
								that.listdata = result.poiList.pois
							} else {
								console.log('获取位置信息失败!');
							}
						});

					});
				},
				getLoction() {
					let that = this
					AMap.plugin('AMap.CitySearch', function() {
						var citySearch = new AMap.CitySearch()
						citySearch.getLocalCity(function(status, result) {
							if (status === 'complete' && result.info === 'OK') {
								// 查询成功，result即为当前所在城市信息
								console.log(result)
								that.currentPos = result.city
								that.toAreaCode = result.adcode
								AMap.plugin(['AMap.PlaceSearch'], function() {
									var PlaceSearchOptions = { //设置PlaceSearch属性
										city: that.currentPos, //城市
										citylimit: true,
										pageSize: 10, //每页结果数,默认10
										pageIndex: 1, //请求页码，默认1
									};
									var MSearch = new AMap.PlaceSearch(
									PlaceSearchOptions); //构造PlaceSearch类
									MSearch.search(" ", function(status, result) {
										console.log(result)
										if (result.info === 'OK') {
							 			that.listdata = result.poiList.pois
										} else {
											console.log('获取位置信息失败!');
										}
									});

								});
							}
						})
					})
				},
				goBack(item) {
					uni.postMessage({
						data: {
							toAddrName: item.name,
							toAddrDetail: item.address,
							toAreaCode: this.toAreaCode,
							toLat: item.location.lat,
							toLng: item.location.lng,
							page: this.urlObj.page,
							urlObjr:JSON.stringify(this.urlObj),
						}
					});

					if (this.urlObj.typer == 'WEIXIN') {
						uni.redirectTo({
							url: "/pages/map/map",
						})
					}

				},
				getQueryString() {
					let url = window.location.href
					let p = url.split('?')[1]
					let keyValue = p.split('&');
					let obj = {};
					for (let i = 0; i < keyValue.length; i++) {
						let item = keyValue[i].split('=');
						let key = item[0];
						let value = item[1];
						obj[key] = value;
					}
					return obj
				}
			}
		});
	</script>
</html>
