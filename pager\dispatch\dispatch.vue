<template>
	<view class="apply">
		<u-navbar title="我的调度" :autoBack="true" :placeholder="true" @leftClick="leftClick"></u-navbar>
		<view class="nav_box">
			<u-tabs :list="navList" :scrollable="false" lineWidth="22" lineHeight="2" lineColor="#346CF2"
				:activeStyle="{color: '#262626'}" :inactiveStyle="{color: '#999999'}" @change="changeTab"
				itemStyle="width:33%; height: 40px; padding:0">
			</u-tabs>
		</view>

		<tab-page ref="tabPage" :tabtype="tabtype" :useCarType='useCarType'></tab-page>
	</view>
</template>

<script>
	import tabPage from "@/pager/dispatch/tabpage/tabpage"
	// import tabPage from "../../pager/dispatch/tabpage/tabpage.vue"
	export default {
		components: {
			tabPage
		},
		data() {
			return {
				tabtype: 1,
				navList: [{
					name: '待派车',
					type: 1,
					// badge: {
					// 	isDot: true
					// }
				}, {
					name: '已派车',
					type: 2,
				}, {
					name: '驳回/取消',
					type: 3,
				}],
				useCarType: null
			};
		},
		onLoad(option) {
			this.useCarType = option.typer
		},
		onShow() {
			this.loadPage()
		},

		methods: {
			leftClick() {
				// #ifdef H5
				const pages = getCurrentPages()
				let page = pages[pages.length - 1].$page.fullPath; //完整路由地址
				let typer = page.split('type=')[1] //携带的type参数

				if (typer) {
					uni.reLaunch({
						url: '/pages/wayPassenger/index/index'
					})
				}
				// #endif
			},
			changeTab(item) {
				this.tabtype = item.type
			},
			loadPage() {
				this.$nextTick(() => {
					this.$refs.tabPage.searchBtn()
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.apply {
		.nav_box {
			background-color: #fff;
		}

		.tab_top {
			width: 360rpx;
			margin: 23rpx auto;

			.tab_top_btn {
				width: 133rpx;
				height: 53rpx;
				line-height: 53rpx;
				text-align: center;
				border-radius: 30rpx;
				font-size: 28rpx;
			}

			.tab_top_btn.active {
				background-color: #346CF2;
				color: #fff;
			}
		}
	}
</style>
