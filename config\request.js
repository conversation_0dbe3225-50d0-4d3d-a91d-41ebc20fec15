// 此vm参数为页面的实例，可以通过它引用vuex中的变量
import requestUri from './requestUri'; // 接口地址的列表
module.exports = (vm) => {
	// 初始化请求配置
	uni.$u.http.setConfig((config) => {
		/* config 为默认全局配置*/
		// #ifdef H5
		config.baseURL = '/dpc/'; /* 代理 */
		// #endif
		// #ifdef MP-WEIXIN
		// config.baseURL = 'https://zqcxdev.di-digo.com/api/zqcx/'; /* 根域名 */
		config.baseURL = 'https://zqcx.di-digo.com/api/zqcx/'; /* 线上根域名 */
		// config.baseURL =  "http://192.168.1.203:8008/zqcx/"
		// #endif
		return config
	})

	// 请求拦截
	uni.$u.http.interceptors.request.use((config) => { // 可使用async await 做异步操作
		// 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
		config.data = config.data || {}
		vm.$common.loading({
			title: "加载中..."
		})
		if (config.method == 'GET') {
			config.header['Content-Type'] = 'application/x-www-form-urlencoded'
		} else {
			config.header['Content-Type'] = 'application/json'
		}
		//加密后的字符串
		var requestForm = {
		  bizContent: null,
		}

		if(requestUri.isNeeDecUris.includes(config.url)){
		  let encryptedJson = ""
		  encryptedJson = JSON.parse(JSON.stringify(config.data))
		  const content = JSON.stringify(encryptedJson);
		  requestForm.bizContent = vm.$DateUtil.encryptPassword(content);
		  config.data = requestForm;
		}
		// 根据custom参数中配置的是否需要token，添加对应的请求头
		if (vm.$common.getItem('userInfo')) {
			// 可以在此通过vm引用vuex中的变量，具体值在vm.$store.state中
			config.header.accessToken = vm.$common.getItem('userInfo').accessToken
		}
		return config
	}, config => { // 可使用async await 做异步操作
		return Promise.reject(config)
	})

	// 响应拦截
	uni.$u.http.interceptors.response.use((response) => {
		/* 对响应成功做点什么 可使用async await 做异步操作*/
		const data = response.data
		setTimeout(() => {
			vm.$common.hideLoading()
		}, 300)
		console.log(data, '请求数据-------------------')
		// 自定义参数
		const custom = response.config?.custom
		if (data.code !== '1') {
			if (data.code == '503' || data.code == '502') {
				// 登录超时
				uni.$u.toast("登录超时,请重新登录")
				vm.$common.removeItem("userInfo")
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}, 1000)
			} else {
				// 如果没有显式定义custom的toast参数为false的话，默认对报错进行toast弹出提示
				if (custom.toast !== false) {
					if (data.msg) {
						uni.$u.toast(data.msg)
					}
				}

				// 如果需要catch返回，则进行reject
				if (custom?.catch) {
					return Promise.reject(data)
				} else {
					// 否则返回一个pending中的promise，请求不会进入catch中
					// return new Promise(() => {})
					return Promise.reject(data)
				}
			}
		}
		return data.data === undefined ? {} : data.data
	}, (response) => {
		setTimeout(() => {
			vm.$common.hideLoading()
		}, 400)
		// 对响应错误做点什么 （statusCode !== 200）
		return Promise.reject(response)
	})
}