<template>
	<view class="system">
		<u-navbar title="选择用车制度" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="filterBox">
			<u-search placeholder="搜索用车制度" :showAction="false" v-model="keyword" @search="queryList"></u-search>
		</view>
		<view class="cost_box">
			<view class="cost_box_tit">
				用车制度(乘车人)
			</view>
			<view class="cost_box_content">
				<u-radio-group size='14' v-model="radiosj" placement="column">
					<view class="radio_box u-flex u-row-between" v-for="(item,index) in dataList" :key="index">
						<u-radio :name="item" :label="item.regulationName"></u-radio>
						<view class="radio_text">
							<u--text type="primary" align="right" text="详情" @click="goDetail(item.regulationId)"></u--text>
						</view>
					</view>

				</u-radio-group>
			</view>
		</view>

		<view class="footer_box">
			<u-button type="primary" color="#346CF2" text="确认" @click="submit()"></u-button>
		</view>


	</view>
</template>

<script>
	import {
		regulationlistbyuser
	} from '@/config/api.js';
	export default {
		data() {
			return {
				pageParams:{},
				dataList:[],
				radiosj:{},
				keyword:''
			};
		},
		onLoad(option) {
			this.pageParams=option
			this.queryList()
		},
		methods: {
			goDetail(id) {
				uni.$u.route('/pages/wayPassenger/index/systemDetail/systemDetail',{
					id:id
				});
			},
			queryList() {
				regulationlistbyuser({
					params: {
						regulationScene: this.pageParams.regulationScene,
						userId: this.pageParams.userId,
						regulationName: this.keyword
					}
				}).then((data) => {
					this.dataList=data
				})
			},
			submit(){
				let pages = getCurrentPages();  //获取跳转的所有页面
				let nowPage = pages[ pages.length - 1]; //当前页
				let prevPage = pages[ pages.length - 2 ]; //上一页
				 
				prevPage.$vm.regulationId = this.radiosj.regulationId
				prevPage.$vm.regulationName = this.radiosj.regulationName
				
				uni.navigateBack({
				    delta: 1
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.system {
		.filterBox {
			padding: 27rpx 32rpx;
		}

		.cost_box {
			padding: 0 32rpx;
			background-color: #fff;
			overflow: hidden;

			.cost_box_tit {
				font-size: 32rpx;
				margin-top: 30rpx;
			}

			.cost_box_content {
				margin-top: 70rpx;

				.radio_box {
					margin-bottom: 40rpx;

					/deep/.u-radio__text {
						margin-left: 30rpx;
						font-size: 28rpx;
					}

					/deep/.radio_text {
						width: 100rpx;
					}
				}
			}
		}
	}
</style>
