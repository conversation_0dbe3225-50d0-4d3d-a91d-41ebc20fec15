<template>
	<view class="detail">
		<u-navbar title="详情" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="app_list">
			<view class="u-flex u-row-between detail_tit">
				<view class="u-flex">
					<u-image :width="16" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_qcl.png">
					</u-image>
					<span class="text-dh tab_icon">申请单号：{{detail.applyCode}}</span>
				</view>
			</view>

			<view class="app_t_line u-flex">
				<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png"></u-image>
				<span class="tab_icon">{{detail.psgName}}</span>
			</view>

			<view class="app_t_line u-flex">
				<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png">
				</u-image>
				<span class="tab_icon">{{detail.regulationName}}</span>
			</view>

			<view class="app_t_line u-flex">
				<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png"></u-image>
				<span class="tab_icon">{{detail.costCenterName}}</span>
			</view>

			<u-line color="#EEEEEE" margin="20rpx 0"></u-line>

			<view class="app_t_line u-flex">
				<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon.png">
				</u-image>
				<span class="tab_icon">{{new Date(detail.reserveStartTime).getTime()| date('mm月dd日 hh:MM')}} -
					{{new Date(detail.reserveEndTime).getTime() | date('mm月dd日 hh:MM')}}</span>
			</view>

			<view class="app_t_line u-flex">
				<view class="spot"></view>
				<span>{{detail.fromAddrName}}</span>
			</view>

			<view class="app_t_line u-flex" v-for="(item,index) in detail.throughAddrInfo" :key="index">
				<view class="spot" style="background-color:#5ac725"></view>
				<span>{{item.siteAddrName}}</span>
			</view>

			<view class="app_t_line u-flex">
				<view class="spot"></view>
				<span>{{detail.toAddrName}}</span>
			</view>

			<u-line color="#EEEEEE" margin="20rpx 0"></u-line>

			<view class="app_t_line u-flex" v-if="detail.processName">
				<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png">
				</u-image>
				<span class="tab_icon">审批人：{{detail.processName}}</span>
			</view>

			<view class="app_t_line u-flex" v-if="detail.description">
				<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_icon4.png"></u-image>
				<span class="tab_icon">用车备注：{{detail.description}}</span>
			</view>

			<view class="app_t_line u-flex" v-if="detail.psgNums">
				<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png">
				</u-image>
				<span class="tab_icon">乘车人数：{{detail.psgNums}}</span>
			</view>

			<!-- <view class="app_t_line u-flex">
				<u-image class="tab_icon" :width="14" :height="14"  src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png"></u-image>
				<span>是否匹配司机：{{detail.isAllotDriver==1?'是':'否'}}</span>
			</view> -->

			<view class="app_t_line u-flex">
				<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png">
				</u-image>
				<span class="tab_icon">所选车型：{{detail.wantCarTypeFullName}}</span>
			</view>
		</view>


		<view class="app_list" v-if="detail.orderEventHis&&detail.orderEventHis.length>0">
			<view class="res_jd_tit">
				订单跟踪：
			</view>
			<view class="res_jd">
				<u-steps :current="detail.orderEventHis.length-1" direction="column">
					<u-steps-item v-for="(item,index) in detail.orderEventHis" :key="index"
						:title="item.operatorName+' '+item.evtDetContent"
						:desc="new Date(item.operatorTime).getTime() | date('yyyy年mm月dd日 hh:MM:ss')">
					</u-steps-item>
				</u-steps>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		applycomporderinfo,
	} from '@/config/api.js';
	export default {
		data() {
			return {
				id: '',
				detail: {},
			};
		},
		onLoad(option) {
			this.id = option.id
			this.getDetail()
		},
		methods: {
			getDetail() {
				applycomporderinfo({
					params: {
						id: this.id,
					}
				}).then((data) => {
					if (data.throughAddrInfo) {
						data.throughAddrInfo = JSON.parse(data.throughAddrInfo)
					}
					this.detail = data
				})
			},

		}
	}
</script>

<style lang="scss" scoped>
	.detail {
		.detail_tit {
			margin-bottom: 34rpx;
		}

		.filterBox {
			padding: 0 32rpx;
			margin-top: 14rpx;

			.search {
				/deep/.u-search__content {
					background-color: #E9ECF7 !important;
				}

				/deep/.u-search__content__input {
					background-color: #E9ECF7 !important;
				}
			}
		}

		.app_list {
			background-color: #fff;
			margin: 14rpx 11rpx;
			border-radius: 11rpx;
			padding: 30rpx;
			font-size: 28rpx;
			position: relative;

			.icon-right {
				position: absolute;
				top: 50%;
				right: 27rpx;
				margin-top: -16rpx;
			}

			.tab_icon {
				margin-left: 16rpx;
			}

			.text-dh {
				font-size: 24rpx;
				color: #999999;
			}

			.text-rt {
				color: #346CF2;

				.text-rt-tag {
					margin-right: 4rpx;

					/deep/.u-tag--mini {
						height: 33rpx;
						line-height: 33rpx;
						padding: 0 4rpx;
					}
				}
			}

			.app_t_line {
				margin-top: 20rpx;
			}

			.dw_box {
				font-size: 24rpx;
			}

			.spot {
				width: 16rpx;
				height: 16rpx;
				background-color: #239EFC;
				border-radius: 50%;
				margin: 0 24rpx 0 8rpx;
			}
		}

		.btn_box {
			background-color: #fff;
			height: 80rpx;
			box-shadow: 0px -1px 43px 0px rgba(131, 131, 131, 0.15);

			.btnmini {
				width: 133rpx;
				height: 66rpx;
			}

			.hui {
				background-color: #999999;
				border-color: #999999;
			}
		}

		.line-dotted {
			border-top: 1px dotted #999999;
			margin: 20rpx 0;
		}

		.res_jd {
			padding: 20rpx 0rpx;

			.u-steps-item--column {
				padding-bottom: 20px;
			}
		}

		.popup_bg {
			.popupcbox {
				padding: 0 53rpx;
			}

			.select_input {
				height: 60rpx;
				border: 1px solid #CCCCCC;
				border-radius: 8rpx;
				padding-left: 20rpx;
				line-height: 60rpx;
				color: #999;
				margin: 20rpx 0;
			}

			.selectbox {
				padding: 0 53rpx;
				font-size: 28rpx;
			}

			.popup_btn_box {
				padding-bottom: 27rpx;
				margin-top: 30rpx;
			}

			.popup_tit {
				font-size: 36rpx;
				text-align: center;
				padding: 40rpx 0 30rpx 0;
			}

			.popup_txt {
				font-size: 28rpx;
				text-align: center;
				margin-bottom: 40rpx;
			}

			/deep/.u-popup__content {
				width: 84%;
			}

			.popup_btn {
				width: 230rpx;
				height: 80rpx;

				/deep/ .u-button__text {
					font-size: 36rpx !important;
				}
			}

			.two {
				color: #666666 !important;
			}
		}

		.popupbox {
			.popupbox_top {
				font-size: 28rpx;
				height: 102rpx;
				padding: 0 32rpx;
				border-bottom: 1px solid #E9ECF7;
				justify-content: space-between;

				.define {
					color: #346CF2;
				}
			}

			.radio_type {
				padding: 24rpx 0;

				.u-radio-group {
					justify-content: space-around;
				}

				.u-radio {
					padding: 18rpx 28rpx;
					border: 1px solid #999999;
					border-radius: 7rpx;
				}

				.u-radio.active {
					border: 1px solid #346CF2;
				}
			}

			.list_one {
				margin: 0 32rpx;
			}

			.list_two {
				margin: 0 32rpx;
			}

			.inputBox {
				padding: 23rpx 0;
				border-top: 1px solid #E9ECF7;

				.s_input_box {
					width: 200rpx;
					margin-left: 10rpx;
					margin-right: 50rpx;
				}

				.s_input {
					padding: 0 9px !important;
				}

				.s_btn_box {
					.del {
						background-color: #999999;
						border-color: #999999;
						// height: 100rpx;
					}

					.yes {
						// height: 100rpx;
					}
				}
			}

			.inputBoxTwo {
				padding: 13rpx 0;
				border-top: 1px solid #E9ECF7;

				.s_input_box {
					width: 200rpx;
					margin-left: 10rpx;
					margin-right: 50rpx;
				}

				.s_input {
					padding: 0 9px !important;
				}

				.s_btn_box {
					.del {
						background-color: #999999;
						border-color: #999999;
						// height: 100rpx;
					}

					.yes {
						// height: 100rpx;
					}
				}
			}
		}
	}
</style>
