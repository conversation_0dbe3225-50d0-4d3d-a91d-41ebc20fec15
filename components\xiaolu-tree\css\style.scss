.flex_between_center {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.checkbox {
		position: relative;
		margin-left: 10rpx;
		margin-right: 0px;
		.color {
			color: #00aaff;
			background-color: #00aaff;
		}
		.txt {
			font-size: 30rpx;
			width: 100%;
			height: 100%;
			display: flex;
		}
	}
	.checkBorder {
		border: 1px solid #ecdee4;
	}
	.header {
		// width: 100%;
		// position: fixed;
		// z-index: 9999;
		.title {
			height: 90rpx;
			padding: 0 32rpx;
			line-height: 90rpx;
			font-size: 30rpx;
			background-color: #fff;
			color: #606064;
		}
	}
	.iconclass {
		display: inline-block;
		margin: 0 12rpx;
		color: #D0D4DB;
		font-size: 28rpx;
	}
	.container-list {
		overflow-y: scroll;
		overflow-x: hidden;
		// padding-bottom: 210rpx;
		z-index: 999;
		.common {
			background-color: #fff;
			border-bottom: 1rpx solid #f4f4f4;
			padding-left: 24rpx;
			.content {
				display: flex;
				align-items: center;
				height: 60rpx;
				width: 100%;
				padding: 15rpx 0;
				position: relative;
				font-size: 32rpx;
				.lable-text{
					margin-left: 16rpx;
					font-size: 30rpx;
					color: #5b5757;
					width: 500rpx;
					word-break: break-all;
				}
				.right {
					position: absolute;
					right: 30rpx;
					color: #babdc3;
					font-size: 32rpx;
				}
			}
		}
	}
	.active {
		color: #4297ED !important;
	}
	.none {
		color: #666666;
	}
	.icon-selected{
		color: rgb(41, 121, 255)!important;
		font-size: 30rpx!important;
	}
	.icons{
		color: rgb(41, 121, 255)!important;
		font-size: 30rpx!important;
	}
	.inline-item {
		display: inline-block
	}
	
	.content-item{
		display: flex;
		position: relative;
		align-items: center;
	}
	
	.box_sizing {
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
	}
	
	.btn {
		position: fixed;
		bottom: 0;
		padding: 10px 16px;
		background-color: #fff;
		width: 100%;
		z-index: 999;
		.sureBtn {
			background-color: #0095F2;
			color: #fff;
		}
	}