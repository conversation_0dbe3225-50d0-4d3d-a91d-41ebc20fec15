<!DOCTYPE html>
<html lang="en" style="font-size: 0.26666666vw">
	<head>
		<meta charset="UTF-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="initial-scale=1.0, user-scalable=no">
		<!-- 框架 -->
		<script src="https://cdn.bootcss.com/vue/2.6.11/vue.js"></script>
		<!-- 样式 -->
		<link rel="stylesheet" type="text/css" href="css/networkCar.css">
		<link rel="stylesheet" type="text/css" href="css/allKinds.css">
		<link rel="stylesheet" type="text/css" href="css/frozen.css">
		<!-- 网络请求 -->
		<script src="https://cdn.bootcdn.net/ajax/libs/axios/1.2.0/axios.min.js"></script>
		<script type="text/javascript" src="js/axios.js"></script>
		<!-- 地圖 -->
		<script type="text/javascript">
			window._AMapSecurityConfig = {
				securityJsCode: 'a0ec29ceac10861c863c75bcf78565a4',
			}
		</script>
		<script src="https://webapi.amap.com/loader.js"></script>
	</head>
	<body>
		<div id="app">
			<div class="mapLeft " @click="goSys(true)">
				<img src="https://zqcx.di-digo.com/app/image/ntLeft.png" alt="" class="imgs">
			</div>
			<div class="mapRight" @click="goSys(false)">
				{{urlObj.sysName!='undefined'?urlObj.sysName:''}}
				<img src="https://zqcx.di-digo.com/app/image/neRh.png" alt="" class="imgs">
			</div>

			<div class="addrrs" v-if="urlObj.tolat == 'undefined'">
				<!-- addrrs addrrs-text -->
				<span class="addrrs-text">{{text}} </span>
				<!-- class="addrrs" -->
				<img src="https://zqcx.di-digo.com/app/image/addrs.png" alt="" style="width: 30rem;height: 30rem;"
					@click="gobck" />
			</div>


			<div id="container" style="height: 80vh;"></div>

			<!-- 加载中 -->
			<div :class="{show:loderShow,'ui-dialog':true}">
				<div class="loder">
					<div></div>
					<div></div>
					<div></div>
				</div>
			</div>
		</div>

	</body>

	<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
	<script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script>
	<script>
		document.addEventListener('UniAppJSBridgeReady', function() {});
	</script>

	<script>
		new Vue({
			el: '#app',
			data() {
				return {
					urlObj: {},
					loderShow: true,
					text: ''

				}
			},
			mounted() {
				this.loderShow = true
				this.urlObj = this.getQueryString()
				if (this.urlObj.foName || this.urlObj.toName || this.urlObj.sys) {
					this.text = decodeURIComponent(this.urlObj.foName)
					this.urlObj.foNames = decodeURIComponent(this.urlObj.foName)
					this.urlObj.toNames = decodeURIComponent(this.urlObj.toName)
					this.urlObj.sysName = decodeURIComponent(this.urlObj.sys)
				}
				// console.log(this.urlObj, '---------------');
				this.initr()
			},
			methods: {
				gobck() {
					uni.$emit('netWockCar', 111)
				},
				goSys(t) {
					if (t) {
						uni.switchTab({
							url: '/pages/wayPassenger/trip/trip'
						});
					} else {
						uni.navigateTo({
							url: '/pages/wayPassenger/index/systemDetail/systemDetail?id=' + this.urlObj
								.sysId
						})
					}

				},
				initr() {
					let that = this
					AMapLoader.load({
						"key": "1cb53fa0e69dc44036161409f1d4039c", // 申请好的Web端开发者Key，首次调用 load 时必填
						"version": "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
						"plugins": ['AMap.Driving', 'AMap.Geocoder'], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
						"AMapUI": { // 是否加载 AMapUI，缺省不加载
							"version": '1.1', // AMapUI 版本
							"plugins": ['overlay/SimpleMarker'], // 需要加载的 AMapUI ui插件
						},
						"Loca": { // 是否加载 Loca， 缺省不加载
							"version": '2.0' // Loca 版本
						},
					}).then((AMap) => {
						var map = new AMap.Map('container', {
							center: [that.urlObj.folong, that.urlObj.folat],
							resizeEnable: true,
						});
						let zoom = map.getZoom()
						// var marker = new AMap.Marker({
						// 	position: new AMap.LngLat(that.urlObj.folong, that.urlObj.folat),
						// 	title: '默认图标' //可以自定义icon图标展示
						// })
						// map.add(marker)

						var Geocoder = new AMap.Geocoder()

						if (that.urlObj.tolong && that.urlObj.tolat != "undefined") {
							var driving = new AMap.Driving({
								map: map,
							});
							driving.search(
								new AMap.LngLat(that.urlObj.folong, that.urlObj.folat),
								new AMap.LngLat(that.urlObj.tolong, that.urlObj.tolat),
								function(status, result) {
									if (status === 'complete') {
										// map.setZoom(map.getZoom())
										map.setZoom(map.getZoom() + 3)
										// map.remove(marker)
										// that.getLode(AMap, [that.urlObj.folong, that.urlObj.folat], map,
										// 	that.urlObj.foNames)
										// that.getLode(AMap, [that.urlObj.tolong, that.urlObj.tolat], map,
										// 	that.urlObj.toNames)
									} else {
										console.log('获取驾车数据失败：' + result)
									}
								});
						} else {
							map.setZoom(zoom + 5)
							// that.getLode(AMap, [that.urlObj.folong, that.urlObj.folat], map, that.urlObj
							// 	.foNames)
						}

						map.on("complete", function() {
							setTimeout(() => {
								that.loderShow = false
							}, 500)
						})

						if (that.urlObj.tolat == 'undefined') {
							map.on('dragend', ($event) => that.dragend($event, map, Geocoder))
							map.on('dragstart', ($event) => that.dragstart($event, map, Geocoder))
						}
					}).catch((e) => {
						that.loderShow = false
						console.error(e); //加载错误提示
					});
				},
				dragend(e, map, Geocoder) {
					let that = this
					let all = map.getCenter()

					console.log(that.urlObj, 'that.urlObj');
					Geocoder.getAddress([all.lng, all.lat], (status, res) => {
						if (status === "complete") {
							// address即经纬度转换后的地点名称
							uni.postMessage({
								data: {
									folat: all.lat,
									folong: all.lng,
									foNames: res.regeocode.formattedAddress,
									adcode: res.regeocode.addressComponent.adcode,
								}
							});
							that.urlObj.folong = all.lng
							that.urlObj.folat = all.lat
							that.urlObj.foName = res.regeocode.formattedAddress
							that.$set(that, 'text', res.regeocode.formattedAddress)
							console.log(that.text);
							// that.urlObj.foNames = res.regeocode.formattedAddress

							console.log(res, status);
						} else {
							alert('获取失败请重新定位')
						}
					})
				},
				dragstart(e, map, Geocoder) {
					// console.log('开始');
					// console.log(map.getCenter(), '-----1---');
				},

				getLode(AMap, arrs, map, text) {
					const textMarker = new AMap.Text({
						text: text,
						position: arrs,
						map: map,
						style: {
							'font-weight': 'bold',
							'font-size': '14px',
							"background": 'none',
							"border": 'none',
							"color": '#3c9cff',
							"text-shadow": "1px 1px 0 #fff,-1px -1px 0 #fff,1px -1px 0 #fff,-1px 1px 0 #fff "
						},
					});
					return
					var geocoder = new AMap.Geocoder({
						city: '350211',
						radius: 500 //范围，默认：500
					});

					geocoder.getAddress(arrs, function(status, result) {
						if (status === 'complete' && result.info === 'OK') {
							console.log(result, 'result');
							let text = result.regeocode.formattedAddress
							const textMarker = new AMap.Text({
								text: text,
								position: arrs,
								map: map,
								offset: new AMap.Pixel(-30, -55),
								style: {
									'font-size': '12px',
								},
							});
						} else {
							console.log(this.addres, 'result');
						}
					});
				},
				getQueryString() {
					let url = window.location.href
					let p = url.split('?')[1]
					let keyValue = p.split('&');
					let obj = {};
					for (let i = 0; i < keyValue.length; i++) {
						let item = keyValue[i].split('=');
						let key = item[0];
						let value = item[1];
						obj[key] = value;
					}
					return obj
				},
			},
		})
	</script>

	<style scoped>
		* {
			margin: 0;
		}

		body,
		html,
		#container {
			width: 100vw;
			height: 100vh
		}

		.ui-dialog {
			background: #ffffff70 !important;
		}

		.loder {
			position: absolute;
			top: 35%;
		}

		.loder div {

			background: #000;
		}
	</style>
</html>