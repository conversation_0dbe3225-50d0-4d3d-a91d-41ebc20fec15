<template>
	<view>
		<u-navbar title="上报记录" :autoBack="true" :placeholder="true">
		</u-navbar>
		<scroll-view class="scroll-content" scroll-y :refresher-triggered="triggered" refresher-default-style="none"
			refresher-enabled @refresherrefresh="onRefresh" @scrolltolower="lower">
			<template #refresher>
				<view class="u-center" style="width:100%">
					<block v-if="trigText=='松开加载'">
						<u-icon name="arrow-downward"></u-icon>
						<text style="margin-left: 20rpx;">{{trigText}}</text>
					</block>
					<u-loading-icon mode="semicircle" :text="trigText" v-else></u-loading-icon>
				</view>
			</template>
			<view class="itemBoxr u-align-items" hover-class="hover" v-for="(item,index) in listr" :key="index"
				@tap="goPage(item)">
				<u-cell-group :border="false">

					<u-cell title="添加时间 :" :value="`${$u.timeFormat(item.createTime , 'yyyy年mm月dd日')}`"
						:border="false"></u-cell>
					<u-cell title="车牌号 :" :value="`${item.carNumber} - ${item.brandModelName}`" :border="false">
					</u-cell>

					<block v-if="optionr.id==1">
						<u-cell title="加油时间 :" :value="`${$u.timeFormat(item.oilTime, 'yyyy年mm月dd日')} `"
							:border="false"></u-cell>
						<u-cell title="加油费用 :" :value="`${Number(item.oilCost).toFixed(2)} 元`" :border="false"></u-cell>
						<u-cell title="燃油标号 :" :value="`${item.oilLevel} 号`" :border="false"></u-cell>
					</block>

					<block v-if="optionr.id==2 ||optionr.id==3">
						<u-cell :title="`${trs==2?'送修':'送保'}日期 :`"
							:value="`${$u.timeFormat(item.maintainTime, 'yyyy年mm月dd日')} `" :border="false"></u-cell>
						<u-cell title="提车日期 :" :value="`${$u.timeFormat(item.takeCarTime, 'yyyy年mm月dd日')} `"
							:border="false"></u-cell>
						<u-cell :title="`${trs==2?'维修':'保养'}费用 :`"
							:value="`${Number(item.maintainPayCost).toFixed(2)} 元`" :border="false">
						</u-cell>
					</block>

					<block v-if="optionr.id==4">
						<u-cell title="投保日期 :" :value="`${$u.timeFormat(item.insuranceDate,'yyyy年mm月dd日')} `"
							:border="false"></u-cell>
						<u-cell title="投保单位 :" :value="item.insuranceCompany" :border="false"></u-cell>
						<u-cell title="本次生效日期 :" :value="`${$u.timeFormat(item.startDate, 'yyyy年mm月dd日')} `"
							:border="false"></u-cell>
						<u-cell title="本次到期日期 :" :value="`${$u.timeFormat(item.endDate, 'yyyy年mm月dd日')} `"
							:border="false"></u-cell>
						<u-cell title="保单费用 :" :value="`${Number(item.insuranceCost).toFixed(2)} 元`" :border="false">
						</u-cell>
					</block>

					<block v-if="optionr.id==5">
						<u-cell title="送检日期 :" :value="`${$u.timeFormat(item.inspectionDate, 'yyyy年mm月dd日')} `"
							:border="false"></u-cell>
						<u-cell title="送检地点 :" :value="item.inspectionAddress" :border="false"></u-cell>
						<u-cell title="下次年检日期 :" :value="`${$u.timeFormat(item.nextInspectionDate, 'yyyy年mm月dd日')} `"
							:border="false"></u-cell>
						<u-cell title="年检费用 :" :value="`${Number(item.inspectionCost).toFixed(2)} 元`" :border="false">
						</u-cell>
					</block>

					<block v-if="optionr.id==6">
						<u-cell title="违章日期 :" :value="`${$u.timeFormat(item.violationDate, 'yyyy年mm月dd日')} `"
							:border="false"></u-cell>
						<u-cell title="违章时间 :" :value="item.violationTime" :border="false"></u-cell>
						<u-cell title="违章地点 :" :value="item.violationAddress" :border="false"></u-cell>
					</block>

					<block v-if="optionr.id==7">
						<u-cell title="项目类型 :" :value="`${washData(item.otherTrafficType)}`" :border="false"></u-cell>
						<u-cell title="办理日期 :" :value="`${$u.timeFormat(item.otherTrafficTime, 'yyyy年mm月dd日')} `"
							:border="false"></u-cell>
						<u-cell title="项目费用 :" :value="`${Number(item.otherTrafficCost).toFixed(2)} 元`" :border="false">
						</u-cell>
					</block>
				</u-cell-group>
				<u-icon name="arrow-right" :size="18"></u-icon>
			</view>
			
			<view style="text-align: center;" v-show="listr.length==totalr&&listr.length!=0">———— 到底了 ————</view>
			
			<u-empty v-if="listr.length==0" text="记录为空" icon="http://cdn.uviewui.com/uview/empty/order.png">
			</u-empty>
			<u-loading-icon mode="semicircle" text="加载中" v-show="boot" style='padding:20rpx 0;'></u-loading-icon>
		</scroll-view>
	</view>
</template>

<script>
	import {
		appCartrafficlist
	} from '@/config/consoler.js'
	export default {
		data() {
			return {
				trs: null,
				triggered: false,
				trigText: '松开加载',
				boot: false,
				params: {
					pageNum: 1,
					pageSize: 10,
					carTrafficType: null
				},
				listr: [],
				totalr: 0,
				otherList: [],
				optionr:{}
			}
		},
		onLoad(option) {
			this.optionr=option
			this.trs=option.id
			this.initr()
			let arrs = this.$common.getItem('dicVoList')
			arrs.forEach(item => {
				if (item.dicCode == "other_traffic_type") {
					this.otherList = item.dicValueList
				}
			})
		},
		methods: {
			goPage(item) {
				let that=this
				uni.$u.route({
					url: '/pager/vehicleEscalaDetail/vehicleEscalaDetail',
					params: {
						idr: item.id,
						type: that.optionr.id,
					},
				})
			},
			// 下拉刷新
			onRefresh() {
				this.trigText = '正在加载'
				this.triggered = true
				this.$set(this, 'listr', [])
				this.params.pageNum = 1
				this.initr()
			},
			onPulling(e) {},
			// 上拉加载
			lower(e) {
				if (this.totalr == this.listr.length) return
				this.boot = true
				this.params.pageNum++
				this.initr()
				setTimeout(() => {
					this.boot = false
				}, 1500)
			},
			initr(type) {
				this.params.carTrafficType = this.optionr.id
				appCartrafficlist({
					params: this.params
				}).then(res => {
					this.listr.push(...res.pageList)
					this.totalr = res.total
					this.triggered = false
					this.trigText = '松开加载'
				})
			},
			// 洗数据
			washData(e) {
				let text = '-'
				this.otherList.forEach(res => {
					if (res.dicValue == e) text = res.dicDescribe
				})
				return text
			}

		}
	}
</script>

<style lang="scss" scoped>
	.scroll-content {
		width: 100%;
		height: calc(100vh - 100rpx);
		position: relative;
	}

	.u-center {
		position: absolute;
		bottom: 0;
		margin: 20rpx 0;
	}

	.itemBoxr {
		margin: 20rpx;
		background-color: #fff;
		border-radius: 10rpx;

		.item_line {}
	}

	.hover {
		background-color: #f3f3f3;
	}
</style>
