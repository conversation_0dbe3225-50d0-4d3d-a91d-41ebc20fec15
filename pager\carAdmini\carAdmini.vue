<template>
	<view>
		<u-navbar title="私车管理" :autoBack="true" :placeholder="true">
		</u-navbar>
		<view class="car_tip">服务车辆:公务 {{ datar.officialNumber?datar.officialNumber:0}} 辆，私车公用
			{{datar.privateNumber?datar.privateNumber:0}}
			辆，还可添加 <text style="color: #346CF2;"> {{datar.availableNumber?datar.availableNumber:0}} </text> 辆;
		</view>
		<u-tabs :list="tabsList" :itemStyle="itemStyle"
			:activeStyle="{color: '#262626', fontWeight: 'bold',transform: 'scale(1.05)'}"
			:inactiveStyle="{color: '#999999'}" class='car_tabs' @change="tabsChange"></u-tabs>

		<scroll-view class="scroll-content" scroll-y="true" refresher-enabled="true" :refresher-triggered="triggered"
			:refresher-threshold="100" refresher-background="#F9FAFE" @scrolltolower="onReflowe"
			@refresherrefresh="onRefresh">
			
			<view class="scroll_item" v-for="(item,i) in datar.privateCarListVos" :key='i'>
				<view @click="goBtn(item)">
					<view class="u-flex u-row-between scroll_top">
						<view class="u-flex">
							<u-image :width="20" :height="20" src="https://zqcx.di-digo.com/app/image/wdsq_qcl.png">
							</u-image>
							<view style="margin-left: 10rpx;">
								<view class="text-tb">{{`${item.carNumber}`}}<span v-show="item.numberColor">{{item.numberColor}}</span></view>
								<view class="text-dh">
									{{`${item.carBrandName}-${item.brandModelName}  `}} <span v-show="item.carColor">{{item.carColor}}色</span>
								</view>
							</view>
						</view>
						<view class="u-flex">
							<text class="tabs_text">{{dataWath(item.selfCarState,selfCarStateList)}}</text>
							<!-- <u-icon name="trash-fill" color="#999999" size="24" @click.stop="delBtn(item)"></u-icon> -->
						</view>
					</view>

					<view class="u-flex text-tile">车辆所有：<text>{{item.carOwner}}</text></view>
					<view class="u-flex text-tile" v-if="item.carTeamName">所属车队：<text>{{item.carTeamName}} </text>
					</view>
					<view class="u-flex text-tile" v-if="item.selfSubsidyFee">
						补贴标准：<text>{{Number(item.selfSubsidyFee).toFixed(2)}} 元/公里</text>
					</view>
					<view class="u-flex text-tile">
						请求加入时间：<text>{{$u.timeFormat(new Date(item.createTime).getTime() , 'yyyy-mm-dd hh:MM:ss')}}</text>
					</view>
				</view>
				<view class="u-flex u-row-between sorllr_btn">
					<u-button type="primary" text="同意加入" style="width: 25%;" v-if="item.selfCarState==10"
						@click="ApprovalBtn(1,item)" size="small"></u-button>
					<u-button type="primary" text="补贴设置" style="width: 25%;"
						v-if="item.selfCarState==20 && item.carCategory==3" @click="ApprovalBtn(2,item)" size="small"></u-button>
					<u-button color="#999999" text="驳回加入" style="width: 25%;" v-if="item.selfCarState==10"
						@click="ApprovalBtn(3,item)" size="small"></u-button>
					<u-button type="error" text="删除" style="width: 25%;" @click="delBtn(item)" size="small"></u-button>
				</view>
			</view>
			<u-empty text="列表为空" icon="http://cdn.uviewui.com/uview/empty/order.png"
				v-if="datar.privateCarListVos.length==0">
			</u-empty>
			<view style="text-align: center;" v-if="datar.privateCarListVos.length!=0">———— 到底了 ————</view>
		</scroll-view>
		

		<!-- 同意、駁回、補貼 -->
		<u-modal :show="ApprovalShow" :title="itemr.titler" :showCancelButton='true' @cancel="closetion"
			@close='closetion' @confirm='confirmTion'>
			<text v-if="itemr.typer==1">是否同意加入{{dataWath(itemr.carCategory,carCategoryList)}}请求?</text>
			<view class="modal_bt" slot="default" v-if="itemr.typer==2">
				<u-cell-group :border='false'>
					<u-cell title="补贴标准:">
						<u-input v-model="itemr.money" border="none" placeholder="请输入补贴标准" type="number" slot="value">
							<text slot="suffix">元/公里</text>
						</u-input>
					</u-cell>
					<u-cell title="所属车队:" :isLink="true" @click="pickerShow=true">
						<u--input v-model="itemr.carTeamIdName" disabled disabledColor="#ffffff" placeholder="请选择车辆类型"
							border="none" slot="value"></u--input>
					</u-cell>
				</u-cell-group>
			</view>
			<view class="" slot="default" v-if="itemr.typer==3">
				<text>是否驳回加入{{dataWath(itemr.carCategory,carCategoryList)}}请求?</text>
				<u--textarea v-model="itemr.remake" placeholder="请输入内容">
				</u--textarea>
			</view>
		</u-modal>

		<!-- 所属车队 -->
		<u-picker :show="pickerShow" :columns="pickerColumns" keyName="carTeamName" @close='pickerShow=false'
			@confirm='pickerConfirm' @cancel='pickerShow=false'></u-picker>

	</view>
</template>

<script>
	import {
		carGetprivatecarList,
		carDeletecar,
		carPutSelfcarcheck,
		carPutSetsubsidies,
		carGetcarteamlist
	} from '@/config/consoler.js';
	export default {
		data() {
			return {
				triggered: false,
				tabsList: [{
					name: '全部',
					id: 1
				}, {
					name: '待审批',
					id: 2
				}, {
					name: '已加入',
					id: 3
				}, {
					name: '已驳回',
					id: 4
				}, ],
				itemStyle: {
					width: "25%",
					height: "80rpx",
					padding: 0,
				},
				params: {
					carType: 1,
					carNumber: null
				},
				datar: {
					privateCarListVos: []
				},
				carCategoryList: [],
				selfCarStateList: [],
				ApprovalShow: false,
				itemr: {},
				pickerShow: false,
				pickerColumns: []
			}
		},
		mounted() {
			this.getList()
		},
		methods: {
			goBtn(item) {
				uni.$u.route('/pages/wayPassenger/my/myCar/addMyCar/addMyCar', {
					type: 'details',
					id: item.carId
				});
			},
			tabsChange(e) {
				this.params.carType = e.id
				this.getList()
			},
			pickerConfirm(e) {
				this.itemr.carTeamIdName = e.value[0].carTeamName
				this.itemr.carTeamId = e.value[0].carTeamId
				this.pickerShow = false
			},
			ApprovalBtn(t, item) {
				item.typer = t
				item.titler = t == 1 ? '同意加入' : t == 2 ? '补贴标准设置' : t == 3 ? '驳回加入' : ''
				this.itemr = item
				this.ApprovalShow = true
			},
			confirmTion() {
				let {
					typer,
					remake,
					carId,
					carTeamId,
					money
				} = this.itemr
				if (typer == 1 || typer == 3) {
					carPutSelfcarcheck({
						state: typer == 1 ? 1 : 0,
						reson: typer == 3 ? remake : null,
						id: carId
					}).then(v => {
						uni.$u.toast(v)
						this.getList()
						this.ApprovalShow = false
					})
				}
				if (typer == 2) {
					carPutSetsubsidies({
						carTeamId: carTeamId,
						selfSubsidyFee: money,
						id: carId
					}).then(v => {
						uni.$u.toast('补贴设置成功')
						this.getList()
						setTimeout(() => {
							this.ApprovalShow = false
						}, 500)
					})
				}


			},
			closetion() {
				this.ApprovalShow = false
				this.itemr = {}
			},
			delBtn(item) {
				let that = this
				uni.showModal({
					title: '提示',
					content: `确定删除已绑车辆【${item.carNumber}】？`,
					success: function(res) {
						if (res.confirm) {
							carDeletecar({
								id: item.carId
							}).then(v => {
								uni.$u.toast(v)
								that.getList()
							})
						} else if (res.cancel) {}
					}
				});

			},
			onRefresh(e) {
				this.triggered = true
				this.getList()
			},
			onReflowe(e) {
				console.log(e, '111111111')
			},
			getList() {
				let arrs = this.$common.getItem('dicVoList')
				arrs.forEach(item => {
					if (item.dicCode == "self_car_state") {
						this.selfCarStateList = item.dicValueList
					}
					if (item.dicCode == "car_category") {
						this.carCategoryList = item.dicValueList
					}
				})
				carGetprivatecarList({
					params: this.params
				}).then(res => {
					res.privateCarListVos.forEach(v => {
						v.colors = v.selfCarState == 10 ? '#2AC326' : v.selfCarState == 20 ? '#FF7E00' : v
							.selfCarState == 30 ? '#999999' : ''
					})
					this.datar = res
					this.triggered = false
				})


				carGetcarteamlist({
					params: {
						pageNum: 1,
						pageSize: 500
					}
				}).then(v => {
					this.pickerColumns = [v.pageList]
				})
			},
			dataWath(v, arr) {
				arr.forEach(r => {
					if (r.dicValue == v) v = r.dicDescribe
				})
				return v
			}
		}
	}
</script>

<style lang="scss" scoped>
	.car_tip {
		background-color: #bdbec1;
		color: #fff;
		padding: 10rpx;
		text-align: center;
		font-size: 25rpx;
	}

	.car_tabs {
		background-color: #fff;
	}

	.scroll-content {
		height: calc(100vh - 220rpx);

		.scroll_item {
			background-color: #fff;
			margin: 16rpx;
			padding: 16rpx 30rpx;
			border-radius: 10rpx;

			.scroll_top {
				width: 100%;
				margin-bottom: 20rpx;

				.tabs_text {
					margin-right: 30rpx;
					color: #fff;
					border-radius: 10rpx;
					padding: 4rpx 20rpx;
				}

				.tab_icon {
					margin-right: 20rpx;
				}

				.text-tb {
					font-size: 28rpx;
					color: #262626;
				}

				.text-dh {
					font-size: 24rpx;
					color: #999999;
				}
			}

			.sorllr_btn {
				margin: 20rpx 0;

				/deep/ .u-button {
					width: 25%;
				}
			}

			.text-tile {
				font-size: 28rpx;
				color: #999999;
				margin-top: 8rpx;
			}

		}
	}

	.modal_bt {
		width: 100%;

		/deep/ .u-input--square {
			flex: 2 !important;
		}

		/deep/ .u-cell--clickable {
			background: #fff;
		}
	}
</style>
