{"name": "zqcx_app_new", "version": "1.0.0", "description": "", "main": "main.js", "dependencies": {"dom7": "^4.0.6", "deepmerge": "^4.3.1", "csstype": "^3.1.2", "estree-walker": "^2.0.2", "magic-string": "^0.30.4", "crypto-js": "^4.2.0", "shvl": "^2.0.3", "postcss": "^8.4.31", "source-map-js": "^1.0.2", "nanoid": "^3.3.6", "ssr-window": "^4.0.2", "picocolors": "^1.0.0", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0", "uview-ui": "^2.0.31", "vue": "^3.3.4", "swiper": "^8.4.7"}, "devDependencies": {}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://code.jjcxfw.com/xieyangbin/zqcx_app_new.git"}, "keywords": [], "author": "", "license": "ISC"}