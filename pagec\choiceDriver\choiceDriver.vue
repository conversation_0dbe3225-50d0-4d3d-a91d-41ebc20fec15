<template>
	<view class="choice">
		<u-navbar title="选择司机" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="filterBox">
			<u-search class="search" placeholder="请输入姓名" :showAction="false" v-model="keyword" @search="search">
			</u-search>
		</view>
		<view class="cell_list">
			<view class="u-flex car u-row-between" v-for="(item,index) in dataList" :key="item.driverName"
				@click="choice(item)">
				<view class="u-flex u-flex-10">
					<u-icon class="iconStyle" size="40" name="https://zqcx.di-digo.com/app/image/gwgp_img1.png">
					</u-icon>
					<view class="car_name_sbox">
						<view class="car_t">
							{{item.driverName}}
						</view>
						<view class="car_t" style="color: #646464;font-size: 24rpx;" v-if='item.carTeamName'>
							{{item.carTeamName}}
						</view>
						<view class="car_txt u-flex" @click.native.stop.prevent="choice(item)">
							<u-rate value="5" :size="16" :gutter="0" activeColor="#FFBA00" :allowHalf="true" readonly>
							</u-rate>
							<!-- <span style="margin-left: 8rpx;">{{item.starLevel || '5'}}星</span> -->
							<span style="margin-left: 8rpx;">5 星</span>
						</view>
					</view>
				</view>
				<view class="car_type u-flex-1">
					{{item.licenseType ?item.licenseType:''}}
				</view>
				<view class="u-flex-4 orderNum">
					{{'该时段有'+item.orderCount+'个订单'}}
				</view>
			</view>

		</view>
		<!-- 更多 -->
		<u-loadmore v-if="dataList.length>0" :status="status" />

		<u-empty v-if="dataList.length==0" mode="order" text="人员为空" icon="http://cdn.uviewui.com/uview/empty/order.png">
		</u-empty>
	</view>
</template>

<script>
	import {
		driverdisplist
	} from '@/config/api.js';
	export default {
		data() {
			return {
				pageParams: {},
				keyword: '',
				dataList: [],
				status: 'loadmore',
				hasNext: true,
				pageNum: 1,
				pageSize: 20,
			};
		},
		onLoad(option) {
			this.pageParams = option
			this.queryList()
		},
		onReachBottom() {
			if (this.hasNext) {
				this.status = 'loading';
				this.pageNum = ++this.pageNum;
				setTimeout(() => {
					this.queryList()
				}, 1000)
			}
		},
		methods: {
			search() {
				this.pageNum = 1
				this.dataList = []
				this.queryList()
			},
			queryList() {
				driverdisplist({
					params: {
						travelId: this.pageParams.travelId,
						pageNum: this.pageNum,
						pageSize: this.pageSize,
						content: this.keyword
					}
				}).then((data) => {
					if (this.pageNum == 1) {
						this.dataList = data.pageList
					} else {
						this.dataList.push(...data.pageList)
					}
					// this.dataList = data.pageList
					this.hasNext = data.hasNext
					if (data.hasNext) {
						this.status = 'loadmore'
					} else {
						this.status = 'nomore'
					}
				})
			},
			choice(item) {
				let pages = getCurrentPages(); //获取跳转的所有页面
				let nowPage = pages[pages.length - 1]; //当前页
				let prevPage = pages[pages.length - 2]; //上一页
				let arrObj = {
					driverUserId: item.userId,
					driverUserName: item.driverName
				}
				console.log(this.pageParams.type, '--------');
				if (this.pageParams.type == '1') {
					// 企业派车
					prevPage.$vm.setChildArrOne(arrObj, this.pageParams.index)
				} else if (this.pageParams.type == '2') {
					// 企业拆单派车
					prevPage.$vm.setChildArr(arrObj, this.pageParams.index)
				} else if (this.pageParams.type == '3') {
					// 租赁派车
					prevPage.$vm.setChildOneLeaseArr(arrObj, this.pageParams.index)
				} else if (this.pageParams.type == '4') {
					// 租赁拆单派车
					prevPage.$vm.setChildTwoLeaseArr(arrObj, this.pageParams.index)
				} else if (this.pageParams.type == '5') {
					// 新版租赁派车拆车
					prevPage.$vm.setChild(item, this.pageParams.index, false)
				}

				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>

<style lang="scss">
	.choice {
		.cell_list {
			background-color: #fff;
		}

		.car {
			padding: 15rpx 0;
			margin: 0 20rpx;
			border-bottom: 1px solid #E9ECF7;

			.car_name_sbox {
				margin-left: 20rpx;
				z-index: 10;

				.car_t {
					font-size: 28rpx;
					margin-bottom: 6rpx;
				}

				.car_txt {
					font-size: 20rpx;
					color: #999999;
				}

				.car_type {
					font-size: 28rpx;
				}
			}

		}

		.filterBox {
			padding: 14rpx 20rpx;
			border-bottom: 1px solid #E9ECF7;
			border-top: 1px solid #E9ECF7;
			background-color: #fff;

			.search {
				/deep/.u-search__content {
					background-color: #E9ECF7 !important;
				}

				/deep/.u-search__content__input {
					background-color: #E9ECF7 !important;
				}
			}
		}

		.orderNum {
			font-size: 26rpx;
		}
	}
</style>
