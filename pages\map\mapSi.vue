<template>
	<view>
		<web-view :src="src" v-if="openShow"></web-view>
	</view>
</template>

<script>
	import qs from 'qs'
	export default {
		data() {
			return {
				src: '',
				optionr: {},
				openShow: false
			};
		},
		onShow() {
			uni.showLoading({
				title: '加载中',
				mask: true
			})
			this.openShow = false
			console.log(this.openShow, '开始的');
			this.openUrlr(this.optionr)

		},
		onLoad(option) {
			this.optionr = option
			// this.openUrlr(option)
		},
		methods: {
			openUrlr(option) {

				setTimeout(() => {
					this.$set(this, 'openShow', true)
					// let urlr = 'https://zqcxdev.di-digo.com/app-h5/'
					let urlr = 'https://zqcx.di-digo.com/app-h5/'
					// let urlr = this.$urlrs
					// this.src = option.url + '?' + qs.stringify(option)
					// #ifdef H5
					option.type = "H5"
					option.accessToken = JSON.parse(uni.getStorageSync("userInfo")).accessToken
					this.src = option.url + '?' + qs.stringify(option)
					// #endif
					// #ifdef MP-WEIXIN
					option.type = "WEIXIN"
					option.accessToken = JSON.parse(uni.getStorageSync("userInfo")).accessToken
					this.src = `${urlr}${option.url}?${qs.stringify(option)}`
					// #endif
					uni.hideLoading()
				}, 600)
			}
		},
	}
</script>

<style lang="scss">

</style>