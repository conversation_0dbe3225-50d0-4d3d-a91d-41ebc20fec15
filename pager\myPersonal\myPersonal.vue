<template>
	<view class="personal">
		<u-navbar title="个人信息" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="cell_list">
			<u-cell-group class="cellgroup" :border='false'>
				<u-cell title="姓名" :value="pageParams.name"></u-cell>
				<u-cell title="手机">
					<u--text slot="value" align="right" mode="phone" :text="pageParams.mobile" ></u--text>
				</u-cell>
				<u-cell title="职务" :value="pageParams.jobs">
					<u--text slot="value" align="right"  :text="pageParams.jobs" ></u--text>
				</u-cell>
				<u-cell title="所属企业" :value="pageParams.compName"></u-cell>
				<u-cell title="常驻城市" :value="pageParams.empAreaCode" :border='false'></u-cell>
			</u-cell-group>
		</view>
		<view class="cell_list">
			<u-cell-group class="cellgroup" :border='false'>
				<u-cell title="系统角色" :value="pageParams.sysRole" :border='false'></u-cell>
			</u-cell-group>
		</view>
		<view class="cell_list">
			<u-cell-group class="cellgroup" :border='false'>
				<u-cell title="驾驶证类型" :value="pageParams.licenseType"></u-cell>
				<u-cell title="驾驶证有效期" :value="pageParams.licenseEndDate" :border='false'></u-cell>
			</u-cell-group>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pageParams:{}
			};
		},
		methods:{},
		onLoad(option) {
			this.pageParams=option
			console.log(this.pageParams,'this.pageParams')
		},
	}
</script>

<style lang="scss">
.personal{
	.cell_list{
		background-color: #fff;
		margin-top: 27rpx;
		/deep/ .u-line{
			border-color: #E9ECF7!important;
			margin: 0 32rpx!important;
			width: calc(100% - 64rpx)!important;
		}
		.iconStyle{
			margin-right: 14rpx;
		}
	}
}
</style>
