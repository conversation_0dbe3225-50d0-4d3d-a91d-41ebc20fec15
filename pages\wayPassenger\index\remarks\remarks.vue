<template>
	<view class="remarks">
		
		<u-navbar title="用车备注" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="remarks_content">
			<view class="remarks_tit">
				填写用车备注
			</view>
			<u--textarea
				placeholder="请填写用车备注"
				v-model="remark"
				confirmType="done"
				height="320"
			></u--textarea>
		</view>
		<view class="footer_box">
			<u-button type="primary" color="#346CF2" text="确认" @click="submit()"></u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pageParams:{},
				remark:''
			};
		},
		methods:{
			submit(){
				let pages = getCurrentPages();  //获取跳转的所有页面
				let nowPage = pages[ pages.length - 1]; //当前页
				let prevPage = pages[ pages.length - 2 ]; //上一页
				 
				prevPage.$vm.remark = this.remark
				
				uni.navigateBack({
				    delta: 1
				});
			}
		},
		onLoad(option) {
			this.pageParams=option
			if(this.pageParams.remark){
				this.remark=this.pageParams.remark
			}
		},
	}
</script>

<style lang="scss" scoped>
.remarks{
	.remarks_tit{
		margin-bottom: 20rpx;
	}
	.remarks_content{
		padding: 20rpx 32rpx 32rpx 32rpx;
		margin-top: 26rpx;
		background-color: #fff;
	}
}
</style>
