<template>
	<view class="Ride">
		<u-navbar title="选择乘车人" :autoBack="true" :placeholder="true"></u-navbar>
		<xiaolu-tree :checkList="checkList" keyValue="id" v-if="tree.length>0" :props="props" @sendValue="confirm"
			:isCheck="true" :treeNone="tree"></xiaolu-tree>
	</view>
</template>

<script>
	import XiaoluTree from '@/components/xiaolu-tree/tree.vue';
	import {
		treeListorg
	} from '@/config/api.js';
	export default {
		components: {
			XiaoluTree
		},

		data() {
			return {
				pageParams: {},
				popupshow: false,
				popupmodel: {
					name: '',
					mobile: '',
				},
				rules: {
					name: {
						type: 'string',
						required: true,
						message: '请填写姓名',
						trigger: ['blur', 'change']
					},
					mobile: [{
							required: true,
							message: '请输入手机号',
							trigger: ['change', 'blur'],
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// uni.$u.test.mobile()就是返回true或者false的
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change', 'blur'],
						}
					]

				},
				checkList: [
					// {
					// id: 664214366998,
					// name: "校长",
					// user: false,
					// path: [], //该值的路径
					// }, 
				],
				props: { //单选模式(任意一项)
					label: 'name',
					children: 'children',
					multiple: false, //值为true时为多选，为false时是单选
					// checkStrictly:true,//需要在多选模式下才传该值，checkStrictly为false时，可让父子节点取消关联，选择任意一级选项。为true时关联子级，可以全选
					nodes: true //在单选模式下，nodes为false时，可以选择任意一级选项，nodes为true时，只能选择叶子节点
				},
				tree: [
					//id，user字段必须，user'字段名不可更改，id的字段明可以通过传keyValue指定，user为true时说明没有子级，user为false时说明有子级（即children的长度不为0）
				]
			};
		},
		onShow() {
			this.inittree()
		},
		onLoad(option) {
			this.pageParams = option
		},
		methods: {
			inittree() {
				treeListorg({}).then((data) => {
					this.recursionList(data)
					let checkListData = JSON.parse(this.pageParams.checkListData)
					if (checkListData && checkListData.length > 0) {
						this.checkList = checkListData
					}
					this.tree = data
				})
			},
			recursionList(data) {
				data.map((item) => {
					if (item.children && item.children.length > 0) {
						item.user = false
						this.recursionList(item.children)
					} else {
						item.user = true
					}
				})
			},
			confirm(data) {
				let pages = getCurrentPages(); //获取跳转的所有页面
				let nowPage = pages[pages.length - 1]; //当前页
				let prevPage = pages[pages.length - 2]; //上一页

				prevPage.$vm.psgUser = data[0].name
				prevPage.$vm.psgUserId = data[0].id
				prevPage.$vm.checkListData = data
				prevPage.$vm.regulationName = ''
				prevPage.$vm.regulationId = ''
				prevPage.$vm.costCenterOrgName = ''
				prevPage.$vm.costCenterOrgId = ''

				try {
					prevPage.$vm.setUser(data)
				} catch (e) {
					console.log(e);
				}


				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>

<style lang="scss">

</style>