<template>
	<view class="new_dis">
		<u-navbar title=" " :autoBack="true" :placeholder="true">
			<view class="" slot="left">取消</view>
			<!-- #ifdef H5 -->
			<view class="" slot="right" style="color: blue;" @click="preseClick">确定</view>
			<!-- #endif -->
		</u-navbar>
		<view class="nav_box">
			<u-tabs :list="navList" :scrollable="false" lineWidth="33" lineHeight="2" lineColor="#346CF2"
				:activeStyle="{color: '#262626'}" :inactiveStyle="{color: '#999999'}" @change="changeTab"
				itemStyle="width:50%; height: 60rpx; padding:5rpx">
			</u-tabs>
		</view>
		<view v-if="navType==1">
			<u-notice-bar :text="navText"></u-notice-bar>
		</view>
		<!--派單  -->
		<view class="enterp u-flex" v-for="(item,index) in carListr" :key="index">
			<view class="ent_left">
				<view class="u-center ent_box" @click="openClick(index,1)">
					<view class="ent_icon">
						<u-icon name='clock-fill' color="#cccccc" size="19"></u-icon>
					</view>
					<u--input placeholder="请选择用车时间" v-model="item.reserveStartTime" readonly></u--input>
				</view>
				<view class="u-center ent_box" @click="openClick(index,2)">
					<view class="ent_icon">
						<u-icon name='car-fill' color="#cccccc" size="20"></u-icon>
					</view>
					<u--input placeholder="请选择车型" v-model="item.carTypeIdName" readonly></u--input>
				</view>
				<view class="u-center ent_box" @click="openClick(index,3,item)" v-if="datar.travelType==2">
					<view class="ent_icon">
						<u-icon name='calendar-fill' color="#cccccc" size="20"></u-icon>
					</view>
					<u--input placeholder="请选择包车套餐" v-model="item.valuationFullName" readonly></u--input>
				</view>
				<view class="u-center ent_box" @click="openClick(index,4,item)">
					<view class="ent_icon">
						<u-icon name='car' color="#cccccc" size="20"></u-icon>
					</view>
					<u--input placeholder="请选择车辆" v-model="item.carName" readonly></u--input>
				</view>
				<view class="u-center ent_box" @click="openClick(index,5,item)">
					<view class="ent_icon">
						<u-icon name='account-fill' color="#cccccc" size="20"></u-icon>
					</view>
					<u--input placeholder="请选择选择司机" v-model="item.driverUserName" readonly></u--input>
				</view>
			</view>
			<view class="ent_right" v-if="navType==1">
				<u-button type="primary" text="添加" size='mini' @click='addClick' v-if="index==0"></u-button>
				<u-button text="删除" size='mini' color='#a5a5a5' @click='delClick(index)' v-else></u-button>
			</view>
		</view>

		<view class="text-info" style=""><u-icon name="info-circle" color="#000000"
				size="18"></u-icon> 订单租车方式如果为不带驾租车,司机随意选一人即可。</view>

		<!-- #ifdef MP-WEIXIN -->
		<view class="footer_box">
			<u-button type="primary" color="#346CF2" text="确定" @click="preseClick"></u-button>
		</view>
		<!-- #endif -->


		<!-- 用车时间 -->
		<u-datetime-picker :show="timeShowr" v-model="timeMode" mode="datetime" :formatter="formatter"
			@cancel="timeShowr=false,tiemIdx=null" :closeOnClickOverlay='true' @close="timeShowr=false,tiemIdx=null"
			@confirm='tiemConfirms'>
		</u-datetime-picker>

		<!-- 车型弹窗 -->
		<u-picker :show="carShowr" ref="uPicker" keyName="carTypeName" :columns="carTypecolumns"
			@cancel="carShowr=false" @confirm="carConfirmr" @change="carTypechange"></u-picker>

		<!-- 包车套餐弹窗-->
		<u-picker :show="packageShow" keyName="valuationFullName" :columns="packaColumnsr" @cancel="packageShow=false"
			@confirm="packaConfirmr"></u-picker>
	</view>
</template>

<script>
	import {
		dispcomporderinfo,
		cartypetreelist,
		tempvaluationlist,
		dispatchleasecar,
		dispatchleasechangecar,
		cartypetreebyfeetemplate
	} from '@/config/api.js';
	export default {
		data() {
			return {
				datar: {},
				navText: '请先与乘车人或单位调度员联系确定拆单派车!',
				navType: 0,
				navList: [{
					name: '派车',
					type: 0,
				}, {
					name: '拆单',
					type: 1,
				}],
				carListr: [{
					carId: null,
					carTypeId: null,
					driverUserId: null,
					reserveStartTime: null,
					valuationId: null,
					carTypeIdName: null,
					valuationFullName: null,
					carName: null,
					driverUserName: null,
				}],
				timeShowr: false,
				timeMode: Number(new Date()),
				tiemIdx: null,
				carShowr: false,
				cartypetree: [],
				carTypecolumns: [],
				packageShow: false,
				packaColumnsr: []

			};
		},
		mounted() {},
		onLoad(option) {
			console.log(option.item, 'option');
			let item = JSON.parse(option.item)
			console.log(item, 'item');
			item.wantCarTypeFullName = item.wantCarTypeFullName.replace('-', '/')
			this.datar = item
			this.getDetail()
		},
		methods: {
			// 保存
			preseClick() {
				// 1 派车 2租赁派车 3改派
				let {
					typer,
					travelId,
					newTyper
				} = this.datar


				if (typer == 1 || typer == 2) {
					// 派单
					dispatchleasecar({
						dispatchCarVo: {
							dispatchCarChildList: this.carListr,
							dispatchMode: this.navType
						},
						id: travelId,
					}).then((data) => {
						uni.$u.toast('操作成功')
						this.backBtn(newTyper)
					})
				} else {
					// 改派
					dispatchleasechangecar({
						dispatchCarVo: {
							dispatchCarChildList: this.carListr,
							dispatchMode: this.navType
						},
						id: travelId,
					}).then((data) => {
						uni.$u.toast('操作成功')
						this.backBtn(newTyper)
					})
				}



			},
			backBtn(type) {
				let that = this
				setTimeout(() => {
					let pages = getCurrentPages(); //获取跳转的所有页面
					let nowPage = pages[pages.length - 1]; //当前页
					let prevPage = pages[pages.length - 2]; //上一页
					if (type == 'add') {
						that.$nextTick(() => {
							prevPage.$vm.loadPage()
						})
					} else {
						that.$nextTick(() => {
							prevPage.$vm.getDetail()
						})
					}
					uni.navigateBack({
						delta: 1
					});
				}, 1000)
			},
			// 打开
			openClick(idx, t, item) {
				this.tiemIdx = idx
				if (t == 1) {
					this.timeShowr = true
				}
				if (t == 2) {
					this.carShowr = true
				}
				if (t == 3) {
					this.getTempval(item)
				}
				if (t == 4) {
					// if (!item.carTypeId) return uni.$u.toast('请选择车型')
					uni.$u.route('/pagec/choiceCar/choiceCar', {
						travelId: this.datar.travelId,
						dispatchMode: this.navType,
						type: 5,
						carTypeId: item.carTypeId ? item.carTypeId : null,
						index: idx,
					});
				}
				if (t == 5) {
					uni.$u.route('/pagec/choiceDriver/choiceDriver', {
						travelId: this.datar.travelId,
						type: 5,
						index: idx
					});
				}
			},
			// 选择包车套餐
			packaConfirmr(e) {
				let id = e.value[0].valuationId,
					name = e.value[0].valuationFullName
				this.carListr[this.tiemIdx].valuationId = id //租赁拆单包车套餐
				this.carListr[this.tiemIdx].valuationFullName = name //租赁拆单包车套餐
				this.packageShow = false
			},

			// 选择车型
			carConfirmr(e) {
				let name = e.value[0].carTypeName + '/' + e.value[1].carTypeName,
					id = e.value[1].carTypeId
				this.carListr[this.tiemIdx].carTypeId = id //租赁拆单车型
				this.carListr[this.tiemIdx].carTypeIdName = name //租赁拆单车型

				this.carListr[this.tiemIdx].carId = null //租赁拆单车型
				this.carListr[this.tiemIdx].carName = null //租赁拆单车型

				this.carListr[this.tiemIdx].valuationId = null //租赁拆单包车套餐
				this.carListr[this.tiemIdx].valuationFullName = null //租赁拆单包车套餐

				this.carShowr = false
			},
			// 选择时间
			tiemConfirms(e) {
				let valr = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
				this.carListr[this.tiemIdx].reserveStartTime = valr
				this.timeShowr = false
			},
			// 删除
			delClick(idx) {
				let that = this
				uni.showModal({
					title: '提示',
					content: '是否删除？',
					success: function(res) {
						if (res.confirm) {
							that.carListr.splice(idx, 1)
						}
					}
				})
			},
			// 添加
			addClick() {
				this.carListr.push(this.objData())
			},
			// tabs切换
			changeTab(e) {
				this.navType = e.type
				this.carListr = [this.objData()]
			},
			// 返回
			setChild(objr, idx, t) {
				console.log(objr, 'objr');
				if (t) {
					this.$set(this.carListr[idx], 'carId', objr.carId)
					this.$set(this.carListr[idx], 'carName',
						` ${objr.carNumber} `)
				} else {
					this.$set(this.carListr[idx], 'driverUserId', objr.userId)
					this.$set(this.carListr[idx], 'driverUserName',
						` ${objr.driverName} `)
				}

			},
			// 获取详情
			getDetail() {

				this.carListr[0].reserveStartTime = this.datar.reserveStartTime
				this.carListr[0].carTypeId = this.datar.compOrderState == 20 ? this.datar.wantCarTypeId : this.datar
					.compOrderState == 25 ?
					this.datar.wantCarTypeId : this.datar.carTypeId //租赁拆单车型

				// this.carListr[0].carTypeIdName = this.datar.carTypeFullName?this.datar.carTypeFullName:this.datar.wantCarTypeFullName //租赁拆单车型
				this.carListr[0].carTypeIdName = this.datar.compOrderState == 20 ? this.datar.wantCarTypeFullName : this
					.datar
					.compOrderState == 25 ?
					this.datar.wantCarTypeFullName : this.datar.carTypeFullName //租赁拆单车型
				cartypetreebyfeetemplate({
					params: {
						feeTemplateId: this.datar.feeTemplateId ? this.datar.feeTemplateId : ''
					}
				}).then((data) => {
					this.cartypetree = data
					this.carTypecolumns = [data, data[0].children]
				})
				return
				dispcomporderinfo({
					params: {
						id: item.travelId,
					}
				}).then((data) => {
					data.typer = item.typer
					data.newTyper = item.newTyper

					data.tiemr = uni.$u.timeFormat(data.reserveStartTime, 'yyyy-mm-dd hh:MM:ss')
					data.wantCarTypeFullName = data.wantCarTypeFullName.replace('-', '/')
					this.datar = data
					this.carListr[0].reserveStartTime = this.datar.tiemr
					this.carListr[0].carTypeId = this.datar.carTypeId //租赁拆单车型
					this.carListr[0].carTypeIdName = this.datar.wantCarTypeFullName //租赁拆单车型
				})


			},
			// 获取包车套餐
			getTempval(item) {
				tempvaluationlist({
					params: {
						rentType: this.datar.rentType || null,
						feeTemplateId: this.datar.feeTemplateId,
						carTypeId: item.carTypeId,
					}
				}).then((data) => {
					if (data.length == 0) return uni.$u.toast('暂无该车型套餐,请添加。')
					this.packaColumnsr = [data]
					this.packageShow = true
				})
			},
			// 初始
			objData() {
				let obj = {
					carId: null,
					carTypeId: this.datar.compOrderState == 20 ? this.datar.wantCarTypeId : this.datar
						.compOrderState == 25 ?
						this.datar.wantCarTypeId : this.datar.carTypeId,
					driverUserId: null,
					reserveStartTime: this.datar.reserveStartTime,
					valuationId: null,
					carTypeIdName: this.datar.compOrderState == 20 ? this.datar.wantCarTypeFullName : this.datar
						.compOrderState == 25 ?
						this.datar.wantCarTypeFullName : this.datar.carTypeFullName,
					valuationFullName: null,
					carName: null,
					driverUserName: null,
				}
				return obj
			},

			// 时间格式化
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				if (type === 'hour') {
					return `${value}时`
				}
				if (type === 'minute') {
					return `${value}分`
				}
				return value
			},
			// 选择车型后缀

			async carTypechange(e) {
				let that = this
				that.$nextTick(() => {
					const {
						columnIndex,
						value,
						values, // values为当前变化列的数组内容
						index,
						// 微信小程序无法将picker实例传出来，只能通过ref操作
						picker = that.$refs.uPicker
					} = e
					// console.log();
					// 当第一列值发生变化时，变化第二列(后一列)对应的选项
					if (columnIndex === 0) {
						// picker为选择器this实例，变化第二列对应的选项
						if (value[0].children && value[0].children.length > 0) {
							picker.setColumnValues(1, that.cartypetree[index].children)
						} else {
							picker.setColumnValues(1, [])
						}
					}
				})

			},
		}
	}
</script>

<style lang="scss" scoped>
	.new_dis {
		.nav_box {
			border-top: 1rpx solid #e7e7e7;
			background-color: #fff;
		}

		.enterp {
			background-color: #fff;
			margin: 30rpx;
			padding: 30rpx;
			border-radius: 20rpx;

			/* #ifdef MP-WEIXIN */
			padding-bottom: 100rpx;
			/* #endif */

			.ent_left {
				flex: 1;

				.ent_box {
					margin-bottom: 20rpx;

					.ent_icon {
						margin-right: 10rpx;
					}
				}

			}

			.ent_right {
				margin-left: 20rpx;
			}

		}
	}

	.text-info {
		color: red;
		display: flex;
		font-size: 26rpx;
		margin: 10rpx;
		align-content: center;
		justify-content: center;
	}
</style>