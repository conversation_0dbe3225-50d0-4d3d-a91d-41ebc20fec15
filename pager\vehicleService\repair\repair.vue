<template>
	<view class="contenr">
		<u--form :model="form" :rules="rulesr" ref="uForm" class="is-bottom" :label-width="100" labelAlign="right">
			<view class="formBg">
				<u-form-item :required="true" label="选择车辆 :" prop="userInfo" borderBottom @tap="selectBtn(1)">
					<u--input v-model="form.userInfo" disabled disabledColor="#ffffff" placeholder="请选择车辆"
						border="none"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item :required="true" label="品牌型号 :" prop="name" borderBottom>
					<u-input v-model="form.name" border="none" placeholder="根据选择车辆自动填充品牌型号" disabled
						disabledColor="#ffffff" />
				</u-form-item>
			</view>
			<view class="formBg">
				<u-form-item label="送修日期 :" prop="timer" borderBottom @click="tiemBtn">
					<u--input v-model="form.timer" disabled disabledColor="#ffffff" placeholder="请选择送修日期" border="none">
					</u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item label="送修里程 :" prop="name" borderBottom>
					<u-input v-model="form.name" border="none" placeholder="请输入送修里程" />
					<text slot="right">公里</text>
				</u-form-item>
				<u-form-item label="提车日期 :" prop="name" borderBottom>
					<u--input v-model="form.userInfo" disabled disabledColor="#ffffff" placeholder="请选择提车日期"
						border="none"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item label="提车里程 :" prop="name" borderBottom>
					<u-input v-model="form.name" border="none" placeholder="请输入提车里程" />
					<text slot="right">公里</text>
				</u-form-item>
			</view>
			<view class="formBg">
				<u-form-item label="维修商 :" prop="name" borderBottom>
					<u-input v-model="form.name" border="none" placeholder="请输入维修商" />
				</u-form-item>
				<u-form-item label="维修单号 :" prop="name" borderBottom>
					<u-input v-model="form.name" border="none" placeholder="请输入维修单号" />
				</u-form-item>
				<u-form-item label="维修金额 :" prop="name" borderBottom>
					<u-input v-model="form.name" border="none" placeholder="请输入维修金额" />
					<text slot="right">元</text>
				</u-form-item>
			</view>
			<view class="formBg">
				<u-form-item label="维修明细 :" prop="timer" borderBottom>
					<u-icon slot="right" name="plus" @tap="popupShow=true"></u-icon>
				</u-form-item>
				<view>
					<u-collapse :value="popupList.map((e,idx)=>{return idx})">
						<u-collapse-item v-for="(item,index) in popupList" :key="index">
							<view slot="title" class="u-align-items u-row-between">
								<text>项目{{index+1}} :</text>
								<text>{{item.five}}元</text>
							</view>
							<view class="collBox">
								<u-cell-group>
									<u-cell title="维修项目 :" :value="item.one">
										<u-icon name="edit-pen" slot='right-icon' :size='20'></u-icon>
									</u-cell>
									<u-cell title="材料费 :" :value="`${item.tow} 元`">
										<u-icon name="edit-pen" slot='right-icon' :size='20'></u-icon>
									</u-cell>
									<u-cell title="工时费 :" :value="`${item.three} 元`">
										<u-icon name="edit-pen" slot='right-icon' :size='20'></u-icon>
									</u-cell>
									<u-cell title="其他费用 :" :value="`${item.four} 元`">
										<u-icon name="edit-pen" slot='right-icon' :size='20'></u-icon>
									</u-cell>
									<u-cell title=" ">
										<u-icon name='trash-fill' slot='right-icon' :size='22' color='red'
											@tap="delBtn(index)"></u-icon>
									</u-cell>
								</u-cell-group>
							</view>
						</u-collapse-item>
					</u-collapse>
				</view>
			</view>
			<view class="formBg">
				<u-upload :fileList="fileList3" @afterRead="afterRead" @delete="deletePic" name="3" multiple
					:maxCount="10" :previewFullImage="true" class="pding"></u-upload>
			</view>

			<view class="formBg pding wx_pd">
				<text>备注说明 :</text>
				<u--textarea v-model="form.name" placeholder="请输入备注说明" confirmType="done"></u--textarea>
			</view>

		</u--form>
		<!-- <view class="formBg pding btn">
			<u-button type="primary" color="#346CF2" text="提交确认" @click="submit()"></u-button>
		</view> -->
		<view class="formBg pding btn u-align-items u-row-around">
			<view class="" style="width: 70%;">
				<u-button type="primary" color="#346CF2" text="提交确认" @tap="submit()"></u-button>
			</view>
			<view class="" style="width: 25%;">
				<u-button type="primary" color="#e1e346CF21e1" text="上报列表" @click="goRouter()"></u-button>
			</view>
		</view>

		<!-- 弹窗 -->
		<popupr :show="popupShow" :title="'添加维修项目'" @close="popupShow=false">
			<slot>
				<u--form :model="popupForm" :rules="popupRules" ref="popupForm" label-width="90" class="is-popup">
					<u-form-item label="维修项目 :" prop="one" :required='true'>
						<u-input v-model="popupForm.one" placeholder="请输入维修项目" />
					</u-form-item>
					<u-form-item label="材料费 :" prop="tow" :required='true'>
						<u-input v-model="popupForm.tow" placeholder="请输入材料费 :" type="number" @input="sumBtn" />
						<text slot="right">元</text>
					</u-form-item>
					<u-form-item label="工时费 :" prop="three">
						<u-input v-model="popupForm.three" placeholder="请输入工时费" type="number" @input="sumBtn" />
						<text slot="right">元</text>
					</u-form-item>
					<u-form-item label="其他费用 :" prop="four">
						<u-input v-model="popupForm.four" placeholder="请输入其他费用" type="number" @input="sumBtn" />
						<text slot="right">元</text>
					</u-form-item>
					<u-form-item label="小计 :" prop="five">
						<u--input v-model="popupForm.five" disabled disabledColor="#ffffff" placeholder="自动计算"
							border="none"></u--input>
						<text slot="right">元</text>
					</u-form-item>
				</u--form>
				<u-button type="primary" color="#346CF2" text="保存" @click="popuprBtn()"></u-button>
			</slot>
		</popupr>

		<!-- 日期选择 -->
		<u-datetime-picker :show="timeShow" v-model="timeMode" mode="date" :formatter="formatter" @cancel="tiemBtn"
			:closeOnClickOverlay='true' @close="tiemBtn" @confirm='confirms'>
		</u-datetime-picker>

		<!-- 选择器 -->
		<u-picker :show="pickerShow" :columns="columnsr" keyName="name" :closeOnClickOverlay='true'
			@confirm="selectConfirm" @cancel="pickerBtn" @close="pickerBtn"></u-picker>
	</view>
</template>

<script>
	import popupr from '../popupr/popupr.vue'
	export default {
		components: {
			popupr
		},
		props: ['carList', 'typer'],
		data() {
			return {
				form: {
					name: "",
					userInfor: "",
					userInfo: '',
					valShow: false
				},
				rulesr: {
					'namer': [{
						required: true,
						message: '请输入姓名',
						trigger: ['blur', 'change']
					}],
					'userInfo': [{
						required: true,
						message: '请输入姓名',
						trigger: ['blur', 'change']
					}],

				},
				fileList3: [{
					url: 'https://cdn.uviewui.com/uview/swiper/1.jpg',
				}],
				// customStyle: ,
				timeShow: false,
				timeMode: Number(new Date()),
				pickerShow: false,
				columnsr: [],
				maintain: [],
				popupShow: false,
				popupForm: {
					one: ''
				},
				popupRules: {
					'one': [{
						required: true,
						message: '请输入姓名',
						trigger: ['blur', 'change']
					}],
				},
				popupList: []
			}
		},
		mounted() {
			let user = JSON.parse(localStorage.getItem('userInfo'))
			this.form.userInfor = user.name
		},
		methods: {
			// 删除
			delBtn(idx) {
				this.popupList.splice(idx, 1)
			},
			// 保存保养项目
			popuprBtn() {
				this.popupList.push(JSON.parse(JSON.stringify(this.popupForm)))
				this.popupShow = false
				console.log(this.popupList, 'this.popupList')
			},
			// 计算
			sumBtn() {
				let zero = (e) => {
					return e ? Number(e) : 0
				}
				this.popupForm.five = zero(this.popupForm.tow) + zero(this.popupForm.three) + zero(this.popupForm.four)
			},
			// 选择
			selectConfirm(e) {
				console.log(e, 'eeeee')
				this.pickerBtn()
			},
			// 打开
			selectBtn(t) {
				if (t == 1) {
					this.columnsr = [
						[{
							id: 92,
							name: '92号'
						}, {
							id: 95,
							name: '95号'
						}]
					]
				}
				this.pickerBtn()
			},

			// 选中时间
			confirms(e) {
				console.log(uni.$u.timeFormat(e.value, 'yyyy-mm-dd'), 'ee')
				this.tiemBtn()
			},
			// 开关
			tiemBtn() {
				this.timeShow = !this.timeShow
			},
			pickerBtn() {
				this.pickerShow = !this.pickerShow
			},

			// 时间格式化
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				return value
			},
			goRouter() {
				uni.$u.route({
					url: '/pager/vehicleEscala/vehicleEscala',
					params: {
						id: this.typer,
					},
				})
			},
			// 保存
			submit() {
				this.$refs.uForm.validate().then(res => {
					uni.$u.toast('校验通过')
				}).catch(errors => {
					uni.$u.toast('校验失败')
				})
			}

		}
	}
</script>

<style lang="scss" scoped>
	.is-bottom {
		padding-bottom: 120rpx;
	}

	.formBg {
		margin: 20rpx 0;
		background-color: #fff;

		/deep/.u-form-item__body__left__content__label {
			display: flex;
			justify-content: flex-end !important;
			padding-right: 10rpx;
		}

		/deep/.item__body__right__content__icon {
			padding-right: 30rpx;
		}

		/deep/.u-form-item__body {
			padding: 30rpx 0;
		}
	}

	.btn {
		position: fixed;
		bottom: 0;
		width: calc(100% - 60rpx);
		margin: 0 !important;
		border-top: 4rpx solid #f3f3f3;
		z-index: 99;
	}

	.pding {
		padding: 20rpx 30rpx;

	}

	.wx_pd {
		/* #ifdef MP-WEIXIN */
		padding-bottom: 120rpx;
		/* #endif */
	}

	/deep/.u-form-item__body__left__content__label {
		display: flex;
		justify-content: end !important;
		padding-right: 10rpx;
	}

	/deep/.u-collapse-item__content__text {
		padding: 0;
	}

	.is-popup {
		/deep/.item__body__right__content__icon {
			padding-left: 20rpx;
		}

		/deep/.u-form-item__body__left__content__required {
			left: 0;
		}
	}

	.is-bottom {
		/deep/.u-form-item__body {
			padding: 30rpx 0;
		}

		/deep/.item__body__right__content__icon {
			padding-right: 30rpx;
		}

		/deep/.u-form-item__body__left__content__required {
			left: 10rpx;
		}
	}
</style>
