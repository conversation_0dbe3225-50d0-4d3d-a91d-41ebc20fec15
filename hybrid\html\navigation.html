<!doctype html>
<html>

	<head>
		<meta charset="utf-8">
		<title>行程</title>
		<meta name="keywords" content="" />
		<meta name="description" content="" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
		<meta name="format-detection" content="telephone=no" />
		<!-- 引入样式和js文件 -->
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		<script type="text/javascript" src="js/axios.js"></script>
		<!-- 	<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black"> -->
		<meta name="author" content="CSS5, css5.com.cn" />
		<link rel="stylesheet" href="https://i.gtimg.cn/vipstyle/frozenui/2.0.0/css/frozen.css">
		<link rel="stylesheet" href="css/font-awesome.min.css" />
		<link rel="stylesheet" type="text/css" href="css/hybird.css">
		<script src="https://cdn.bootcss.com/vue/2.6.11/vue.js"></script>
		<script src="js/element-ui/lib/index.js"></script>
		<script type="text/javascript">
			window._AMapSecurityConfig = {
				securityJsCode: 'a0ec29ceac10861c863c75bcf78565a4',
			}
		</script>

		<script src="https://webapi.amap.com/loader.js"></script>

	</head>
	<style>
		body {
			overflow: hidden;
			touch-action: none;
		}
	</style>

	<body>
		<div id="app">
			<div class="backbtn" @click="goBack()" style="top:30px">
				<i class="fa fa-angle-left"></i>
			</div>

			<div class="top_tips" v-show="!loderShow" style="top:2px">
				<div class="top_tips_item">
					预计里程: <span>{{tipsObj.distance}}</span> 公里 - 预估用时: <span>
						{{tipsObj.time}}</span></div>
			</div>

			<div class="mapdown" :class="[touchmoveType?'fullScreen':'narrow']" @touchmove="touchmove"
				@touchstart='touchstart' :style="{height:moveHgt}">
				<div class="mapdown-top d_b_b">
					<div class="mapdown-slio"></div>
					<div class="u-flex u-row-between" style="width: 100%;">
						<span>
							<!-- {{moveHgt}}-- {{scrollHgt}} -->
							{{getWash(detail.compOrderState)}}
						</span>
						<span class="ui-img-icon"
							style="background-image:url(https://zqcx.di-digo.com/app/image/diandian.png)"
							@click="openAir"></span>

						<div class="trip_s_airClass" v-if="airShow">
							<div class="footbtn footbtnCenter" @click="telBtn(detail.driverMobile)"
								v-if="browserType!='Safari'">
								<i class="fa fa-volume-control-phone"></i>联系司机
							</div>
							<div class="footbtn " :class='{"border":browserType!="Safari"}'
								@click="stepShow=true,airShow=false">
								<i class="fa fa-list"></i>订单跟踪
							</div>
							<div class="footbtn border" @click="goEditr">
								<i class="fa fa-list"></i>变更行程
							</div>
							<div class="footbtn " @click="cancelorder=true,airShow=false">
								<i class="fa fa-times-circle"></i>取消订单
							</div>
						</div>

					</div>
				</div>

				<div class="mapdown-down" :class="[moveHgt=='100%'?'mapdown-down-scroll':'']" ref="scrollContainer"
					@scroll="handleScroll">
					<ul class="ui-list ui-list-function d_b_b toum "
						style="display: flex;justify-content: space-between;align-items: center;"
						v-if="detail.carNumber">
						<li class="mapdownbox">
							<div class="ui-avatar posIcon">
								<span
									style="background-image:url(https://zqcx.di-digo.com/app/image/wdsq_qcl.png)"></span>
							</div>
							<div class="ui-list-info">
								<h5 class="ui-nowrap">{{detail.carNumber}}
									{{detail.numberColor?(detail.numberColor) :''}}
								</h5>
								<p>{{detail.wantCarTypeFullName}}</p>
							</div>
						</li>
						<li class="mapdownbox mapdownboxr">
							<!-- <div class="ui-avatar posIcon">
								<span
									style="background-image:url(https://zqcx.di-digo.com/app/image/wdsq_qcl.png)"></span>
							</div> -->
							<div>
								{{detail.driverName}}
							</div>
							<!-- 11111111 -->
						</li>
					</ul>
					<div class="d_txt ui-row-flex ui-whitespace">
						<div class="ui-col" style="font-size:13px">
							<!-- <i class="fa fa-clock-o"></i> -->预约时间：
							{{detail.reserveStartTime | timeFormat('mm月dd日 hh:MM')}} -
							{{detail.reserveEndTime | timeFormat('mm月dd日 hh:MM')}}
						</div>
					</div>
					<div class="d_txt ui-row-flex ui-whitespace">
						<div class="ui-col ui-col-4">
							行程单号：{{detail.travelCode}}
							<span @click="spanBtm(detail.travelCode)">复制</span>
						</div>
						<!-- 	<div class="ui-col text-right" @click="goOrderDetail(detail.travelId)">
							<span>订单详情 <i class="fa fa-angle-double-right"></i></span>
						</div> -->
					</div>
					<!-- 费用信息 -->
					<div class="money" v-if="detail.compOrderState==100||detail.compOrderState==200">
						<div class="money-top d_b_b">
							<span style="font-size: 17px;">{{(detail.totalFee - detail.reduceFee -
							detail.discountReduceFee).toFixed(2)}}
								元</span>
							<div style="font-size: 23px;">
								<i class="fa fa-angle-down" v-show="!monryShow" @click="monryShow=!monryShow"></i>
								<i class="fa fa-angle-up" v-show="monryShow" @click="monryShow=!monryShow"></i>
							</div>
						</div>
						<div class="money-btm " v-show="!monryShow">
							<div class="app_t_line u-flex u-row-between d_b_b">
								<span class="mar">服务里程</span>
								<div class="ui-txt-info" style="color: #262626;">{{detail.actualDistance}} 公里</div>
							</div>
							<div class="app_t_line u-flex u-row-between d_b_b">
								<span class="mar">服务时长</span>
								<div class="ui-txt-info" style="color: #262626;">{{detail.actualDuration}} </div>
							</div>
							<div v-for="(item,index) in detail.feeDetail" :key="index">
								<div class="app_t_line u-flex u-row-between d_b_b">
									<span class="mar">{{item.key}}</span>
									<div class="ui-txt-info" style="color: #262626;">{{item.value?item.value:0}}元</div>
								</div>
							</div>
							<div class="app_t_line u-flex u-row-between d_b_b">
								<span class="mar">订单金额</span>
								<div class="ui-txt-info" style="color: #262626;"> {{detail.totalFee}} 元</div>
							</div>
							<div class="app_t_line u-flex u-row-between d_b_b" v-if="detail.discountReduceFee">
								<span class="mar">企业折扣</span>
								<div class="ui-txt-info" style="color: #262626;">{{detail.discountReduceFee}} 元</div>
							</div>
							<div class="app_t_line u-flex u-row-between d_b_b" v-if='detail.reduceFee'>
								<span class="mar">优惠金额</span>
								<div class="ui-txt-info" style="color:red;">- {{detail.reduceFee}} 元</div>
							</div>
							<div class="app_t_line u-flex u-row-between d_b_b">
								<span class="mar">实付金额</span>
								<span>{{(detail.totalFee - detail.reduceFee - detail.discountReduceFee).toFixed(2)}}
									元</span>
							</div>
						</div>
					</div>
					<div class="money money-btm">
						<div class="app_t_line u-flex u-row-between d_b_b" v-if='detail.applyCode'>
							<span class="mar">申请单号</span>
							<span>{{detail.applyCode}} <span @click="spanBtm(detail.applyCode)">复制</span>
							</span>
						</div>
						<div class="app_t_line u-flex u-row-between d_b_b">
							<span class="mar">乘车人</span>
							<span>{{detail.psgName}} </span>
						</div>
						<div class="app_t_line u-flex u-row-between d_b_b" v-if="detail.regulationName">
							<span class="mar">用车制度</span>
							<span>{{detail.regulationName}} </span>
						</div>
						<div class="app_t_line u-flex u-row-between d_b_b">
							<span class="mar">成本中心</span>
							<span>{{detail.costCenterName}} </span>
						</div>
						<div class="app_t_line u-flex u-row-between d_b_b">
							<span class="mar">乘车人数</span>
							<span>{{detail.psgNums}} 人</span>
						</div>
						<div class="app_t_line u-flex u-row-between d_b_b" v-if='detail.tgtUserNames'>
							<span class="mar">同行人员</span>
							<span>{{detail.tgtUserNames}} 人</span>
						</div>
						<div class="app_t_line u-flex u-row-between d_b_b">
							<span class="mar">所选车型</span>
							<span>{{detail.wantCarTypeFullName}} </span>
						</div>
						<div class="app_t_line u-flex u-row-between d_b_b" v-if='detail.description'>
							<span class="mar">用车备注</span>
							<span>{{detail.description}} 人</span>
						</div>
						<div class="u-flex u-row-center" @click="goPages()" v-if="detail.orderType==1">
							<span class="rules">计费规则</span>
						</div>
					</div>
				</div>
			</div>

			<div id="container" :style="{'margin-top':(touchmoveType?'-45%':0)}"></div>

			<!-- 组件 -->
			<div :class="{show:cancelorder,'ui-dialog':true}">
				<div class="ui-dialog-cnt-m">
					<div class="popup_tit">
						是否取消订单
					</div>
					<div class="u-flex u-row-between popup_btn_box">
						<button class="popup_btn_yes" @click="cancelOrder(detail.travelId)">确定</button>
						<button class="popup_btn_no" @click="cancelorder=false">取消</button>
					</div>
				</div>
			</div>

			<!-- 订单跟踪 -->
			<div :class="{show:stepShow,'ui-dialog':true}">
				<div class="ui-dialog-cnt-m" style="padding:20px;">
					<ul style="max-height: 400px;overflow-y: auto;">
						<li v-for='item in detail.orderEventHis' :key='item.operatorTimes' class="stepList">
							<span> <i class="fa fa-tag fa-icont"></i>{{item.operatorTimes}}</span>
							<span style="box-shadow: 0 0 3px 0px #6666665e;margin: 5px;padding: 5px 15px;">
								{{item.operatorName}} : {{item.evtDetContent}}
							</span>
						</li>
					</ul>

					<div class="u-flex" style="display: flex; justify-content: center;">
						<button class="popup_btm_notr" @click="stepShow=false">关闭</button>
					</div>
				</div>
			</div>

			<!-- 加载中 -->
			<div :class="{show:loderShow,'ui-dialog':true}">
				<div class="loder">
					<div></div>
					<div></div>
					<div></div>
				</div>
			</div>

		</div>
	</body>
	<!-- 微信 JS-SDK 如果不需要兼容小程序，则无需引用此 JS 文件。 -->
	<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
	<!-- uni 的 SDK -->
	<script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script>
	<script>
		document.addEventListener('UniAppJSBridgeReady', function() {});
	</script>

	<script>
		new Vue({
			el: '#app',
			data: {
				loderShow: false,
				isshow: false,
				starcount: 5,
				star: 0,
				urlObj: {},
				detail: {},
				cancelorder: false,
				touchmoveType: false,
				monryShow: false,
				startY: 0,
				moveEndY: 0,
				moveHgt: "30%",
				copyHgt: null,
				browserType: '',
				pageHgt: 0, //页面的高度
				scrollHgt: 0, //页面滚动的高度
				airShow: false,
				stepShow: false,
				tipsObj: {
					distance: '',
					time: '',
				}
			},
			filters: {
				timeFormat(dateTime = null, fmt = 'yyyy-mm-dd') {
					// 如果为null,则格式化当前时间
					if (!dateTime) dateTime = Number(new Date())
					// 如果dateTime长度为10或者13，则为秒和毫秒的时间戳，如果超过13位，则为其他的时间格式
					if (dateTime.toString().length == 10) dateTime *= 1000
					const date = new Date(dateTime)
					let ret
					const opt = {
						'y+': date.getFullYear().toString(), // 年
						'm+': (date.getMonth() + 1).toString(), // 月
						'd+': date.getDate().toString(), // 日
						'h+': date.getHours().toString(), // 时
						'M+': date.getMinutes().toString(), // 分
						's+': date.getSeconds().toString() // 秒
						// 有其他格式化字符需求可以继续添加，必须转化成字符串
					}
					for (const k in opt) {
						ret = new RegExp(`(${k})`).exec(fmt)
						if (ret) {
							fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1]
								.length, '0')))
						}
					}
					return fmt
				}
			},
			computed: {
				heightStyle() {
					return {
						height: this.currentHeight + 'vh',
					}
				},
			},
			mounted() {
				this.urlObj = this.getQueryString()
				// console.log(this.urlObj, '--------------------')
				this.getDetail()
				this.telBtm()
				// #ifdef H5
				let body = document.getElementsByTagName("body")
				body[0].style.backgroundColor = "#F9FAFE";
				window.alert = function(name) {
					const iframe = document.createElement('IFRAME');
					iframe.style.display = 'none';
					iframe.setAttribute('src', 'data:text/plain,');
					document.documentElement.appendChild(iframe);
					window.frames[0].window.alert(name);
					iframe.parentNode.removeChild(iframe);
				};
				// #endif

				const container = document.getElementById("container");
				container.style.filter =
					"Alpha(opacity=10,style=0)"

			},
			methods: {
				goPages() {
					uni.navigateTo({
						url: '/pager/billingRules/billingRules?rules=' + JSON.stringify(this.detail
							.tempValuationResultVo)
					});
				},
				goEditr() {
					let vv = this.urlObj
					let urlr = `/pager/editTrip/editTrip?id=${vv.id}&orderType=${vv.orderType}`
					uni.navigateTo({
						url: urlr
					})
				},
				telBtn(tel) {
					if (!tel) return alert('暂无联系方式')
					window.location.href = 'tel://' + tel
				},
				openAir() {
					this.$set(this, 'airShow', true)
					setTimeout(() => {
						this.$set(this, 'airShow', false)
					}, 3000)
				},
				handleScroll() {
					if (this.moveHgt != "100%") return
					this.scrollHgt = this.$refs.scrollContainer.scrollTop
				},
				touchstart(e) {
					this.startY = e.changedTouches[0].pageY
				},
				touchmove(e) {
					let Y = 0
					let sum = this.detail.carNumber ? 100 : 48
					this.moveEndY = e.changedTouches[0].pageY
					Y = this.moveEndY - this.startY
					if (Y > 100) {
						if (this.moveHgt == '100%') {
							if (this.scrollHgt == 0) {
								let Yr = 0
								Yr = this.moveEndY - this.startY
								if (Yr > 100) {
									this.touchmoveType = false
									this.$set(this, 'moveHgt', "30%")
									this.$set(this, 'scrollHgt', 0)
								}
							} else if (this.scrollHgt < -5) {
								this.touchmoveType = false
								this.$set(this, 'moveHgt', "30%")
								this.$set(this, 'scrollHgt', 0)
							}
						} else {
							Y = 0
							this.touchmoveType = false
							this.$set(this, 'moveHgt', "30%")
						}
						return //向下
					} else if (Y < 100) {
						if (this.pageHgt > (this.copyHgt)) {
							Y = 0
							this.touchmoveType = true
							this.$set(this, 'moveHgt', (this.copyHgt) + 'px')
						} else {
							this.$set(this, 'moveHgt', "100%")
						}
						return //向上
					}
					return
					if (Y > 100) {
						Y = 0
						this.touchmoveType = false
						this.$set(this, 'moveHgt', "30%")
						// this.$set(this, 'moveHgt', (this.copyHgt+48)+'px')
						return
						// alert("top 2 bottom");
					} else if (Y < 100) {
						Y = 0
						this.touchmoveType = true
						let sum = this.detail.carNumber ? 100 : 48
						this.$set(this, 'moveHgt', (this.copyHgt + sum) + 'px')
						// this.$set(this, 'moveHgt', "65%")
						return
						// alert("bottom 2 top");
					}
				},
				init(item) {
					let that = this
					// 高德地图控件
					AMapLoader.load({
						"key": "1cb53fa0e69dc44036161409f1d4039c", // 申请好的Web端开发者Key，首次调用 load 时必填
						"version": "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
						"plugins": ['AMap.Driving'], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
						"AMapUI": { // 是否加载 AMapUI，缺省不加载
							"version": '1.1', // AMapUI 版本
							"plugins": ['overlay/SimpleMarker'], // 需要加载的 AMapUI ui插件
						},
						"Loca": { // 是否加载 Loca， 缺省不加载
							"version": '2.0' // Loca 版本
						},
					}).then((AMap) => {
						///基本地图加载
						var map = new AMap.Map("container", {
							center: [that.detail.toLng, that.detail.toLat],
							resizeEnable: true,
							// zoom: 13 //地图显示的缩放级别
						});
						//构造路线导航类
						var driving = new AMap.Driving({
							map: map
						});
						// 途经点
						let markers = [{
								position: [that.detail.toLng, that.detail.toLat],
								text: that.detail.toAddrName
							},
							{
								position: [that.detail.fromLng, that.detail.fromLat],
								text: that.detail.fromAddrName
							},
						];
						let wayArr = []
						if (item.throughAddrInfo && item.throughAddrInfo.length > 0) {
							item.throughAddrInfo.forEach(v => {
								v.text = v.siteAddrName
								v.position = [v.siteLng, v.siteLat]
								wayArr.push(new AMap.LngLat(v.siteLng, v.siteLat))
							})
							markers.push(...item.throughAddrInfo)
						}

						// 根据起终点经纬度规划驾车导航路线
						driving.search(new AMap.LngLat(that.detail.fromLng, that.detail.fromLat), new AMap
							.LngLat(that.detail.toLng, that.detail.toLat), {
								waypoints: wayArr
							},
							function(status, result) {
								// result 即是对应的驾车导航信息，相关数据结构文档请参考  https://lbs.amap.com/api/javascript-api/reference/route-search#m_DrivingResult
								if (status === 'complete') {
									//构建自定义信息窗体
									var position = [that.detail.toLng, that.detail.toLat];
									var distance = result.routes[0].distance / 1000;
									var time = that.second(result.routes[0].time);
									that.tipsObj.distance = distance
									that.tipsObj.time = time
									// 添加多个点标记
									markers.forEach(marker => {
										const textMarker = new AMap.Text({
											// text: this.createInfoWindow(marker.text，),
											text: marker.text,
											position: marker.position,
											map: map,
											offset: new AMap.Pixel(-40, -55)
										});
									});
								} else {
									alert('网络不佳,请返回重试')
									uni.navigateBack();
								}
							});

						map.on("complete", function() {
							setTimeout(() => {
								that.loderShow = false
							}, 800)
						})

					}).catch((e) => {
						console.error(e); //加载错误提示
						that.loderShow = false
					});

				},

				goBack() {
					uni.navigateBack();
				},

				//构建自定义信息窗体
				createInfoWindow(distance, time) {
					var info = '<div class="mapTip">' +
						'<div class="mapTipt">' +
						'预计里程<span>' + distance + '</span>公里' +
						'</div>' +
						'<div class="mapTipd">' +
						'预估用时<span>' + time + '</span>' +
						'</div>' +
						'</div>';
					return info;
				},

				switchFun() {
					this.isshow = !this.isshow
				},

				clickStars(i) {
					this.star = i + 1
				},
				getQueryString() {
					let url = window.location.href
					console.log(url)
					let p = url.split('?')[1]
					let keyValue = p.split('&');
					let obj = {};
					for (let i = 0; i < keyValue.length; i++) {
						let item = keyValue[i].split('=');
						let key = item[0];
						let value = item[1];
						obj[key] = value;
					}
					return obj
				},
				getDetail(noinitMap) {
					this.loderShow = true
					let objr = {
						id: this.urlObj.id,
						orderType: this.urlObj.orderType,
						pageNum: 1,
						pageSize: 20,
					}
					axios.get("order/app/mytravelinfo", {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
						params: objr,
						additional: {
							typer: this.urlObj.type,
							accessToken: this.urlObj.accessToken
						}
					}).then(res => {
						if (res.data.code == 1) {
							let data = res.data.data
							if (data.throughAddrInfo) {
								let arrs = JSON.parse(data.throughAddrInfo)
								this.$set(data, 'throughAddrInfo', arrs)
								this.$set(data, 'throughAddrInfoName', arrs.map(v => v.siteAddrName)
									.join(
										' → '))
							}

							if (data.feeDetail && data.feeDetail != '{}') {
								data.feeDetail = JSON.parse(data.feeDetail)
								data.feeDetail.forEach((item, index) => {
									if (item.feeDetailCode == 200) {
										item.unit = JSON.parse(item.unit)
										item.unit.forEach(r => {
											data.feeDetail.push(r)
										})
									}
									if (item.key == "自定义项目费用") {
										data.feeDetail.splice(index, 1)
									}
								})
							}
							this.detail = data
							this.$nextTick(() => {
								this.swHeight()
							})
							// this.swHeight()
							if (!noinitMap) {
								this.init(data)
							}
						} else {
							this.loderShow = false
							alert('数据请求失败，请刷新重试。')
						}
					})
				},
				swHeight() {
					// let mapdown = document.getElementsByClassName('mapdown')
					let mapdownTop = document.getElementsByClassName('mapdown-top')
					let mapdownDown = document.getElementsByClassName('mapdown-down')
					// this.copyHgt = mapdown[0].scrollHeight //clientHeight  scrollHeight offsetHeight
					this.copyHgt = (mapdownTop[0].offsetHeight + mapdownDown[0].offsetHeight)
					console.log(this.copyHgt, '---');
					this.pageHgt = (document.body.clientHeight || document.documentElement.clientHeight) + 100 //页面高度
					// this.moveHgt = (mapdown[0].scrollHeight) + 'px'
				},
				spanBtm(orderId) {
					let input = document.createElement("input"); // 创建input对象
					input.value = orderId; // 设置复制内容
					document.body.appendChild(input); // 添加临时实例
					input.select(); // 选择实例内容
					document.execCommand("Copy"); // 执行复制
					document.body.removeChild(input); // 删除临时实例
					alert('成功复制订单号')
				},
				telBtm(tel) {
					var explorer = navigator.userAgent;
					let text = 'not'
					if (explorer.indexOf("MSIE") >= 0) {
						this.browserType = 'ie'
					} else if (explorer.indexOf("Firefox") >= 0) {
						this.browserType = 'firefox'
					} else if (explorer.indexOf("Chrome") >= 0) {
						this.browserType = 'Chrome'
					} else if (explorer.indexOf("Opera") >= 0) {
						this.browserType = 'Opera'
					} else if (explorer.indexOf("Safari") >= 0) {
						this.browserType = 'Safari'
					} else if (explorer.indexOf("Netscape") >= 0) {
						this.browserType = 'Netscape'
					}
				},
				second(value) {
					var theTime = parseInt(value); // 秒
					var middle = 0; // 分
					var hour = 0; // 小时

					if (theTime >= 60) {
						middle = parseInt(theTime / 60);
						theTime = parseInt(theTime % 60);
						if (middle >= 60) {
							hour = parseInt(middle / 60);
							middle = parseInt(middle % 60);
						}
					}
					var result = "";
					if (theTime > 0) {
						result = "" + parseInt(theTime) + "秒"
					}
					if (middle > 0) {
						result = "" + parseInt(middle) + "分" + result;
					}
					if (hour > 0) {
						result = "" + parseInt(hour) + "小时" + result;
					}
					return result;
				},
				getWash(stater) {
					return stater == 10 ? "待审批" : stater == 95 ? "行后待审批" : stater == 20 ? "待派车" : stater == 24 ?
						"租赁待审批" : stater == 25 ? "租赁待派车" : stater == 30 ? "待接单" : stater == 40 ? "待执行" : stater ==
						50 ? "执行中" : stater == 60 ? "到达出发地" : stater == 70 ? "进行中" : stater == 80 ? "到达目的地" :
						stater == 120 ? "结算审核中" : stater == 200 ? "已完成" : stater == 210 ? "已取消" : stater == 220 ?
						"审批驳回" : stater == 230 ? "调度驳回" : stater == 100 ? "待确认" : stater == 90 ? "已回场" : stater ==
						110 ? "待结算" : stater == 205 ? "待评价" : "订单异常,请返回刷新。"
				},
				// 跳转详情
				goOrderDetail(id) {
					// setTimeout(()=>{
					// 	console.log(1111);
					// },1000)
					uni.navigateTo({
						url: '/pager/orderDetail/orderDetail?id=' + id
					});
				},
				cancelOrder(id) {
					axios.put("order/app/cancelTravelOrder/" + id, {}, {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
					}).then(res => {
						this.cancelorder = false
						alert(res.data.msg)
						uni.navigateBack({
							delta: 1,
							success: function() {
								beforePage.$vm.queryList(); // 执行前一个页面的刷新
							}
						});
					})
				}

			}
		});
	</script>

</html>