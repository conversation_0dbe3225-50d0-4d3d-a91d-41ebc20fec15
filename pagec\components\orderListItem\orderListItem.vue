<template>
	<view class="item" @click="goPage(item)">
		<view class="u-flex u-row-between top mag">
			<view class="u-flex">
				<view class="jiao_img" v-if="item.travelType==1">
					<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/qi.png">
					</u-image>
				</view>
				<view class="jiao_img" v-if="item.travelType==2">
					<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/zu.png">
					</u-image>
				</view>
				<view class="jiao_img" v-if="item.applyType==2">
					<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/si.png">
					</u-image>
				</view>

				<span class="tags" v-if="item.travelType==1||item.travelType==2">{{item.psgNums}}人</span>
			</view>
			<span class="pad" style="color: #0F40F5;">{{stateWash(item.compOrderState)}}</span>
		</view>
		<view class="pad mag" style="color: #868686;">
			<span>行程单号：</span>
			<span>{{item.travelCode}}</span>
		</view>
		<view class="pad mag u-flex" style="color: #868686;">
			<u-image class="tab_icon" :width="14" :height="14"
				src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon.png"></u-image>
			<span class="pad">{{new Date(item.reserveStartTime).getTime() | date('mm月dd日 hh:MM')}} -
				{{new Date(item.reserveEndTime ).getTime()| date('mm月dd日 hh:MM')}}</span>
		</view>
		<view class="addrs mag">
			<view class="app_t_line u-flex ">
				<view class="spot"></view>
				<span style="width: 97%;">{{item.fromAddrName}}</span>
			</view>
			<view class="app_t_line u-flex " v-if="item.throughAddrInfoName">
				<view class="spot" style="background-color: #5ac725;"></view>
				<span style="width: 97%;">{{item.throughAddrInfoName}}</span>
			</view>

			<view class="app_t_line u-flex ">
				<view class="spot spottwo"></view>
				<span style="width: 97%;">{{item.toAddrName}}</span>
			</view>
		</view>

		<view class="pad mag u-flex u-row-between">
			<view class="u-flex">
				<u-image class="tab_icon" :width="14" :height="14"
					src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png"></u-image>
				<span class="pad" v-if="item.applyType==2">{{item.carNumber}}</span>
				<span class="pad" v-else>{{item.carTypeFullName}}</span>
			</view>
			<view class="">
				<span v-if="item.compOrderState==100" class="spanBtn" @click.stop='stateClick(item)'>确认费用</span>
				<span v-if="item.compOrderState==205" class="spanBtn" @click.stop='stateClick(item)'>去评价</span>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default: () => {
					return {}
				},
			}
		},
		data() {
			return {
				compOrderStateList: []
			}
		},
		mounted() {
			if (this.$store.state.dicVoList) {
				this.$store.state.dicVoList.forEach(vs => {
					if (vs.dicCode == 'comp_order_state') {
						this.compOrderStateList = vs.dicValueList
					}
				})
			}
		},
		methods: {
			stateWash(v) {
				let text = ''
				this.compOrderStateList.forEach(vv => {
					if (vv.dicValue == v) {
						text = vv.dicDescribe
					}
				})
				return text
			},
			goPage(item) {
				this.$emit('goDetail', item)
			},
			stateClick(item) {
				this.$emit('confirm', item)
			}
		}
	}
</script>

<style scoped>
	.item {
		font-size: 26rpx;
		background: #fff;
		margin: 20rpx;
		border-radius: 5rpx;
		padding-bottom: 10rpx;
	}

	.top {
		border-bottom: 1px solid #e6e6e6;
	}

	.addrs {
		margin: 10rpx;
		padding: 10rpx;
		background-color: #EFEFEF;
		border-radius: 20rpx;
	}

	.spot {
		width: 16rpx;
		height: 16rpx;
		background-color: #239EFC;
		border-radius: 50%;
		margin: 0 24rpx 0 8rpx;
	}

	.spottwo {
		background-color: #FF7031;
	}

	.spotthree {
		background-color: #28D79F;
	}

	.pad {
		padding: 0 20rpx;
	}

	.mag {
		margin-bottom: 15rpx;
	}

	.app_t_line {
		margin: 15rpx 0;
	}

	.tags {
		border: 1px solid #4b9eff;
		padding: 4rpx 10rpx;
		margin: 0 20rpx;
		border-radius: 10rpx;
		font-size: 12px;
		color: #4b9eff;
		background: #e9f3ff;
	}

	.spanBtn {
		background: rgb(15, 64, 245);
		color: #fff;
		padding: 4rpx 16rpx;
		border-radius: 10rpx;
		font-size: 12px;
	}
</style>