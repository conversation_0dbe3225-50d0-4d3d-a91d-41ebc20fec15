<template>
	<view class="driverIndex">
		<u-navbar title="正其出行" :placeholder="true">
			<view slot="left" @click="leftshow=true">
				<u-icon name="home" size="20"></u-icon>
			</view>

			<!-- <view slot="right" v-if="userInfo.appTerminal=='1,2'">
				<view class="nav_right" @click="show=true">
					<u-icon class="nav_right_icon" name="https://zqcx.di-digo.com/app/image/change.png" size="20">
					</u-icon>
					<view class="nav_right_txt">
						乘客入口
					</view>
				</view>
			</view> -->
		</u-navbar>

		<view class="index_top u-flex u-row-between">
			<view>
				<view class="top_item_num">
					{{indexData.totalHours || 0}}
				</view>
				<view class="top_item_txt">
					服务时长(h)
				</view>
			</view>
			<view>
				<view class="top_item_num">
					{{indexData.todayOrderNums || 0}}
				</view>
				<view class="top_item_txt">
					本月订单数
				</view>
			</view>
			<view>
				<view class="top_item_num">
					{{indexData.totalMileages || 0}}
				</view>
				<view class="top_item_txt">
					服务里程(km)
				</view>
			</view>
		</view>

		<view class="tabpage">

			<view class="app_list" v-for="(item,index) in dataList" :key="index">

				<view v-if="item.travelType==1" class="jiao_img">
					<u-image :lazyLoad="true" :width="30" :height="30"
						src="https://zqcx.di-digo.com/app/image/qi.png">
					</u-image>
				</view>
				<view v-if="item.travelType==2" class="jiao_img">
					<u-image :lazyLoad="true" :width="30" :height="30"
						src="https://zqcx.di-digo.com/app/image/zu.png">
					</u-image>
				</view>
				<view @click="godetail(item)">
					<view class="u-flex u-row-between">
						<view style="visibility: hidden;"></view>
						<view class="u-flex">
							<span class="text-dh">{{item.carNumber}} <span
									v-if="item.numberColor">【{{item.numberColor}}】</span> </span>
						</view>
						<view class="text-rt u-flex">
							<u-tag
								:text='item.compOrderState==10?"待审批":item.compOrderState==95?"行后待审批":item.compOrderState==20?"待派车":item.compOrderState==24?"租赁待审批":item.compOrderState==25?"租赁待派车":item.compOrderState==30?"待接单":item.compOrderState==40?"待执行":item.compOrderState==50?"执行中":item.compOrderState==60?"到达出发地":item.compOrderState==70?"进行中":item.compOrderState==80?"到达目的地":item.compOrderState==120?"结算审核中":item.compOrderState==200?"已完成":item.compOrderState==210?"已取消":item.compOrderState==220?"审批驳回":item.compOrderState==230?"调度驳回":item.compOrderState==100?"待确认":item.compOrderState==90?"已回场":item.compOrderState==110?"待结算":item.compOrderState==205?"待评价":"订单异常"'
								type="warning" size="mini" plain></u-tag>
						</view>
					</view>

					<u-line color="#E6E6E6" margin="20rpx 0"></u-line>

					<view class="app_t_line u-flex">
						<view class="tab_icon">
							<u-image class="" :width="14" :height="14" :lazyLoad="true"
								src="https://zqcx.di-digo.com/app/image/driver_s_icon2.png"></u-image>
						</view>

						<span>行程单号:{{item.travelCode}}</span>
						<span class='text-c' v-if="item.dispatchMode==1">拆</span>
					</view>

					<!-- <view class="app_t_line u-flex" v-if="item.travelType==2">
						<view class="tab_icon">
							<u-image :width="14" :height="14" :lazyLoad="true"
								src="https://zqcx.di-digo.com/app/image/gongsi2.png"></u-image>
						</view>
						<span>{{item.applyUnitName}}</span>
					</view> -->

					<!-- 企业单，预约开始时间跟预约结束时间 -->
					<view class="app_t_line u-flex" v-if="item.travelType==1">
						<view class="tab_icon">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/driver_s_icon1.png"></u-image>
						</view>
						<span>{{item.reserveStartTimeSeconds | date('mm月dd日 hh:MM')}} -
							{{item.reserveEndTimeSeconds | date('mm月dd日 hh:MM')}}</span>
					</view>

					<!-- 租赁单，预约开始时间跟计费套餐全称 -->
					<view class="app_t_line u-flex" v-else>
						<view class="tab_icon">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/driver_s_icon1.png"></u-image>
						</view>
						<span>{{item.reserveStartTimeSeconds | date('mm月dd日 hh:MM')}}</span>
					</view>

					<!-- <view class="app_t_line u-flex" v-if="item.travelType==2">
						<view class="tab_icon">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/entp_icon2.png"></u-image>
						</view>
						<span>{{item.valuationFullName}}</span>
					</view> -->


					<view class="form_bg">
						<view class=" u-flex">
							<view class="spot"></view>
							<span style="flex: 1;">{{item.fromAddrName}}</span>
						</view>

						<view class="app_t_line u-flex" v-if="item.throughAddrInfoName">
							<view class="spot" style="background-color: #5ac725;"></view>
							<view style="flex: 1;">{{item.throughAddrInfoName}}</view>
						</view>

						<view class="app_t_line u-flex">
							<view class="spot spottwo"></view>
							<span style="flex: 1;">{{item.toAddrName}}</span>
						</view>
					</view>
				</view>


				<view style=" display: flex;justify-content: space-between;margin-bottom: 10rpx;">
					<view class=" u-flex">
						<block v-if="item.travelType==2">
							<view class="tab_icon">
								<u-image :width="14" :height="14"
									src="https://zqcx.di-digo.com/app/image/entp_icon2.png"></u-image>
							</view>
							<span>{{item.valuationFullName}}</span>
						</block>
					</view>
					<view class="u-flex">
						<view class="item_km">
							{{item.aboutDistance/1000 | numFilter}}km
						</view>
						<template v-if="item.compOrderState==50||item.compOrderState==60||item.compOrderState==80">
							<view class="item_line"></view>
							<view class="dh_btn">
								<!-- 	<u-button text="导航" size="mini" icon="map" plain type="primary"
									@click='navigaBtn(item)'>
								</u-button> -->
								<text @click='navigaBtn(item)'>导航</text>
							</view>
						</template>
					</view>

				</view>
				<u-line color="#E6E6E6" margin="20rpx 0" v-if="item.compOrderState==30"></u-line>
				<view v-if="item.compOrderState==30" style=" display: flex;justify-content: flex-end;">
					<view class="cancel_btn">
						<u-button size="small" type="primary" class="cancel_btn" color="#c9cbd4" text="取消接单"
							@click="cancelorder(item)"></u-button>
					</view>
					<view class="con_btn">
						<u-button size="small" type="primary" color="#FF7031" text="确认接单" @click="getorder(item)">
						</u-button>
					</view>
				</view>

			</view>

		</view>

		<!-- <u-loadmore v-if="dataList.length>0" :status="status" /> -->

		<u-empty v-if="dataList.length==0" text="订单为空" icon="http://cdn.uviewui.com/uview/empty/order.png">
		</u-empty>

		<!-- 左侧弹出 -->
		<u-popup :show="leftshow" mode="left" @close="leftclose" :customStyle="customStyle">
			<view class="left_box">
				<!-- #ifdef H5 -->
				<view class="u-center">
				<!-- #endif -->
					<!-- #ifdef MP-WEIXIN -->
					<view class="u-center" style="margin-top: 80rpx;">
					<!-- #endif -->
						<u--image @click="goPerson()" class="left_img" width="173rpx" height="173rpx"
							:src="driverinfo.headUrl" shape="circle"></u--image>
					</view>
					<view class="u-text-center left_img_name">
						{{driverinfo.name?driverinfo.name:''}}
					</view>
					<view class="u-center  car_team_name">
						<view>
							{{driverinfo.carTeamName?driverinfo.carTeamName:''}}
						</view>
						<view class="u-center">
							<view class="car_team_fs">
								5.0
							</view>
							<u-icon name="star-fill" size="18" color="#FFC71C"></u-icon>
						</view>
					</view>

					<view class="cell_list">
						<u-cell-group class="cellgroup" :border='false'>
							<u-cell title="我的订单" @click="goPage(0)">
								<u-icon class="iconStyle" slot="icon" size="18"
									name="https://zqcx.di-digo.com/app/image/driver_icon1.png"></u-icon>
							</u-cell>
							<u-cell title="调度中心" @click="goPage(1)">
								<u-icon class="iconStyle" slot="icon" size="18"
									name="https://zqcx.di-digo.com/app/image/driver_icon2.png"></u-icon>
							</u-cell>
							<u-cell title="车务上报" :border='false' @click="goPage(2)">
								<u-icon class="iconStyle" slot="icon" size="18"
									name="https://zqcx.di-digo.com/app/image/driver_icon3.png"></u-icon>
							</u-cell>
							<u-cell title="乘客入口" @click="goPage(4)" v-if="userInfo.appTerminal=='1,2'">
								<u-icon class="iconStyle" slot="icon" size="18"
									name="https://zqcx.di-digo.com/app/image/driver_icon2.png"></u-icon>
							</u-cell>
							<u-cell title="设置" :border='false' @click="goPage(3)">
								<u-icon class="iconStyle" slot="icon" size="18"
									name="https://zqcx.di-digo.com/app/image/driver_icon4.png"></u-icon>
							</u-cell>
						</u-cell-group>
					</view>

				</view>
		</u-popup>

		<!-- 组件 -->
		<u-popup class="popup_bg" :round="5" :show="show" mode="center" @close="close">
			<view class="popup_tit">
				确定切换
			</view>
			<view class="popup_txt">
				是否确定切换至乘客端？
			</view>
			<view class="u-flex u-row-between popup_btn_box">
				<view class="popup_btn">
					<u-button color="#346CF2" type="primary" text="确定" @click="adoptFun()"></u-button>
				</view>
				<view class="popup_btn two">
					<u-button color="#E9ECF7" type="primary" text="取消" @click="close()">
					</u-button>
				</view>
			</view>
		</u-popup>

		<!-- 弹窗组件 -->
		<u-popup class="popup_bg" :round="5" :show="rejectshow" mode="center" @close="rejectclose"
			:customStyle='{width:"80%"}'>
			<view class="popup_tit">
				您取消的原因
			</view>
			<view class="popupcbox">
				<u--textarea placeholder="请输入取消原因" v-model="reason" height="107"></u--textarea>
			</view>

			<view class="u-flex u-row-around popup_btn_box">
				<view class="popup_btn">
					<u-button color="#346CF2" type="primary" text="提交" @click="carryOrder()"></u-button>
				</view>
				<view class="popup_btn two">
					<u-button color="#E9ECF7" type="primary" text="取消" @click="rejectclose()">
					</u-button>
				</view>

			</view>
		</u-popup>

		<!-- 组件 -->
		<u-popup class="popup_bg" :round="5" :show="yesshow" mode="center" @close="yesclose"
			:customStyle='{width:"80%"}'>
			<view class="popup_tit">
				<!-- 确定接单 -->
			</view>
			<view class="popup_txt">
				是否确定接单？
			</view>
			<view class="u-flex u-row-around popup_btn_box">
				<view class="popup_btn">
					<u-button color="#346CF2" type="primary" text="确定" @click="carryOrder()"></u-button>
				</view>
				<view class="popup_btn two">
					<u-button color="#E9ECF7" type="primary" text="取消" @click="yesclose()">
					</u-button>
				</view>
			</view>
		</u-popup>

		<!-- 导航组件 -->
		<u-popup :show="mapShow" @close="mapShow=false" :mode='"bottom"'>
			<view class="mapBox">
				<text class="map_title">要去哪里呢?</text>
				<ul class="map_ul" style="overflow-y: auto;height:90%;">
					<li class="u-flex u-row-between">
						<view>{{ mapItem.fromAddrName}}</view>
						<view class="map_text" @click="goMap(mapItem.fromLng,mapItem.fromLat,mapItem.fromAddrDetail)">
							去这里
						</view>
					</li>
					<li class="u-flex u-row-between" v-for="(item,index) in mapItem.throughAddrInfo" :key="index">
						<view>{{item.siteAddrName}}</view>
						<view class="map_text" @click="goMap(item.siteLng,item.siteLat,item.siteAddrDetail)">去这里</view>
					</li>
					<li class="u-flex u-row-between">
						<view>{{ mapItem.toAddrName}}</view>
						<view class="map_text" @click="goMap(mapItem.toLng,mapItem.toLat,mapItem.toAddrDetail)">去这里
						</view>
					</li>
				</ul>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		driverorder,
		driverreport,
		getuserinfo,
		driveracceptorder,
		appcacherole
	} from '@/config/api.js';
	export default {
		data() {
			return {
				dataList: [],
				status: 'loadmore',
				pageNum: 1,
				pageSize: 20,
				hasNext: true,
				leftshow: false,
				show: false,
				yesshow: false,
				rejectshow: false,
				isAccept: '',
				indexData: {},
				driverinfo: {},
				reason: '',
				choiceItem: {},
				fileList1: [],
				userInfo: {},
				mapShow: false,
				mapItem: {},
				customStyle: {
					"width": '40%'
				}
			}
		},
		filters: {
			numFilter(value) {
				let realVal = "";
				if (!isNaN(value) && value !== "") {
					// 截取当前数据到小数点后两位,改变toFixed的值即可截取你想要的数值
					realVal = parseFloat(value).toFixed(1);
				} else {
					realVal = "--";
				}
				return realVal;
			},

		},
		onShow() {
			this.userInfo = this.$common.getItem('userInfo')
			this.queryList()
			this.getdriverreport()
			this.getdriverinfo()
		},
		// onReachBottom() {
		// 	if(this.hasNext){
		// 		this.status = 'loading';
		// 		this.pageNum = ++ this.pageNum;
		// 		setTimeout(() => {
		// 			this.queryList()
		// 		}, 1000)
		// 	}
		// },
		methods: {


			// 导航
			navigaBtn(item) {
				this.mapItem = item

				let lngr = item.compOrderState == 60 ? item.toLng : item.fromLng
				let latr = item.compOrderState == 60 ? item.toLat : item.fromLat
				let namer = item.compOrderState == 60 ? item.toAddrDetail : item.fromAddrName

				if (item.compOrderState == 50) {
					this.goMap(lngr, latr, namer)
				} else {
					if (item.throughAddrInfo) {
						this.mapShow = true
					} else {
						this.goMap(lngr, latr, namer)
					}
				}

			},

			// 导航
			goMap(lngrs, latrs, namers) {
				// #ifdef MP-WEIXIN
				uni.openLocation({
					latitude: Number(latrs),
					longitude: Number(lngrs),
					name: namers,
					address: namers,
				})
				// #endif
				// #ifdef H5
				window.open(
					`https://uri.amap.com/marker?position=${lngrs},${latrs}&name=${namers}&coordinate=gaode&callnative=1`
				)
				// #endif

			},
			// 文件上传start
			// 删除图片
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'http://************:7001/upload', // 仅为示例，非真实的接口地址
						filePath: url,
						name: 'file',
						formData: {
							user: 'test'
						},
						success: (res) => {
							setTimeout(() => {
								resolve(res.data.data)
							}, 1000)
						}
					});
				})
			},
			// 文件上传end
			rejectclose() {
				this.rejectshow = false
			},
			cancelorder(item) {
				this.isAccept = '0'
				this.choiceItem = item
				this.rejectshow = true
				this.reason = ""
			},
			getorder(item) {
				this.isAccept = '1'
				this.choiceItem = item
				this.yesshow = true
			},
			close() {
				this.show = false
			},
			yesclose() {
				this.yesshow = false
			},
			leftclose() {
				this.leftshow = false
			},
			queryList() {
				uni.showLoading({
					title: '加载中'
				})
				driverorder({
					params: {
						// pageNum:this.pageNum,
						// pageSize:this.pageSize,
					}
				}).then((data) => {
					data.forEach(v => {
						if (v.throughAddrInfo) {
							v.throughAddrInfo = JSON.parse(v.throughAddrInfo)
							let text = v.throughAddrInfo.map(v => v.siteAddrName)
								.join(
									' → ')
							this.$set(v, 'throughAddrInfoName', text)
						}
					})
					this.dataList = data

					setTimeout(() => {
						uni.hideLoading();
					}, 600)
					// this.hasNext=data.hasNext
					// if(data.hasNext){
					// 	this.status='loadmore'
					// }else{
					// 	this.status='nomore'
					// }
				})
			},
			getdriverreport() {
				driverreport({
					params: {}
				}).then((data) => {
					this.indexData = data
				})
			},
			getdriverinfo() {
				getuserinfo({
					params: {}
				}).then((data) => {
					this.driverinfo = data.userInfoVo;
				})
			},
			godetail(item) {
				// uni.getLocation({
				// 	type: 'wgs84',
				// 	success: function(res) {
				// 		console.log('当前位置的经度：' + res.longitude);
				// 		console.log('当前位置的纬度：' + res.latitude);
				// 	}
				// });
				uni.$u.route('/pages/map/driverMap', {
					title: '订单详情',
					id: item.travelId,
					valuationId: item.valuationId,
					isBack:true
				})
				return
				uni.$u.route('/pages/map/mapnotit', {
					url: "/hybrid/html/drivermap.html",
					title: '订单详情',
					id: item.travelId,
					valuationId: item.valuationId
				});
			},
			carryOrder() {
				driveracceptorder({
					driverAcceptOrderVo: {
						isAccept: this.isAccept,
						reason: this.reason
					},
					id: this.choiceItem.travelId
				}).then((data) => {
					console.log(data, 'data');
					if (this.isAccept == 1) {
						uni.$u.toast('确认接单成功！')
					} else {
						uni.$u.toast('取消接单成功！')
					}
					this.rejectshow = false
					this.yesshow = false
					setTimeout(() => {
						this.queryList()
					}, 1000)
				})
			},
			goPage(num) {
				let that = this
				if (num == 0) {
					// 订单管理
					uni.$u.route('/pageDriver/driverOdeder/driverOdeder', {});
				} else if (num == 1) {
					// 调度中心
					uni.$u.route('/pageDriver/dispatching/dispatching', {
						data: encodeURIComponent(JSON.stringify(this.driverinfo))
					});
				} else if (num == 2) {
					// 车务上报
					uni.$u.route('/pageDriver/carReport/carReport', {});
				} else if (num == 3) {
					// 设置
					uni.$u.route('/pages/wayPassenger/my/setting/setting', {});
				} else if (num == 4) {
					that.leftshow = false
					uni.showModal({
						content: '是否确定切换至乘客端',
						success: (res) => {
							if (res.confirm) {
								that.adoptFun()
							}
						}
					})
				}
			},
			goPerson() {
				uni.$u.route('/pages/driver/personal/personal', {});
			},
			adoptFun() {
				appcacherole({
					cacheRoleType: '1'
				}).then(data => {
					this.userInfo.cacheRoleType = '1';
					this.$common.setItem('userInfo', this.userInfo)
					uni.switchTab({
						url: '/pages/wayPassenger/index/index'
					})
				})
			}

		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F9FAFE;

		.left_box {
			.left_img {
				margin-top: 140rpx;

				/deep/.u-image {
					left: 50%;
					margin-left: -86rpx;
				}
			}

			.left_img_name {
				font-size: 36rpx;
				margin-top: 20rpx;
			}

			.car_team_name {
				font-size: 28rpx;
				margin-top: 20rpx;
				padding: 0 90rpx;
				display: flex;
				flex-direction: column;
				width: 200rpx;
			}

			.cell_list {
				background-color: #fff;
				margin-top: 33rpx;

				/deep/ .u-line {
					border-color: #E9ECF7 !important;
					margin: 0 32rpx !important;
					width: calc(100% - 64rpx) !important;
				}

				.iconStyle {
					margin-right: 14rpx;
				}
			}
		}

		.driverIndex {
			.nav_right {
				.nav_right_icon {
					/deep/.u-icon__img {
						left: 50%;
						margin-left: -20rpx;
					}
				}

				.nav_right_txt {
					font-size: 24rpx;
				}
			}

			.index_top {
				// background: #346CF2;
				background-image: linear-gradient(to top, #004cff, #a9c3ff);
				border-radius: 0rpx 0rpx 20rpx 20rpx;
				color: #fff;
				padding: 50rpx 54rpx;

				.top_item_num {
					text-align: center;
					font-size: 48rpx;
				}

				.top_item_txt {
					text-align: center;
					font-size: 24rpx;
					margin-top: 30rpx;
				}
			}

			.tabpage {
				margin: 24rpx 32rpx;

				.filterBox {
					padding: 0 32rpx;

					.search {
						/deep/.u-search__content {
							background-color: #E9ECF7 !important;
						}

						/deep/.u-search__content__input {
							background-color: #E9ECF7 !important;
						}
					}
				}

				.app_list {
					border: 1px solid #E6E6E6;
					background-color: #fff;
					border-radius: 20rpx;
					padding: 25rpx;
					font-size: 28rpx;
					position: relative;
					margin-bottom: 20rpx;

					.form_bg {
						background: #F5F5F5;
						padding: 18rpx 0;
						margin: 10rpx 0;
						border-radius: 10rpx;
					}

					.jiao_img {
						position: absolute;
						left: 0;
						top: 0;
					}

					.appbtnbox {
						position: absolute;
						right: 27rpx;
						bottom: 27rpx;
					}

					.appbtn {
						background-color: #346CF2;
						padding: 4rpx 18rpx;
						font-size: 28rpx;
						color: #fff;
						border-radius: 8rpx;
						margin-left: 30rpx;
					}

					.icon-right {
						position: absolute;
						top: 50%;
						right: 27rpx;
						margin-top: -16rpx;
					}

					.tab_icon {
						margin-right: 16rpx;
					}

					.text-dh {
						font-size: 32rpx;
						font-weight: bold;
						color: #262626;
						padding-left: 40rpx;
					}

					.text-rt {
						color: #346CF2;

						.text-rt-tag {
							margin-right: 10rpx;
						}
					}

					.app_t_line {
						margin-top: 14rpx;
					}

					.spot {
						width: 16rpx;
						height: 16rpx;
						background-color: #FF7031;
						border-radius: 50%;
						margin: 0 24rpx 0 8rpx;
					}

					.spottwo {
						background-color: #404040;
					}

					.dh_btn {
						// width: 120rpx;
						// margin-right: 20rpx;
						color: #004cff;
					}

					.spotthree {
						background-color: #28D79F;
					}

					.cancel_btn {
						color: #666666 !important;
						width: 173rpx;
						margin-right: 14rpx;
					}

					.con_btn {
						width: 173rpx;
					}

					.item_line {
						width: 1px;
						height: 47rpx;
						background-color: #E6E6E6;
						margin: 0 20rpx;
					}

					.btn_box {
						height: 60rpx;
						justify-content: end;
					}
				}
			}

			.popup_bg {}

			.popupcbox {
				padding: 0 53rpx;
				margin-bottom: 30rpx;
			}

			.upload_box {
				padding: 0 53rpx 40rpx 53rpx;

				.upload_name {
					line-height: 80rpx;
					font-size: 36rpx;
				}
			}

			.select_input {
				height: 60rpx;
				border: 1px solid #CCCCCC;
				border-radius: 8rpx;
				padding-left: 20rpx;
				line-height: 60rpx;
				color: #999;
				margin: 20rpx 0;
			}

			.selectbox {
				padding: 0 53rpx;
				font-size: 28rpx;
			}

			.popup_btn_box {
				padding-bottom: 27rpx;
			}

			.popup_tit {
				font-size: 36rpx;
				text-align: center;
				padding: 40rpx 0 30rpx 0;
			}

			.popup_tit_t {
				padding: 40rpx 53rpx 30rpx 53rpx;
				font-size: 36rpx;
			}

			.popup_txt {
				font-size: 28rpx;
				text-align: center;
				margin-bottom: 40rpx;
			}

			/deep/.u-popup__content {
				width: 100%;
			}

			.popup_btn {
				width: 230rpx;
				height: 80rpx;

				/deep/ .u-button__text {
					font-size: 36rpx !important;
				}
			}

			.two {
				color: #666666 !important;
			}

		}


	}

	.text-c {
		border: 1rpx solid #9659f7;
		background: #eee5fe;
		margin: 0 20rpx;
		padding: 0 5rpx;
		color: #9659f7;
		border-radius: 10rpx;
		font-size: 16rpx;
	}

	.textHide {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	// 
	.mapBox {
		height: 30vh;
		background: #fff;
		margin-bottom: 30rpx;
		padding: 30rpx 30rpx 0;
		font-size: 24rpx;
		font-weight: bold;
		color: #000;

		.map_title {}

		.map_ul {
			padding: 0;
			margin-top: 20rpx;
			margin-bottom: 40rpx;

			li {
				background-color: #EFEFEF;
				padding: 16rpx 32rpx;
				border-radius: 12rpx;
				line-height: 40rpx;
				margin-bottom: 20rpx;
			}

			.map_text {
				background-color: #fff;
				border-radius: 25px;
				padding: 4px 14px;
			}
		}
	}
</style>