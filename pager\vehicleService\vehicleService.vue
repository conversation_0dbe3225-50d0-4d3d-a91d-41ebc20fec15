<template>
	<view>
		<u-navbar :title="initObjr.name" :autoBack="true" :placeholder="true">
		</u-navbar>
		<chargRefuel v-if="initObjr.id==0" @changeBack='changeBack'></chargRefuel>
		<comeon v-if="initObjr.id==1 || initObjr.id==8" :carList='carList' @preserva='collectVal' :typer="initObjr.id">
		</comeon>
		<maintain v-if="initObjr.id==3 || initObjr.id==2" :carList='carList' @preserva='collectVal'
			:typers="initObjr.id"></maintain>
		<insurance v-if="initObjr.id==4" :carList='carList' @preserva='collectVal' :typer="initObjr.id"></insurance>
		<yearly v-if="initObjr.id==5" :carList='carList' @preserva='collectVal' :typer="initObjr.id"></yearly>
		<other v-if="initObjr.id==6 || initObjr.id==7" :carList='carList' @preserva='collectVal' :typer="initObjr.id">
		</other>

	</view>
</template>

<script>
	let that
	import comeon from './comeon/comeon.vue'
	import maintain from './maintain/maintain.vue'
	import insurance from './Insurance/Insurance.vue'
	import yearly from './yearly/yearly.vue'
	import chargRefuel from './chargRefuel/chargRefuel.vue'
	import other from './other/other.vue'
	

	import {
		appCarlist,
		appCartraffic
	} from '@/config/consoler.js'
	export default {
		components: {
			comeon,
			maintain,
			insurance,
			yearly,
			chargRefuel,
			other,
		},
		data() {
			return {
				carList: [],
				fromAddrName: null,
				initObjr: {
					name: '',
					id: ''
				}
			}
		},
		watch: {
			fromAddrName(dee, app) {
				console.log(dee, 'dee')
				console.log(app, 'app')
			},
		},
		onLoad(option) {
			that = this
			that.initObjr = option
			that.initr()
		},
		methods: {
			changeBack(t) {
				that.initObjr.id = t
				that.initObjr.name = t == 1 ? '加油' : t == 8 ? '充电' : ''
			},
			// 保存
			collectVal(obj) {
				let objr = {
					carTrafficType: Number(that.initObjr.id),
				}
				objr[that.initObjr.type] = obj
				appCartraffic(objr).then(res => {
					uni.redirectTo({
						url: '/pager/vehicleEscala/vehicleEscala?id=' + that.initObjr.id,
					})
					uni.$u.toast('提交成功')
				})
			},
			// 初始化数据
			initr() {
				let objr = {
					pageNum: 1,
					pageSize: 999
				}
				appCarlist({
					params: objr
				}).then(res => {
					that.carList = [res.pageList]
				})
			},
			gopage() {
				uni.$u.route({
					url: '/pages/wayPassenger/workBench/vehicleService/escalation/escalation',
					params: {
						id: this.initObjr.id,
					},
				})
			},
		},
	}
</script>

<style scoped>
	/deep/.u-form-item__body__left__content__required {
		left: 0rpx !important;
	}
</style>