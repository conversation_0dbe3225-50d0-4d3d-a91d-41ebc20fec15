<!DOCTYPE html>
<html lang="en">

	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
		<script type="text/javascript">
			window._AMapSecurityConfig = {
				securityJsCode: 'a0ec29ceac10861c863c75bcf78565a4',
			}
		</script>
		<script src="https://webapi.amap.com/loader.js"></script>
		<script src="https://cdn.bootcss.com/vue/2.6.11/vue.js"></script>
	</head>

	<body>
		<div id="app">

		</div>
	</body>

	<!-- 微信 JS-SDK 如果不需要兼容小程序，则无需引用此 JS 文件。 -->
	<script type="text/javascript" src="//res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
	<script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script>
	<script>
		document.addEventListener('UniAppJSBridgeReady', function() {});
	</script>
	<script>
		new Vue({
			el: '#app',
			data: {},
			mounted() {
				this.initMap()
			},
			methods: {
				initMap() {
					AMapLoader.load({
						"key": "1cb53fa0e69dc44036161409f1d4039c", // 申请好的Web端开发者Key，首次调用 load 时必填
						"version": "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
						"plugins": [], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
						"AMapUI": { // 是否加载 AMapUI，缺省不加载
							"version": '1.1', // AMapUI 版本
							"plugins": ['overlay/SimpleMarker'], // 需要加载的 AMapUI ui插件
						},
						"Loca": { // 是否加载 Loca， 缺省不加载
							"version": '2.0' // Loca 版本
						},
					}).then((AMap) => {
						AMap.plugin('AMap.CitySearch', function() {
							var citySearch = new AMap.CitySearch()
							citySearch.getLocalCity(function(status, result) {
								if (status === 'complete' && result.info === 'OK') {
									uni.postMessage({
										data: {
											city:result.city
										}
									});
								} else {

								}
							})
						})
					}).catch((e) => {
						console.error(e); //加载错误提示
					});
				}
			}
		})
	</script>

</html>