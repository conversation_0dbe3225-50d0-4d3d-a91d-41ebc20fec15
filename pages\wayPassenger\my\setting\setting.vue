<template>
	<view class="my">
		<u-navbar title="设置" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="cell_list">
			<u-cell-group class="cellgroup" :border='false'>
				<u-cell title="清除缓存" @click="goPage(0)" isLink>
					<text slot="value">0.0MB</text>
				</u-cell>
				<u-cell title="修改密码" @click="goPage(1)" isLink>
				</u-cell>
			</u-cell-group>
		</view>

		<view class="cell_list" v-if="false">
			<u-cell-group class="cellgroup" :border='false'>
				<u-cell title="关于用户协议" @click="goPage(2)" isLink>
				</u-cell>
				<u-cell title="关于隐私协议" @click="goPage(3)" isLink>
				</u-cell>
				<u-cell title="用户指南" :border='false' @click="goPage(4)" isLink>
				</u-cell>
			</u-cell-group>
		</view>
		<view class="cell_list">
			<u-cell-group class="cellgroup" :border='false'>
				<u-cell title="意见反馈" @click="goPage(5)" isLink>
				</u-cell>
				<!-- <u-cell title="关于我们" @click="goPage(6)" isLink>
				</u-cell> -->
			</u-cell-group>
		</view>

		<view class="footer_box">
			<u-button type="primary" text="退出登录" @click="goout()"></u-button>
		</view>

		<!-- 组件 -->
		<u-popup class="popup_bg" :round="5" :show="show" mode="center" @close="close">
			<view class="popup_tit">
				退出登录
			</view>
			<view class="popup_txt">
				是否确定要退出登录
			</view>
			<view class="u-flex u-row-between popup_btn_box">
				<u-button class="popup_btn" color="#346CF2" type="primary" text="确定" @click="goout()"></u-button>
				<u-button class="popup_btn two" color="#E9ECF7" type="primary" text="取消" @click="close()"></u-button>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		outLogin
	} from '@/config/api.js';
	export default {
		data() {
			return {
				show: false,
			};
		},
		methods: {

			close() {
				this.show = false
			},
			goPage(type) {
				if (type == 0) {
					uni.$u.route('/pages/wayPassenger/my/order/order');
				} else if (type == 1) {
					uni.$u.route('/pager/mayChangePass/mayChangePass');
				} else if (type == 5) {
					uni.$u.route('/pages/wayPassenger/my/feedBack/feedBack');
				}
			},
			goout() {
				let that = this
				uni.showModal({
					// title: '退出登录',
					content: '是否确定要退出登录',
					success: function(res) {
						if (res.confirm) {
							// that.$common.removeItem("userInfo")
							// uni.navigateTo({
							// 	url: '/pages/login/login'
							// })
							outLogin().then(res => {
								that.$common.removeItem("userInfo")
								uni.reLaunch({
									url: '/pages/login/login'
								})
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
				return

			}
		}
	}
</script>

<style lang="scss" scoped>
	.my {
		padding-bottom: 80rpx;

		.popup_bg {
			.popupcbox {
				padding: 0 53rpx;
			}

			.select_input {
				height: 60rpx;
				border: 1px solid #CCCCCC;
				border-radius: 8rpx;
				padding-left: 20rpx;
				line-height: 60rpx;
				color: #999;
				margin: 20rpx 0;
			}

			.selectbox {
				padding: 0 53rpx;
				font-size: 28rpx;
			}

			.popup_btn_box {
				padding-bottom: 27rpx;
			}

			.popup_tit {
				font-size: 36rpx;
				text-align: center;
				padding: 40rpx 0 30rpx 0;
			}

			.popup_txt {
				font-size: 28rpx;
				text-align: center;
				margin-bottom: 40rpx;
			}

			/deep/.u-popup__content {
				width: 84%;
			}

			.popup_btn {
				width: 230rpx;
				height: 80rpx;

				/deep/ .u-button__text {
					font-size: 36rpx !important;
				}
			}

			.two {
				color: #666666 !important;
			}
		}

		.cell_list {
			background-color: #fff;
			margin-top: 27rpx;

			/deep/ .u-line {
				border-color: #E9ECF7 !important;
				margin: 0 32rpx !important;
				width: calc(100% - 64rpx) !important;
			}

			.iconStyle {
				margin-right: 14rpx;
			}
		}
	}
</style>