<template>
	<view class="approver">
		<u-navbar title="选择审批人" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="filterBox">
			<u-search placeholder="搜索审批人姓名" :showAction="false" v-model="keyword" @search="queryList"></u-search>
		</view>
		<view class="cost_box">
			<view class="cost_box_content">
				<u-radio-group size='14' v-model="radiosj" placement="column">
					<view class="radio_box " v-for="(item,index) in dataList" :key="index">
						<view class="u-flex u-row-between">
							<u-radio :name="item" :label="item.fullName">
							</u-radio>
							<!-- <view class="radio_text">
								<u--text align="right" :text="item.mobile | formatPhone"></u--text>
							</view> -->
						</view>
						<view class="u-flex radio_box_down" v-if="item.jobs">
							<text class="u-tags">{{item.jobs}}</text>
							<!-- <u-tag class="stag" :text="`${item.jobs}`" plain> </u-tag> -->
						</view>
					</view>
				</u-radio-group>
			</view>
		</view>

		<u-empty text="请联系管理添加功能权限!" icon="http://cdn.uviewui.com/uview/empty/order.png" style='margin-top: 60rpx;'
			v-if="dataList.length==0">
		</u-empty>

		<view class="footer_box">
			<u-button type="primary" color="#346CF2" text="确认" @click="submit()"></u-button>
		</view>


	</view>
</template>

<script>
	import {
		regulationinfo
	} from '@/config/api.js';
	export default {
		data() {
			return {
				pageParams: {},
				dataList: [],
				radiosj: {},
				keyword: ''
			};
		},
		onLoad(option) {
			this.pageParams = option
			this.queryList()
		},
		methods: {
			queryList() {
				regulationinfo({
					params: {
						reguid: this.pageParams.regulationId,
						psgUserId: this.pageParams.psgUserIdr,
						empName: this.keyword
					}
				}).then((data) => {
					data.processNodeInfoList[0].empList.forEach(v => {
						v.sort = data.processNodeInfoList[0].sort
					})
					this.dataList = data.processNodeInfoList[0].empList
					this.dataList[0].sort = data.processNodeInfoList[0].sort
					this.radiosj = {}
				})
			},
			submit() {
				if (JSON.stringify(this.radiosj) == '{}') return uni.$u.toast('请先选择审批人')
				let pages = getCurrentPages(); //获取跳转的所有页面
				let nowPage = pages[pages.length - 1]; //当前页
				let prevPage = pages[pages.length - 2]; //上一页

				if (this.pageParams.type) {
					this.radiosj.mobile = this.formatPhone(this.radiosj.mobile)
					prevPage.$vm.approverVal(this.radiosj)
				} else {
					prevPage.$vm.checkUserId = this.radiosj.userId
					prevPage.$vm.checkUserName = this.radiosj.fullName
					prevPage.$vm.checkUserPhone = this.formatPhone(this.radiosj.mobile)
					prevPage.$vm.sortr = this.radiosj.sort
					prevPage.$vm.psgUserIdr = this.radiosj.id
				}
				uni.navigateBack({
					delta: 1
				});
			},
			formatPhone(value) {
				if (value) {
					return value.substring(0, 3) + '****' + value.substring(value.length - 4);
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.approver {
		.filterBox {
			padding: 27rpx 32rpx;
		}

		.cost_box {
			padding: 0 32rpx;
			background-color: #fff;
			width: calc(100% - 64rpx);

			.cost_box_tit {
				font-size: 32rpx;
				margin-top: 30rpx;
			}

			.cost_box_content {
				.radio_box {
					margin-top: 30rpx;
					margin-bottom: 30rpx;

					/deep/.u-radio__text {
						margin-left: 30rpx;
						font-size: 28rpx;
					}

					.radio_box_down {
						// padding-left: 70rpx;
						margin-top: 20rpx;

						.stag {
							margin-right: 18rpx;
						}
					}

				}
			}
		}
	}


	.u-tags {
		border: 1rpx solid #3c9cff;
		border-radius: 10rpx;
		padding: 10rpx;
		color: #3c9cff;
		font-size: 26rpx;
	}

	/deep/.u-tag-wrapper {}
</style>