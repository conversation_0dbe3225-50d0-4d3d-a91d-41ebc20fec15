<template>
	<view>
		<u-navbar title="计费规则" :autoBack="true" :placeholder="true" />
		<view class="u-center u-flex-direction" style="height: 190rpx;margin: 30rpx 0;">
			<u--image :showLoading="true" :src="'https://zqcx.di-digo.com/app/image/blii_car.png'" height="80px"
				@click="click"></u--image>
			<view class="">
				{{dataWash(rules.carTypeFullName)}}
			</view>

		</view>
		<view class="u-center u-flex-direction">
			<view class="top"><span style="margin-left: 30rpx;">基础费用</span></view>
			<view class="table">
				<view class="tr">
					<view class="td">起步价</view>
					<view class="td" style="width: 20%;"> </view>
					<view class="td" style="width: 40%;"><span
							v-if="rules.startingFee">{{dataWash(rules.startingFee)}}元</span></view>
				</view>
				<view class="tr">
					<view class="td" style="text-align: right;">包含里程</view>
					<view class="td" style="width: 20%;">
						<span v-if="rules.containMileage">{{dataWash(rules.containMileage)}}公里</span>
					</view>
					<view class="td"></view>
				</view>
				<view class="tr">
					<view class="td" style="text-align: right;">包含时长</view>
					<view class="td" style="width: 20%;">
						<span v-if="rules.containTime">{{dataWash(rules.containTime)}}分钟</span>
					</view>
					<view class="td"></view>
				</view>
				<view class="tr">
					<view class="td">超里程</view>
					<view class="td"></view>
					<view class="td"><span v-if="rules.overtakeMileage">{{dataWash(rules.overtakeMileage)}}元/公里</span>
					</view>
				</view>
				<view class="tr">
					<view class="td">超时长</view>
					<view class="td"> </view>
					<view class="td"><span v-if="rules.overtakeTime">{{dataWash(rules.overtakeTime)}}元/分钟</span></view>
				</view>
			</view>
		</view>

		<view class="u-center u-flex-direction" style="margin-top: 20rpx;">
			<view class="top "><span style="margin-left: 30rpx;">基础费用</span></view>
			<view class="top cance">
				<view style="margin:0 30rpx;">
					高速费、停车费、其他费用，按行程过程司机实际执付的费用收取。
				</view>
			</view>
		</view>

		<view class="u-center u-flex-direction" style="margin-top: 20rpx;">
			<view class="top"><span style="margin-left: 30rpx;">取消费说明</span></view>
			<view class="top cance">
				<view style="margin:0 30rpx;">
					【有责取消】
					以下单预约用车出发时间为准,当乘客取消订单时，符合以下条件，将产生取消费用(预估费*费用收取比例值-取消费);
				</view>
			</view>
			<view class="table" style="margin: 20rpx 0;">
				<view class="tr">
					<view class="th">距离下单预约用车出发时间(含)</view>
					<view class="th">费用收取比例</view>
				</view>
				<view class="tr" v-for="item in rules.cancelFees" :key="item.key">
					<view class="td">{{dataWash(item.key)}}分钟</view>
					<view class="td">{{dataWash(item.value)}}%</view>
				</view>
			</view>
			<view class="top cance">
				<view style="margin:0 30rpx;">
					【无责取消】
					当到达下单预约用车出发时间，司机还未点击【开始出发】，乘客可无责取消订单;
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				rules: {
					startingFee: '',
					containMileage: '',
					containTime: '',
					overtakeMileage: '',
					overtakeTime: '',
					cancelFees: []
				}
			}
		},
		onLoad(option) {
			if (option.rules && option.rules != 'null') {
				this.rules = JSON.parse(option.rules)
				if (this.rules.cancelFee && option.rules != '[]') {
					this.rules.cancelFees = JSON.parse(this.rules.cancelFee)
				}
				
				console.log(this.rules,'--');
			}
		},
		methods: {
			dataWash(v) {
				return v ? v : ''
			}
		}
	}
</script>

<style scoped>
	.top {
		width: 94%;
		border: 1px solid #EDF0F4;
		border-bottom: 0;
		border-collapse: collapse;
		color: #000;
		padding: 20rpx 0;
		font-weight: bolder;
		background: #FFFFFF;
		font-size: 26rpx;
	}

	.cance {
		border-bottom: 1px solid #EDF0F4;
		font-weight: 100;
	}

	.table {
		width: 94%;
		border-radius: 8rpx;
		display: table;
		border: 1px solid #EDF0F4;
		border-collapse: collapse;
		font-size: 26rpx;
	}

	.th {
		text-align: center;
		color: #000;
		padding: 20rpx 0;
		font-weight: bolder;
		display: table-cell;
		border: 1px solid #EDF0F4;
		background: #CECECE;
	}

	.td {
		text-align: center;
		background: #FFFFFF;
		padding: 20rpx 0;
		display: table-cell;
		border: 1px solid #EDF0F4
	}

	.tr {
		display: table-row;
	}
</style>