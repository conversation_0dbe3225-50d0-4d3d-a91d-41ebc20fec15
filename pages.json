{
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/index/index",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/trip/trip",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/message/message",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/my/my",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		},
		{
			"path": "pages/wayPassenger/my/setting/setting",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}

		, {
			"path": "pages/wayPassenger/my/myCar/myCar",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/my/myCar/addMyCar/addMyCar",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/workBench/apply/apply",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/workBench/apply/detail/detail",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/workBench/approve/approve",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/workBench/approve/detail/detail",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/index/entp/entp",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/index/choiceRide/choiceRide",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/index/system/system",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/index/cost/cost",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/map/map",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/map/driverMap",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/map/mapnotit",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/index/remarks/remarks",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/index/peer/peer",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}

		, {
			"path": "pages/wayPassenger/index/systemDetail/systemDetail",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/index/approver/approver",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/login/selectRole/selectRole",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}

		, {
			"path": "pages/map/mapSi",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/my/feedBack/feedBack",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/workBench/newWorkBench",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wayPassenger/index/customCar/customCar",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}

		, {
			"path": "pages/map/mapNet",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}
	],

	"subPackages": [{
		"root": "pager",
		"pages": [{
				"path": "vehicleService/vehicleService",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "carAdmini/carAdmini",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "dispatch/dispatch",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "dispatch/newDispatch/newDispatch",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "orderDetail/orderDetail",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "result/result",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "dispatchDetail/dispatchDetail",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "orderMenu/orderMenu",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "order/order",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "myPersonal/myPersonal",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "myAvatar/myAvatar",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "mayChangePass/mayChangePass",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "vehicleEscala/vehicleEscala",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "vehicleEscalaDetail/vehicleEscalaDetail",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "dragListr/dragListr",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "editTrip/editTrip",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "networkCarDetais/networkCarDetais",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "networkCar/networkCar",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "editMileage/editMileage",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}

			, {
				"path": "frequently/frequently",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "billingRules/billingRules",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}
		]
	}, {
		"root": "pagec",
		"pages": [{
				"path": "choiceCar/choiceCar",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "choiceDriver/choiceDriver",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "choiceFleet/choiceFleet",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "newSublease/newSublease",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}

			, {
				"path": "choiceCompany/choiceCompany",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}


			, {
				"path": "middleware/middleware",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}

			}

		]
	}, {
		"root": "pageDriver",
		"pages": [{
			"path": "driveIndex/driveIndex",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "staging/staging",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "dispatching/dispatching",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "carReport/carReport",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "driverOdeder/driverOdeder",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}]
	}],
	"preloadRule": {
		"pages/login/login": {
			"network": "all",
			"packages": ["pager"]
		}
	},
	"globalStyle": {
		"navigationStyle": "custom",
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#F9FAFE"
	},
	"tabBar": {
		"color": "#626468",
		"selectedColor": "#0048ff",
		"borderStyle": "white",
		"backgroundColor": "#ffffff",
		"list": [{
				"pagePath": "pages/wayPassenger/index/index",
				"iconPath": "static/home.png",
				"selectedIconPath": "static/home-select.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/wayPassenger/trip/trip",
				"iconPath": "static/trip.png",
				"selectedIconPath": "static/trip-select.png",
				"text": "行程"
			},
			{
				"pagePath": "pages/wayPassenger/workBench/newWorkBench",
				"iconPath": "static/workBench.png",
				"selectedIconPath": "static/workBench-select.png",
				"text": "工作台"
			},
			{
				"pagePath": "pages/wayPassenger/message/message",
				"iconPath": "static/message.png",
				"selectedIconPath": "static/message-select.png",
				"text": "消息"
			},
			{
				"pagePath": "pages/wayPassenger/my/my",
				"iconPath": "static/my.png",
				"selectedIconPath": "static/my-select.png",
				"text": "我的"
			}
		]
	},
	"uniIdRouter": {}
}