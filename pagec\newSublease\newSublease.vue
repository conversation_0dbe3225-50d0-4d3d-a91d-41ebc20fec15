<template>
	<view>
		<u-navbar title="转租赁" :autoBack="true" :placeholder="true">
		</u-navbar>
		<view class="btm">
			<view v-for="(item,index) in subleaList" :key="index" class="sub_list">
				<view class="u-center ent_wdsq">
					<view class="u-center " style="flex: 1;margin-bottom: 10rpx;" @click="openClick(index,1,)">
						<view class="ent_icon">
							<u-image :width="22" :height="22" :lazyLoad="true"
								src="https://zqcx.di-digo.com/app/image/gongsi.png">
							</u-image>
						</view>
						<u--input placeholder="请选择租赁公司" v-model="item.coopeMerName" readonly></u--input>
					</view>
					<view class="ent_btn">
						<u-button type="primary" color="#b9b9b9" size='mini' text="删除单位" @click="coopeDelClick(index)"
							v-if="index!=0">
						</u-button>
					</view>
				</view>
				<view class="u-center ent_box contenr" v-for="(itemr,i) in item.childItem" :key="i">
					<view class="ent_left">
						<view class="u-center ent_box" @click="openClick(index,2,i)">
							<view class="ent_icon">
								<u-image :width="18" :height="18"
									src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon.png">
								</u-image>
							</view>
							<u--input placeholder="请选择用车时间" v-model="itemr.reserveStartTime" readonly></u--input>
						</view>

						<view class="u-center ent_box" @click="openClick(index,3,i)">
							<view class="ent_icon">
								<u-image :width="17" :height="17" :lazyLoad="true"
									src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png">
								</u-image>
							</view>
							<u--input placeholder="请选择车型" v-model="itemr.carTypeName" readonly></u--input>
						</view>

						<view class="u-center ent_box" @click="openClick(index,4,i,item,itemr)">
							<view class="ent_icon">
								<u-image :width="20" :height="20" :lazyLoad="true"
									src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon2.png">
								</u-image>
							</view>
							<u--input placeholder="请选择计费套餐" v-model="itemr.valuationName" readonly></u--input>
						</view>
						<view class="u-center ent_box">
							<view class="ent_icon">
								<u-image :width="20" :height="20" :lazyLoad="true"
									src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon4.png">
								</u-image>
							</view>
							<u--input placeholder="请输入乘车人数" v-model="itemr.psgNums" type="number" maxlength='99'
								@input="TypeInput($event,itemr)">
								<span slot="suffix">人</span>
							</u--input>
						</view>
					</view>
					<view class="ent_btn">
						<u-button type="primary" text="添加" size='mini' @click='addClick(item)' v-if="i==0"></u-button>
						<u-button type="primary" color="#b9b9b9" size='mini' text="删除" @click="delClick(item,i)" v-else>
						</u-button>
					</view>
				</view>

				<view class="remak">
					<u--textarea v-model="item.remark" placeholder="请输入用车备注,让租赁公司更清楚用车需求(非必填)" :height='50'
						confirmType='done'></u--textarea>
				</view>
			</view>
		</view>
		<view class="footer_box">
			<u-button type="primary" color="#346CF2" text="确定" @click="preseClick"></u-button>
		</view>

		<view class="add_Car u-center" @click="addBtn('add')">
			<text class="add_Car_text">添加单位</text>
		</view>

		<!-- 用车时间  -->
		<u-datetime-picker :show="timeShowr" v-model="timeMode" mode="datetime" :formatter="formatter"
			:closeOnClickOverlay='true' @confirm='tiemConfirms' @cancel="timeShowr=false" @close="timeShowr=false">
		</u-datetime-picker>

		<!-- 车型弹窗 -->
		<u-picker :show="carShowr" ref="uPicker" keyName="carTypeName" :columns="carTypecolumns"
			@cancel="carShowr=false" @confirm="carConfirmr" @change="carTypechange"></u-picker>

		<!-- 包车套餐弹窗-->
		<u-picker :show="packageShow" keyName="valuationFullName" :columns="packaColumnsr" @cancel="packageShow=false"
			@confirm="packaConfirmr"></u-picker>

	</view>
</template>

<script>
	import {
		cartypetreelist,
		tempvaluationlist,
		dispatchtolease
	} from '@/config/api.js';
	export default {
		data() {
			return {
				initialr: {},
				subleaList: [{
					childItem: [{
						carTypeId: '',
						psgNums: '',
						valuationId: '',
						valuationName: "",
						reserveStartTime: ''
					}],
					coopeMerName: '',
					coopeMerId: '',
					remark: '',
				}],
				timeMode: Number(new Date()),
				timeShowr: false,
				idxr: null,
				waiIdxr: null,
				carShowr: false,
				carTypecolumns: [],
				cartypetree: [],
				packageShow: false,
				packaColumnsr: []
			}
		},
		onLoad(option) {
			option.reserveStartTime = uni.$u.timeFormat(option.reserveStartTime, 'yyyy-mm-dd hh:MM:ss')
			this.initialr = option
			this.subleaList[0].childItem[0].reserveStartTime = this.initialr.reserveStartTime
			this.getInit()
		},
		methods: {
			TypeInput(e, val) {
				// #ifdef MP-WEIXIN
				const inputType = /[^\d]/g
				this.$nextTick(() => {
					val.psgNums = Number(e.replace(inputType, '')) == 0 ? '' : Number(e.replace(inputType, ''))
				})
				// #endif
			},
			preseClick() {
				dispatchtolease({
					id: this.initialr.travelId,
					dispatchToLeaseVo: {
						orderItem: this.subleaList
					}
				}).then(v => {
					uni.$u.toast('操作成功')
					setTimeout(() => {
						uni.redirectTo({
							url: '/pager/dispatch/dispatch?typer=1',
						})
					}, 300)

					// uni.navigateBack({
					// 	delta: 1
					// })
				})
			},
			openClick(idx, t, index, item, itemr) {

				this.$set(this, 'waiIdxr', idx)
				this.$set(this, 'idxr', index)
				// this.waiIdxr = idx
				// this.idxr = index
				if (t == 1) {
					uni.$u.route('/pagec/choiceCompany/choiceCompany', {
						index: idx
					});
				} else if (t == 2) {
					this.timeShowr = true
				} else if (t == 3) {
					this.carShowr = true
				} else if (t == 4) {
					if (!item.coopeMerId && !itemr.carTypeId) return uni.$u.toast('请先选择租赁单位和车型')
					this.getTempval(item, itemr)
				}
			},
			coopeDelClick(i) {
				this.subleaList.splice(i, 1)
			},
			delClick(item, i) {
				item.childItem.splice(i, 1)
			},
			addClick(item) {
				item.childItem.push({
					carTypeId: '',
					psgNums: '',
					valuationId: '',
					reserveStartTime: this.initialr.reserveStartTime
				})
			},
			addBtn() {
				this.subleaList.push({
					childItem: [{
						carTypeId: '',
						psgNums: '',
						valuationId: '',
						reserveStartTime: this.initialr.reserveStartTime
					}],
					coopeMerName: '',
					coopeMerId: '',
					remark: '',
				})
			},
			getInit() {
				cartypetreelist({
					params: {}
				}).then((data) => {
					this.cartypetree = data
					this.carTypecolumns = [data, data[0].children]
				})
			},
			// 接收
			receive(objr, idx) {
				this.$set(this.subleaList[idx], 'coopeMerName', objr.supplierCompName)
				this.$set(this.subleaList[idx], 'coopeMerId', objr.supplierMerId)
				this.$set(this.subleaList[idx], 'feeTemplateId', objr.feeTemplateId)
			},
			tiemConfirms(e) {
				let valr = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
				this.$set(this.subleaList[this.waiIdxr].childItem[this.idxr], 'reserveStartTime', valr)
				this.timeShowr = false
				this.emptyBtn()
			},
			carConfirmr(e) {
				let name = e.value[0].carTypeName + '/' + e.value[1].carTypeName,
					id = e.value[1].carTypeId
				this.$set(this.subleaList[this.waiIdxr].childItem[this.idxr], 'carTypeId', id)
				this.$set(this.subleaList[this.waiIdxr].childItem[this.idxr], 'carTypeName', name)
				this.carShowr = false
				this.emptyBtn()
			},
			packaConfirmr(e) {
				let id = e.value[0].valuationId,
					name = e.value[0].valuationFullName
				console.log(e.value[0]);
				console.log(this.waiIdxr, 'this.waiIdxr')
				console.log(this.idxr, 'this.idxr')
				this.$set(this.subleaList[this.waiIdxr].childItem[this.idxr], 'valuationId', id)
				this.$set(this.subleaList[this.waiIdxr].childItem[this.idxr], 'valuationName', name)
				this.packageShow = false
				this.emptyBtn()
			},
			emptyBtn() {
				this.waiIdxr = null
				this.idxr = null
			},
			// 获取包车套餐
			getTempval(item, itemr) {
				tempvaluationlist({
					params: {
						feeTemplateId: item.feeTemplateId,
						carTypeId: itemr.carTypeId,
					}
				}).then((data) => {
					if (data.length == 0) return uni.$u.toast('暂无计费套餐,请添加')
					this.packaColumnsr = [data]
					this.packageShow = true
				})
			},
			// 时间格式化
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				if (type === 'hour') {
					return `${value}时`
				}
				if (type === 'minute') {
					return `${value}分`
				}
				return value
			},
			// 选择车型后缀
			async carTypechange(e) {
				let that = this
				that.$nextTick(() => {
					const {
						columnIndex,
						value,
						values, // values为当前变化列的数组内容
						index,
						// 微信小程序无法将picker实例传出来，只能通过ref操作
						picker = that.$refs.uPicker
					} = e
					// 当第一列值发生变化时，变化第二列(后一列)对应的选项
					if (columnIndex === 0) {
						// picker为选择器this实例，变化第二列对应的选项
						if (value[0].children && value[0].children.length > 0) {
							picker.setColumnValues(1, that.cartypetree[index].children)
						} else {
							picker.setColumnValues(1, [])
						}
					}
				})

			},
		}
	}
</script>

<style lang="scss" scoped>
	.sub_list {
		background: #fff;
		margin-top: 20rpx;
		padding: 40rpx;


		.contenr {
			background: #f9fafe;
			padding: 20rpx 40rpx;
			border-radius: 20rpx;

			.ent_left {
				width: 100%;
			}
		}

		.ent_box {
			margin-bottom: 20rpx;
		}

		.ent_icon {
			margin-right: 20rpx;
		}

		.ent_btn {
			margin-left: 20rpx;
		}
	}

	.ent_wdsq {
		width: 100%;
	}

	.btm {
		padding-bottom: 80rpx;
	}

	.add_Car {
		z-index: 99;
		position: fixed;
		bottom: 200rpx;
		right: 50rpx;
		background: #06AFFF;
		border-radius: 50%;
		width: 80rpx;
		height: 80rpx;
		padding: 10rpx;
		color: #fff;
		box-shadow: 2px 2px 5px 2px #06AFFFc2;
	}

	.add_Car_text {
		font-size: 30rpx !important;
		text-align: center;
	}

	.add_Car:hover {
		width: 85rpx;
		height: 85rpx;
	}
</style>