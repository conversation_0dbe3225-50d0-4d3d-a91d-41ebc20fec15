<template>
	<view class="myCar">
		<u-navbar title="我的车辆" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="car_binding" v-if="adoptList.length!=0">已绑定 <text class="car_zi">{{adoptList.length}}</text> 辆
		</view>
		<u-swipe-action>
			<u-swipe-action-item :options="options" v-for="(item,index) in adoptList" :key="index"
				@click="(event)=>actionBtn(event,item)">
				<view class="u-border-bottom swipe_text u-flex-direction" @click="goBtn(item)">
					<view>{{ `${item.carNumber}`}} <text v-if="item.numberColor">({{item.numberColor}})</text></view>
					<text class="text_car">{{ `${item.carBrandName} ${item.brandModelName} ${item.carColor}`}}</text>
				</view>
			</u-swipe-action-item>
		</u-swipe-action>
		<view class="car_binding" v-if="waitList.length!=0">审核中 <text class="car_zi">{{waitList.length}}</text> 辆</view>
		<u-swipe-action>
			<u-swipe-action-item :options="optionsrs" v-for="(item,index) in waitList" :key="index"
				@click="(event)=>withdrawBtn(event,item)">
				<view class="u-border-bottom swipe_text u-flex-direction" @click="goBtn(item)">
					<view>{{ `${item.carNumber}`}} <text v-if="item.numberColor">({{item.numberColor}})</text></view>
					<text class="text_car">{{ `${item.carBrandName} ${item.brandModelName} ${item.carColor}`}}</text>
				</view>
			</u-swipe-action-item>
		</u-swipe-action>
		<view class="car_binding" v-if="rejectList.length!=0">已驳回 <text class="car_zi">{{rejectList.length}}</text> 辆
		</view>
		<u-swipe-action>
			<u-swipe-action-item :options="optionsr" :disabled='true' v-for="(item,index) in rejectList" :key="index"
				@click="(event)=>actionBtn(event,item)">
				<view class="u-border-bottom swipe_text u-flex-direction" @click="goBtn(item)">
					<view>{{ `${item.carNumber}`}} <text v-if="item.numberColor">({{item.numberColor}})</text></view>
					<text class="text_car">{{ `${item.carBrandName} ${item.brandModelName} ${item.carColor}`}}</text>
				</view>
			</u-swipe-action-item>
		</u-swipe-action>

		<u-empty text="列表为空" icon="http://cdn.uviewui.com/uview/empty/order.png"
			v-if="adoptList.length==0 && waitList.length==0 && rejectList.length==0 ">
		</u-empty>
		<view class="add_Car u-center" @click="addBtn('add')">
			<u-icon name="plus" color="#FFF" size='30'></u-icon>
		</view>
	</view>
</template>

<script>
	import {
		carGetcarbyuserList,
		carDeletecar,
		carPostUndocar
	} from '@/config/consoler.js';
	export default {
		data() {
			return {
				optionsrs: [{
					text: '撤销',
					style: {
						backgroundColor: '#ffaa00',
						width: "90rpx",

					}
				}],
				optionsr: [{
					text: '编辑',
					style: {
						backgroundColor: '#ffaa00',
						width: "90rpx",

					}
				}],
				options: [{
					text: '编辑',
					style: {
						backgroundColor: '#ffaa00',
						width: "90rpx",

					}
				}, {
					text: '删除',
					style: {
						backgroundColor: '#ff0000',
						width: "90rpx",
					}
				}],

				adoptList: [],
				waitList: [],
				rejectList: [],

			};
		},
		onShow() {
			this.getList()
		},
		mounted() {
			// this.getList()
		},
		methods: {
			withdrawBtn(e, item) {
				console.log(item, 'item');
				let that = this
				uni.showModal({
					title: '提示',
					content: `确定撤销提交车辆【${item.carNumber}】？`,
					success: function(res) {
						if (res.confirm) {
							carPostUndocar({
								id: item.carId
							}).then(v => {
								uni.$u.toast(v)
								that.getList()
							})
						}
					}
				});
			},
			actionBtn(e, item) {
				let that = this
				if (e.index == 1) {
					uni.showModal({
						title: '提示',
						content: `确定删除已绑车辆【${item.carNumber}】？`,
						success: function(res) {
							if (res.confirm) {
								carDeletecar({
									id: item.carId
								}).then(v => {
									uni.$u.toast(v)
									that.getList()
								})
							}
						}
					});
				} else {
					uni.$u.route('/pages/wayPassenger/my/myCar/addMyCar/addMyCar', {
						type: 'edit',
						id: item.carId
					});
				}
				console.log(e, 'eeee');
				console.log(item, 'item');
			},
			addBtn(type) {
				uni.$u.route('/pages/wayPassenger/my/myCar/addMyCar/addMyCar', {
					type: type
				});
			},
			goBtn(item) {
				uni.$u.route('/pages/wayPassenger/my/myCar/addMyCar/addMyCar', {
					type: 'details',
					id: item.carId
				});
			},
			getList() {
				carGetcarbyuserList().then(v => {
					this.waitList = v.filter(vr => {
						return vr.selfCarState == 10
					})
					this.adoptList = v.filter(vr => {
						return vr.selfCarState == 20
					})
					this.rejectList = v.filter(vr => {
						return vr.selfCarState == 30
					})

					console.log(this.rejectList, 'this.rejectList ');

				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.myCar {
		.car_binding {
			font-size: 32rpx;
			margin: 20rpx 40rpx;

			.car_zi {
				color: #06AFFF;
				margin: 0 10rpx;
			}
		}

		.add_Car {
			z-index: 99;
			position: absolute;
			bottom: 200rpx;
			right: 50rpx;
			background: #06AFFF;
			border-radius: 50%;
			width: 80rpx;
			height: 80rpx;
			padding: 10rpx;
			color: #fff;
			box-shadow: 2px 2px 5px 2px #06AFFFc2;
		}

		.add_Car:hover {
			width: 85rpx;
			height: 85rpx;
		}

		.swipe_text {
			display: flex;
			padding: 20rpx 40rpx;

			.text_car {
				color: #999;
				font-size: 24rpx;
				margin-top: 10rpx;
			}
		}

	}
</style>