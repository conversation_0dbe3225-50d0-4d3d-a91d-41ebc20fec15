<template>
	<view>
		<u-navbar title="记录详情" :autoBack="true" :placeholder="true">
		</u-navbar>

		<u-cell-group class="cellBody">
			<!-- <u-cell title="添加时间 :" :value="`${$u.timeFormat(form.oilTime, 'yyyy年mm月dd日')}`"></u-cell> -->
			<u-cell title="车牌号 :" :value='form.carNumber'></u-cell>
			<u-cell title="车牌型号 :" :value='form.brandModelName'></u-cell>
		</u-cell-group>
		<!-- 加油 -->
		<block v-if="tr==1">
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="加油时间 :" :value="`${$u.timeFormat(new Date(form.oilTime), 'yyyy年mm月dd日')}`"></u-cell>
				<u-cell title="加油费用 :" :value='`${Number(form.oilCost).toFixed(2)} 元`'></u-cell>
				<u-cell title="燃油标号 :" :value='`${form.oilLevel} 号`'></u-cell>
				<u-cell title="燃油单价 :" :value='`${Number(form.oilUnitPrice).toFixed(2)} 元`'></u-cell>
				<u-cell title="加油量 :" :value='form.oilNumber'></u-cell>
				<!-- <u-cell title="加油地点 :" :value='form.oilAddress'></u-cell> -->
			</u-cell-group>
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="支付方式 :" :value='dataWash(oilPayWayList,form.oilPayWay)'></u-cell>
				<u-cell title="油卡 :" :value='carWash(form.oilCardId)' v-if="form.oilPayWay==2"></u-cell>
			</u-cell-group>
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="里程数 :" :value='`${form.dashboardMileage| null} 公里`'></u-cell>
				<u-cell title="经办人 :" :value='form.operatorName'></u-cell>
			</u-cell-group>
		</block>
		<!-- 维修\保养 -->
		<block v-if="tr==2 ||tr==3">
			<u-cell-group class="cellBody" :border="false">
				<u-cell :title="`${tr==2?'送修':'送保'}日期 :`"
					:value="`${$u.timeFormat(new Date(form.maintainTime), 'yyyy年mm月dd日')}`">
				</u-cell>
				<u-cell :title="`${tr==2?'送修':'送保'}里程 :`" :value='`${form.maintainMileage} 公里`'></u-cell>
				<u-cell title="提车日期 :" :value="`${$u.timeFormat(new Date(form.takeCarTime), 'yyyy年mm月dd日')}`"></u-cell>
				<u-cell title="提车里程 :" :value='`${form.dashboardMileage} 公里`'></u-cell>
			</u-cell-group>
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="维修商 :" :value='form.maintainComp'></u-cell>
				<u-cell title="维修单号 :" :value='form.maintainCode'></u-cell>
				<u-cell :title="`${tr==2?'维修':'保养'}费用 :`" :value='`${ Number(form.maintainPayCost).toFixed(2)} 元`'>
				</u-cell>
			</u-cell-group>
			<u-cell-group class="cellBody" :border="false" v-if="form.maintainDetail.length>0">
				<u-collapse :value="form.maintainDetail.map((e,idx)=>{return idx})">
					<u-collapse-item :title="`${tr==2?'维修':'保养'}明细 :`">
						<text slot="value" class="">{{form.moneyr}}</text>
						<u-cell-group class="cellBody" v-for="(item,index) in form.maintainDetail" :key="index">
							<u-cell :title="`项目 ${index+1}`" :value='`${ Number(item.itemCost).toFixed(2)} 元`'>
							</u-cell>
							<u-cell :title="`${tr==2?'维修':'保养'}项目 :`" :value='item.maintainItem' :border="false">
							</u-cell>
							<u-cell title="材料费 :" :value='`${ Number(item.materialCost).toFixed(2)} 元`' :border="false">
							</u-cell>
							<u-cell title="工时费 :" :value='`${ Number(item.timeCost).toFixed(2)} 元`' :border="false">
							</u-cell>
							<u-cell title="其他费用 :" :value='`${ Number(item.otherCost).toFixed(2)} 元`' :border="false">
							</u-cell>
						</u-cell-group>
					</u-collapse-item>
				</u-collapse>
			</u-cell-group>
		</block>
		<!-- 保险 -->
		<block v-if="tr==4">
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="投保日期 :" :value="`${$u.timeFormat(new Date(form.insuranceDate), 'yyyy年mm月dd日')}`">
				</u-cell>
				<u-cell title="投保单位 :" :value='form.insuranceCompany'></u-cell>
				<u-cell title="生效日期 :" :value="`${$u.timeFormat(new Date(form.startDate), 'yyyy年mm月dd日')}`"></u-cell>
				<u-cell title="到期日期 :" :value="`${$u.timeFormat(new Date(form.endDate), 'yyyy年mm月dd日')}`"></u-cell>
			</u-cell-group>
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="保单费用 :" :value='`${Number(form.insuranceCost).toFixed(2)} 元`'></u-cell>
			</u-cell-group>
			<u-cell-group class="cellBody " :border="false" v-if="form.insuranceDetail.length>0">
				<u-collapse :value="form.insuranceDetail.map((e,idx)=>{return idx})">
					<u-collapse-item title="保险明细 :">
						<text slot="value" class="">{{form.moneyr}}</text>
						<u-cell-group class="cellBody" v-for="(item,index) in form.insuranceDetail" :key="index">
							<u-cell :title="`项目 ${index+1}`" :value='`${Number(item.itemCost).toFixed(2)} 元`'></u-cell>
							<u-cell title="保险项目 :" :value="item.insuranceItem" :border="false"></u-cell>
							<u-cell title="金额 :" :value='`${Number(item.itemCost).toFixed(2)} 元`' :border="false">
							</u-cell>
						</u-cell-group>
					</u-collapse-item>
				</u-collapse>
			</u-cell-group>
		</block>
		<!-- 年检 -->
		<block v-if="tr==5">
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="送检日期 :" :value="`${$u.timeFormat(new Date(form.inspectionDate), 'yyyy年mm月dd日')}`">
				</u-cell>
				<u-cell title="年检站点 :" :value='form.inspectionStation'></u-cell>
				<!-- <u-cell title="年检地址 :" :value='form.inspectionAddress'></u-cell> -->
				<u-cell title="下次年检日期 :" :value="`${$u.timeFormat(new Date(form.nextInspectionDate), 'yyyy年mm月dd日')}`">
				</u-cell>
			</u-cell-group>
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="年检费用 :" :value='`${Number(form.inspectionCost).toFixed(2)} 元`'></u-cell>
			</u-cell-group>
		</block>
		<!-- 违章 -->
		<block v-if="tr==6">
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="违章日期 :" :value="`${$u.timeFormat(new Date(form.violationDate), 'yyyy年mm月dd日')}`">
				</u-cell>
				<u-cell title="违章时间 :" :value='form.violationTime'></u-cell>
				<u-cell title="违章地点 :" :value='form.violationAddress'></u-cell>
			</u-cell-group>
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="违章罚款 :" :value='`${form.violationCost} 元`'></u-cell>
				<u-cell title="违章扣分 :" :value='`${form.violationPoints} 分`'></u-cell>
				<u-cell title="违章状态 :" :value='form.violationPoints==1?"已处理":"未处理"'></u-cell>
			</u-cell-group>
		</block>
		<!-- 其他 -->
		<block v-if="tr==7">
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="项目类型 :" :value='dataWash(otherList,form.otherTrafficType)'></u-cell>
				<u-cell title="办理时间 :" :value="`${$u.timeFormat(new Date(form.otherTrafficTime), 'yyyy年mm月dd日')}`">
				</u-cell>
				<u-cell title="经办人 :" :value='form.operatorName'></u-cell>
			</u-cell-group>
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="项目费用 :" :value='`${Number(form.otherTrafficCost).toFixed(2)} 元`'></u-cell>
			</u-cell-group>
		</block>
		<!-- 充电 -->
		<block v-if="tr==8">
			<u-cell-group class="cellBody" :border="false">
				<u-cell title="充电时间 :" :value="`${$u.timeFormat(new Date(form.oilTime), 'yyyy年mm月dd日')}`"></u-cell>
				<u-cell title="充电类型 :" :value='`${Number(form.oilCost).toFixed(2)} 元`'></u-cell>
				<u-cell title="订单费用 :" :value='`${form.oilLevel} 号`'></u-cell>
				<u-cell title="充电度数 :" :value='`${Number(form.oilUnitPrice).toFixed(2)} 元`'></u-cell>
				<u-cell title="充电时长 :" :value='form.oilNumber'></u-cell>
				<u-cell title="电费单位 :" :value='form.oilAddress'></u-cell>
				<u-cell title="充电地点 :" :value='`${Number(form.oilUnitPrice).toFixed(2)} 元`'></u-cell>
			</u-cell-group>

			<u-cell-group class="cellBody" :border="false">
				<u-cell title="支付方式 :" :value='dataWash(oilPayWayList,form.oilPayWay)'></u-cell>
				<u-cell title="电卡 :" :value='carWash(form.oilCardId)' v-if="form.oilPayWay==2"></u-cell>
			</u-cell-group>

			<u-cell-group class="cellBody" :border="false">
				<u-cell title="里程数 :" :value='`${form.dashboardMileage| null} 公里`'></u-cell>
				<u-cell title="经办人 :" :value='form.operatorName'></u-cell>
			</u-cell-group>
		</block>

		<u-cell-group class="cellBody" :border="false">
			<u-cell title="备注说明 :">
				<span slot='value' style="width: 70%;word-break: break-word;text-align: end;"> {{form.remark}}</span>
			</u-cell>
		</u-cell-group>
		<u-cell-group class="cellBody " :border="false" style="padding: 20rpx;" v-if="form.imgUrlList">
			<view class="imgBox u-flex-wrap">
				<u--image :showLoading="true" :src="item.imgUrl" width="150rpx" height="150rpx"
					@tap='openImage(index,item.imgUrl)' v-for="(item,index) in form.imgUrlList" :key="index"
					style='margin:15rpx;'>
				</u--image>
			</view>
		</u-cell-group>

		<!-- 保养、保险、年检 -->
		<block v-if="tr==3 ||tr==4 || tr==5">
			<u-cell-group class="cellBody padding" :border="false">
				<u-cell title="是否开启保养提醒 :" :value='form.isOpenPush==1?"是":"否"'></u-cell>
				<block v-if="form.isOpenPush!=0">
					<u-cell title="保养日期 :" :value="`${$u.timeFormat(form.nextMaintainDate, 'yyyy年mm月dd日')}`"
						v-if="tr==3">
					</u-cell>
					<u-cell title="保养里程 :" :value='`${form.nextMaintainMileage} 公里`' v-if="tr==3"></u-cell>
					<u-cell title="提醒人员 :">
						<span slot="value" style="flex: 1;text-align: end">
							{{form.pushUserNames}}
						</span>
					</u-cell>
					<u-cell title="提醒时间 :" :value='`提前 ${form.Timers.date} 天 ${form.Timers.time}`'></u-cell>
				</block>
			</u-cell-group>
		</block>

	</view>
</template>

<script>
	import {
		appCardlist,
		appCartrafficinfo
	} from '@/config/consoler.js'
	export default {
		data() {
			return {
				tr: null,
				form: {
					maintainDetail: [],
					insuranceDetail: [],
					remark: ''
				},
				cardList: [], //油卡
				oilPayWayList: [],
				otherList: [],
				optionr: {}
			}
		},
		onLoad(option) {
			this.optionr = option
			this.tr = option.type
			this.$nextTick(() => {
				this.initr()
			})
		},
		methods: {
			openMap() {

			},
			initr() {
				let arrs = this.$common.getItem('dicVoList')
				arrs.forEach(item => {
					if (item.dicCode == "oil_pay_way") {
						this.oilPayWayList = item.dicValueList
					}
					if (item.dicCode == "other_traffic_type") {
						this.otherList = item.dicValueList
					}
				})
				appCartrafficinfo({
					params: {
						id: this.optionr.idr,
						carTrafficType: this.optionr.type,
					}
				}).then(res => {
					if (res.maintainDetail) {
						res.maintainDetail = JSON.parse(res.maintainDetail)
						res.moneyr = this.moneyWash(res.maintainDetail)
					}
					if (res.insuranceDetail) {
						res.insuranceDetail = JSON.parse(res.insuranceDetail)
						res.moneyr = this.moneyWash(res.insuranceDetail)
					}
					if (res.advancePushTime) {
						res.Timers = JSON.parse(res.advancePushTime)
					}
					this.form = res
				})

				appCardlist({
					params: {
						pageNum: 1,
						pageSize: 999
					}
				}).then(res => {
					this.cardList = res.pageList
				})
			},
			openImage(idx, img) {
				let urlr = this.form.imgUrlList.map(v => {
					return v.imgUrl
				})
				uni.previewImage({
					urls: urlr,
					current: idx,
				})
			},
			carWash(e) {
				this.cardList.forEach(v => {
					if (v.oilCardId == e) {
						e = v.oilCardName
					}
				})
				return e
			},
			dataWash(arrs, e) {
				console.log(arrs);
				let text = '-'
				arrs.forEach(v => {
					if (v.dicValue == e) {
						text = v.dicDescribe
					}
				})
				return text
			},
			moneyWash(arr) {
				let sum = 0
				arr.forEach(v => {
					sum += Number(v.itemCost)
				})
				return `${sum.toFixed(2)} 元 `
			}
		}
	}
</script>

<style lang="scss" scoped>
	.cellBody {
		background-color: #fff;
		// margin: 20rpx 0;
		margin-bottom: 20rpx;
	}

	.imgBox {
		display: flex;
	}

	.padding {
		padding-bottom: 40rpx;
	}
</style>