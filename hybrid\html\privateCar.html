<!DOCTYPE html>
<html lang="en" style="font-size: 0.26666666vw">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge" />
		<meta name="author" content="CSS5, css5.com.cn" />
		<title>私车公用</title>
		<!-- 样式 -->
		<link rel="stylesheet" href="css/font-awesome.min.css" />
		<link rel="stylesheet" type="text/css" href="css/privateCss.css">
		<link rel="stylesheet" type="text/css" href="css/allKinds.css">
		<link rel="stylesheet" type="text/css" href="css/frozen.css">
		<!-- 组件库 -->
		<script src="https://cdn.bootcss.com/vue/2.6.11/vue.js"></script>
		<!-- <script src="https://cdn.jsdelivr.net/npm/vue@2.7.14/dist/vue.js"></script> -->
		<!-- 网络请求 -->
		<script src="https://cdn.bootcdn.net/ajax/libs/axios/1.2.0/axios.min.js"></script>
		<script type="text/javascript" src="js/axios.js"></script>
		<!-- 地图 -->
		<script src="https://webapi.amap.com/loader.js"></script>
		<script type="text/javascript">
			window._AMapSecurityConfig = {
				securityJsCode: 'a0ec29ceac10861c863c75bcf78565a4',
			}
		</script>

	</head>

	<body>
		<div id="app">
			<div class="backbtn" @click="goBack()">
				<i class="fa fa-angle-left"></i>
			</div>
			<div id="container"></div>
			<div class="contenrs">
				<div class="cont-top">
					<div class="cont-tips"
						v-if="datar.compOrderState==50||datar.compOrderState==40||datar.compOrderState==100">
						{{textWash(datar)}}
					</div>
					<div class="cont-box ">
						<div class="ui-row-flex cont-btm borar">
							<!-- <div class="ui-col  u-flex">
								<div class="ui-avatar posIcon">
									<span style="background-image:url(http://placeholder.qiniudn.com/100x100)"></span>
								</div>
							</div> -->
							<div class="ui-col ui-col-4  u-flex-direction ">
								<div class="martb">{{datar.carNumber}} {{datar.numberColor}}</div>
								<div class="fonts cont-btm">{{datar.brandModelFullName}} {{datar.carColor}}</div>
							</div>
							<div class="ui-col u-flex-direction  u-flex">
								<div class="ui-avatar posIcon">
									<span style="background-image:url(http://placeholder.qiniudn.com/100x100)"></span>
								</div>
								<div class="fonts cont-btm">{{datar.driverName}}</div>
							</div>

						</div>
						<div class="u-flex u-row-between martb borar">
							<span class="fonts">行程单号：{{datar.travelCode}}</span>
							<span class="fonts colorb" @click="goOrderDetail(datar.travelId)">订单详情 <i
									class="fa fa-angle-double-right"></i></span>
						</div>
						<div class="cont-tiemr martb borar fonts">
							<i class="fa fa-clock-o"></i>
							{{datar.reserveStartTime | timeFormat('mm月dd日 hh:MM')}} -
							{{datar.reserveEndTime | timeFormat('mm月dd日 hh:MM')}}
						</div>
					</div>
				</div>

				<div class="u-flex cont-btm demo"
					v-if="datar.compOrderState==40||datar.compOrderState==50||datar.compOrderState==80">
					<button class="ui-btn-lg ui-btn-primary color-bg" @click="startBtn()">
						{{datar.compOrderState==40?"开始执行":datar.compOrderState==50?"到达目的地":datar.compOrderState==80?"结束":'订单异常'}}
						<!-- {{textWash(datar)}} -->
						<!-- {{datar.compOrderState==10?"待审批":datar.compOrderState==95?"行后待审批":datar.compOrderState==20?"待派车":datar.compOrderState==24?"租赁待审批":datar.compOrderState==25?"租赁待派车":datar.compOrderState==30?"待接单":datar.compOrderState==40?"开始执行":datar.compOrderState==50?"到达目的地":datar.compOrderState==60?"到达出发地":datar.compOrderState==70?"进行中":datar.compOrderState==80?"结束":datar.compOrderState==120?"结算审核中":datar.compOrderState==200?"已完成":datar.compOrderState==210?"已取消":datar.compOrderState==220?"审批驳回":datar.compOrderState==230?"调度驳回":datar.compOrderState==100?"待确认":datar.compOrderState==90?"已回场":datar.compOrderState==110?"待结算":datar.compOrderState==205?"待评价":"订单异常"}} -->
					</button>
					<button class="ui-btn-lg goMapBtn" @click="openBtn('drawerShow')"
						v-if="datar.compOrderState==50">导航</button>
				</div>

				<div class="map_detail" v-if="datar.compOrderState==200">
					<div class="map_detail_num u-flex u-row-between" @click="isshow=!isshow">
						<div class="map_detail_num_t">
							<span>{{datar.actualFee}}</span>元
						</div>
						<div class="map_detail_num_r">
							<span>{{isshow? "收起":"展开"}}</span>
							<i :class="{ fa : true, 'fa-angle-down' : !isshow, 'fa-angle-up' : isshow}"></i>
						</div>
					</div>

					<div :class="{map_detail_scoll:true,active:isshow}">
						<ul class="map_detail_ul">
							<li class="u-flex u-row-between">
								<div class="detail_tit">
									<p>预估里程</p>
								</div>
								<div class="detail_val text-right">
									<P>{{(datar.aboutDistance / 1000).toFixed(1)}}公里</p>
								</div>
							</li>
							<li class="u-flex u-row-between">
								<div class="detail_tit">
									<p>实际里程</p>
								</div>
								<div class="detail_val text-right">
									<p>{{datar.actualMileage?datar.actualMileage:0}}公里</p>
								</div>
							</li>
							<li v-for="(item,index) in datar.feeDetail" :key="index" class="u-flex u-row-between">
								<div class="detail_tit">
									<p>{{item.key}}</p>
								</div>
								<div class="detail_val text-right">
									<p>{{item.value?item.value:0}} 元</p>
								</div>
							</li>
						</ul>
					</div>
				</div>
			</div>

			<!-- 结束订单 -->
			<div :class="{show:endShow,'ui-dialog':true}">
				<ul class="end_box">
					<div class="u-flex u-row-between end_title">
						<span></span>
						<span>里程费用确认</span>
						<i class="fa fa-close" @click="closeBtn('endShow')"></i>
					</div>

					<li class="ui-border-t">
						<div class="ui-list-info u-flex u-row-between">
							<h4 class="ui-nowrap">预估里程:</h4>
							<div class="ui-txt-info">
								{{datar.aboutDistancer}}公里
							</div>
						</div>
					</li>
					<li class="ui-border-t">
						<div class="ui-list-info u-flex u-row-between">
							<h4 class="ui-nowrap">实际里程:</h4>
							<div class="ui-txt-info">
								{{datar.actualMileage?datar.actualMileage:0}}公里
								<!-- <input type="number" class="mileage_input" placeholder="请输入实际里程" v-model="actualMileage"
									@input="inputAbout">
								公里
								<i class="fa fa-edit"></i> -->
							</div>
						</div>
					</li>

					<li class="ui-border-t">
						<div class="ui-list-info u-flex u-row-between">
							<h4 class="ui-nowrap">订单费用:</h4>
							<!-- <div class="ui-txt-info">
								{{(datar.selfSubsidyFee?datar.selfSubsidyFee:0 * actualMileage?actualMileage:0).toFixed(2)}}
								元
							</div> -->
						</div>
					</li>
					<li class="ui-border-t">
						<div class="ui-list-info u-flex u-row-between">
							<h4 class="ui-nowrap">补贴费用 <span
									v-if="datar.selfSubsidyFee">({{datar.selfSubsidyFee}}元/公里)</span>:
							</h4>
							<div class="ui-txt-info">
								{{((datar.actualMileage?datar.actualMileage:0)*datar.selfSubsidyFee).toFixed(2)}}元
							</div>
						</div>
					</li>
					<li class="ui-border-t">
						<div class="ui-list-info u-flex u-row-between">
							<h4 class="ui-nowrap" style="min-width: 70px;">停车费(元):</h4>
							<div class="ui-txt-info" style="flex:1">
								<input type="number" class="mileage_input" placeholder="请输入实际费用" v-model="moneyParking"
									@input="inputMoney($event,'moneyParking')" style="width: 80%">
								元
								<i class="fa fa-edit"></i>
							</div>
						</div>
					</li>
					<li class="ui-border-t">
						<div class="ui-list-info u-flex u-row-between">
							<h4 class="ui-nowrap" style="min-width: 70px;">高速费(元):</h4>
							<div class="ui-txt-info" style="flex:1">
								<input type="number" class="mileage_input" placeholder="请输入实际费用" v-model="moneySpeed"
									@input="inputMoney($event,'moneySpeed')" style="width: 80%">
								元
								<i class="fa fa-edit"></i>
							</div>
						</div>
					</li>
					<li class="ui-border-t">
						<div class="ui-list-info u-flex u-row-between">
							<h4 class="ui-nowrap">总费用(元):</h4>
							<div class="ui-txt-info">
								{{moneyWash()}}元
							</div>
						</div>
					</li>
					<div class="u-flex u-row-around popup_btn_box">
						<button class="popup_btn_yes" @click="saveBtn">确定</button>
						<button class="popup_btn_no" @click="closeBtn('endShow')">取消</button>
					</div>
				</ul>
			</div>

			<!-- 导航弹出选择目的地 -->
			<div :class="{show:drawerShow,'ui-dialog':true}">
				<ul class="drawerUl">
					<div class="drawerTlite">
						<span>您要去哪里？</span>
						<span class="drawerClose" @click="closeBtn('drawerShow')">关闭</span>
					</div>
					<li class="u-flex u-row-between">
						<div>{{ datar.fromAddrName}}</div>
						<div class="gobtn"
							@click="gonav({siteLng:datar.fromLng,siteLat:datar.fromLat,siteAddrDetail:datar.fromAddrDetail,})">
							去这里</div>
					</li>
					<li class="u-flex u-row-between" v-for="(item,index) in datar.throughAddrInfo" :key="index">
						<div>{{item.siteAddrName}}</div>
						<div class="gobtn" @click="gonav(item)">去这里</div>
					</li>
					<li class="u-flex u-row-between">
						<div>{{ datar.toAddrName}}</div>
						<div class="gobtn"
							@click="gonav({siteLng:datar.toLng,siteLat:datar.toLat,siteAddrDetail:datar.toAddrDetail,})">
							去这里</div>
					</li>
				</ul>
			</div>

			<!-- 加载中 -->
			<div :class="{show:loderShow,'ui-dialog':true}">
				<div class="loder">
					<div></div>
					<div></div>
					<div></div>
				</div>
			</div>

			<!-- 开始行程 -->

		</div>
	</body>

	<!-- 微信 JS-SDK 如果不需要兼容小程序，则无需引用此 JS 文件。 -->
	<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
	<!-- uni 的 SDK -->
	<script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script>
	<script>
		document.addEventListener('UniAppJSBridgeReady', function() {});
	</script>

	<script>
		new Vue({
			el: '#app',
			data: {
				loderShow: false,
				urlObj: {},
				datar: {},
				loderhoust: "",
				drawerShow: false,
				endShow: false,
				actualMileage: "",
				moneyParking: "",
				moneySpeed: "",
				isshow: false
			},

			created() {
				this.loderShow = true
			},
			mounted() {
				this.urlObj = this.getQueryString()
				console.log(this.urlObj, '---------------');
				this.getDtails()

			},
			watch: {
				loderhoust(newr, used) {
					if (newr == 503 || newr == 502) {
						setTimeout(() => {
							uni.navigateTo({
								url: '/pages/login/login'
							})
						}, 1500)

					} else if (newr == 0) {
						setTimeout(() => {
							uni.navigateBack()
						}, 1500)
					}
				},
			},
			methods: {
				moneyWash() {
					return (this.sumWash(this.moneyParking) + this.sumWash(this.moneySpeed) + (this.sumWash(this.datar
						.actualMileage) * this.sumWash(this.datar.selfSubsidyFee))).toFixed(2)
				},
				sumWash(v) {
					return v ? Number(v) : 0
				},
				textWash(datar) {
					return datar.compOrderState == 10 ? "待审批" : datar.compOrderState == 95 ? "行后待审批" : datar
						.compOrderState == 20 ? "待派车" : datar.compOrderState == 24 ? "租赁待审批" : datar
						.compOrderState ==
						25 ? "租赁待派车" : datar.compOrderState == 30 ? "待接单" : datar.compOrderState == 40 ?
						"待执行" : datar
						.compOrderState == 50 ? "订单进行中，正在前往目的地" : datar.compOrderState == 60 ? "到达出发地" : datar
						.compOrderState == 70 ? "进行中" : datar.compOrderState == 80 ? "到达目的地" : datar
						.compOrderState ==
						120 ? "结算审核中" : datar.compOrderState == 103 ? "费用待审批" : datar.compOrderState == 200 ?
						"已完成" :
						datar.compOrderState == 210 ? "已取消" :
						datar.compOrderState == 220 ? "审批驳回" : datar.compOrderState == 230 ? "调度驳回" : datar
						.compOrderState == 100 ? "费用待审批" : datar.compOrderState == 90 ? "已回场" : datar
						.compOrderState ==
						110 ? "待结算" : datar.compOrderState == 205 ? "待评价" : "订单异常"
				},
				saveBtn() {
					let obj = {
						aboutDistance: Number((this.datar.aboutDistance / 1000).toFixed(1)),
						actualMileage: this.datar.actualMileage,
						totalFee: this.moneyWash(),
						privateCarOtherFee: JSON.stringify(
							[{
								key: "停车费",
								value: this.moneyParking,
								feeDetailCode: 200,
							}, {
								key: "高速费",
								value: this.moneySpeed,
								feeDetailCode: 200,
							}]
						)
					}
					axios.put("order/app/privateendorder/" + this.datar.travelId,
						obj, {
							headers: {
								accessToken: this.urlObj.accessToken,
							},
						}).then(res => {
						if (res.data.code == 1) {
							this.getDtails()
							this.tipsr(res.data.data)
							this.endShow = false
						} else {
							this.tipsr(res.data.msg)
						}
					})
				},

				inputAbout(val) {
					this.$set(this, 'actualMileage', Number(val.target.value.toString().match(
						/^\d+(?:\.\d{0,1})?/)))
				},
				inputMoney(val, name) {
					// return item.value = (val.target.value.match(/^\d*(\.?\d{0,2})/g)[0]) || null
					// this.$set(this, name, Number(val.target.value.toString().match(/^\d+(?:\.\d{0,2})?/)))
					this.$set(this, name, (val.target.value.match(/^\d*(\.?\d{0,2})/g)[0]) || null)
				},
				startBtn() {
					if (this.datar.compOrderState == 40) {
						uni.navigateTo({
							url: `/pager/editMileage/editMileage?id=${this.datar.applyId}&type=1`
						})
					} else if (this.datar.compOrderState == 50) {
						uni.navigateTo({
							url: `/pager/editMileage/editMileage?id=${this.datar.applyId}&type=2`
						})
					} else if (this.datar.compOrderState == 80) {
						this.actualMileage = (this.datar.aboutDistance / 1000).toFixed(1)
						this.openBtn('endShow')
					}

				},
				openBtn(name) {
					this[name] = true
				},
				closeBtn(name) {
					this[name] = false
				},
				getDtails() {
					axios.get("order/app/privateorderinfo/" + this.urlObj.id, {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
					}).then(res => {
						this.loderhoust = res.data.code
						if (res.data.code == 1) {
							let data = res.data.data
							if (data.feeDetail) {
								data.feeDetail = JSON.parse(data.feeDetail)
								data.feeDetail.map((item, index) => {
									if (item.feeDetailCode == 200) {
										let unit = JSON.parse(item.unit)
										data.feeDetail.push(...unit)
									}
									if (item.key == "自定义项目费用") {
										data.feeDetail.splice(index, 1)
									}
								})
							}
							if (data.throughAddrInfo) {
								let arrs = JSON.parse(data.throughAddrInfo)
								this.$set(data, 'throughAddrInfo', arrs)
								this.$set(data, 'throughAddrInfoName', arrs.map(v => v.siteAddrName)
									.join(
										' → '))
							}
							data.aboutDistancer = (data.aboutDistance / 1000).toFixed(1) //只用在结束订单
							this.datar = data
							this.setMap(data)
						} else {
							this.tipsr(res.data.msg)
							setTimeout(() => {
								this.loderShow = false
							}, 400)
						}
					})
				},
				setMap(item) {
					let that = this
					AMapLoader.load({
						"key": "1cb53fa0e69dc44036161409f1d4039c", // 申请好的Web端开发者Key，首次调用 load 时必填
						"version": "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
						"plugins": ['AMap.Driving'], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
						"AMapUI": { // 是否加载 AMapUI，缺省不加载
							"version": '1.1', // AMapUI 版本
							"plugins": ['overlay/SimpleMarker'], // 需要加载的 AMapUI ui插件
						},
						"Loca": { // 是否加载 Loca， 缺省不加载
							"version": '2.0' // Loca 版本
						},
					}).then((AMap) => {
						///基本地图加载
						var map = new AMap.Map("container", {
							center: [that.datar.toLng, that.datar.toLat],
							resizeEnable: true,
						});

						//构造路线导航类
						var driving = new AMap.Driving({
							map: map
						});
						// 途经点
						let wayArr = []
						let wayAtt = []

						wayAtt.push({
							pozition: [that.datar.toLng, that.datar.toLat],
							name: that.datar.toAddrName,
						})

						if (item.throughAddrInfo && item.throughAddrInfo.length > 0) {
							item.throughAddrInfo.forEach(v => {
								wayArr.push(new AMap.LngLat(v.siteLng, v.siteLat))
								wayAtt.push({
									pozition: [v.siteLng, v.siteLat],
									name: v.siteAddrName,
								})
							})
						}

						if (wayAtt && wayAtt.length > 0) {
							wayAtt.forEach(v => {
								var marker = new AMap.Marker({
									position: v.pozition,
									icon: "",
									offset: new AMap.Pixel(0, 0)
								});
								marker.setMap(map);
								// 设置鼠标划过点标记显示的文字提示
								// label默认蓝框白底左上角显示，样式className为：amap-marker-label
								marker.setLabel({
									direction: 'right',
									offset: new AMap.Pixel(10, 0), //设置文本标注偏移量
									content: this.creatrMarg(v), //设置文本标注内容
								});
								marker.setExtData(v)

								marker.on('click', this.markerClick)
							})
						}

						// 根据起终点经纬度规划驾车导航路线
						driving.search(new AMap.LngLat(that.datar.fromLng, that.datar.fromLat),
							new AMap
							.LngLat(that.datar.toLng, that.datar.toLat), {
								waypoints: wayArr
							},
							function(status, result) {
								// result 即是对应的驾车导航信息，相关数据结构文档请参考  https://lbs.amap.com/api/javascript-api/reference/route-search#m_DrivingResult
								if (status === 'complete') {
									//构建自定义信息窗体
									var position = [that.datar.toLng, that.datar.toLat];
									//实例化信息窗体
									// var distance = result.routes[0].distance / 1000;
									// var time = that.second(result.routes[0].time);
									var distance = (item.aboutDistance / 1000).toFixed(1);
									var time = that.second(item.aboutDuration);

									var infoWindow = new AMap.InfoWindow({
										isCustom: true, //使用自定义窗体
										content: that.createInfoWindow(distance, time),
										offset: new AMap.Pixel(0, -40)
									});
									infoWindow.open(map, position);
								}
							});

					})
					setTimeout(() => {
						this.loderShow = false
					}, 400)
				},
				gonav(item) {
					if (this.urlObj.type == 'H5') {
						window.open(
							`https://uri.amap.com/marker?position=${item.siteLng},${item.siteLat}&name=${item.siteAddrDetail}&coordinate=gaode&callnative=1`,
							"_blank")
					} else {
						let objr = {
							latitude: Number(item.siteLat),
							longitude: Number(item.siteLng),
							name: item.siteAddrDetail,
							address: item.siteAddrDetail,
						}
						uni.navigateTo({
							url: `/pageDriver/staging/staging?objr=${JSON.stringify(objr)}`,
						});
					}
					this.drawerShow = false
				},
				markerClick(e) {
					if (this.datar.compOrderState == 40 || this.datar.compOrderState == 50) {
						let text = e.target.getExtData()
						uni.navigateTo({
							url: `/pages/map/map?url=/hybrid/html/addressSearch.html&title=地址
							&toLng=${text.pozition[0]}&toLat=${text.pozition[1]}&page=address&useCarInfoId=${this.datar.useCarInfoId}`
						});
					} else {
						this.tipsr('当前状态无法修改')
					}

				},
				// 跳转详情
				goOrderDetail(id) {
					uni.navigateTo({
						url: '/pager/orderDetail/orderDetail?id=' + id
					});
				},
				goBack() {
					uni.navigateBack({
						delta: 1
					})
					// uni.navigateTo({
					// 	url: '/pager/order/order?typer=2&namer=私车公用订单'
					// });
				},
				//构建自定义信息窗体
				createInfoWindow(distance, time) {
					var info = '<div class="mapTip">' +
						'<div class="mapTipt fontmin">' +
						'预计里程<span class="fontmin">' + distance + '</span>公里' +
						'</div>' +
						'<div class="mapTipd fontmin">' +
						'预估用时<span class="fontmin">' + time + '</span>' +
						'</div>' +
						'</div>';
					return info;
				},
				creatrMarg(v) {
					let text = `<div class='makarInfo'>${v.name}<i class="fa fa-angle-right"></i></div>`
					return text
				},
				second(value) {
					var theTime = parseInt(value); // 秒
					var middle = 0; // 分
					var hour = 0; // 小时

					if (theTime >= 60) {
						middle = parseInt(theTime / 60);
						theTime = parseInt(theTime % 60);
						if (middle >= 60) {
							hour = parseInt(middle / 60);
							middle = parseInt(middle % 60);
						}
					}
					var result = "";
					if (theTime > 0) {
						result = "" + parseInt(theTime) + "秒"
					}
					if (middle > 0) {
						result = "" + parseInt(middle) + "分" + result;
					}
					if (hour > 0) {
						result = "" + parseInt(hour) + "小时" + result;
					}
					return result;
				},
				tipsr(data, time) {
					let msg_c = document.createElement('div');
					msg_c.id = "msg_c"
					let alertForm = document.createElement('div');
					alertForm.id = "successMsg";
					alertForm.innerHTML = data;
					msg_c.appendChild(alertForm);
					document.getElementsByTagName("body")[0].appendChild(msg_c);
					if (time) {
						setTimeout(function() {
							msg_c.style.display = "none";
							msg_c.remove()
						}, time)
					} else {
						setTimeout(function() {
							msg_c.style.display = "none";
							msg_c.remove()
						}, 1500)
					}
				},
				getQueryString() {
					let url = window.location.href
					let p = url.split('?')[1]
					let keyValue = p.split('&');
					let obj = {};
					for (let i = 0; i < keyValue.length; i++) {
						let item = keyValue[i].split('=');
						let key = item[0];
						let value = item[1];
						obj[key] = value;
					}
					return obj
				},
			},
			filters: {
				timeFormat(dateTime = null, fmt = 'yyyy-mm-dd') {
					// 如果为null,则格式化当前时间
					if (!dateTime) dateTime = Number(new Date())
					// 如果dateTime长度为10或者13，则为秒和毫秒的时间戳，如果超过13位，则为其他的时间格式
					if (dateTime.toString().length == 10) dateTime *= 1000
					const date = new Date(dateTime)
					let ret
					const opt = {
						'y+': date.getFullYear().toString(), // 年
						'm+': (date.getMonth() + 1).toString(), // 月
						'd+': date.getDate().toString(), // 日
						'h+': date.getHours().toString(), // 时
						'M+': date.getMinutes().toString(), // 分
						's+': date.getSeconds().toString() // 秒
						// 有其他格式化字符需求可以继续添加，必须转化成字符串
					}
					for (const k in opt) {
						ret = new RegExp(`(${k})`).exec(fmt)
						if (ret) {
							fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1]
								.length, '0')))
						}
					}
					return fmt
				}
			},
		})
	</script>
</html>