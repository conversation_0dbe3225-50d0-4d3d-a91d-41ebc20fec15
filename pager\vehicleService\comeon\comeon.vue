<template>
	<view class="contenr">
		<u--form :model="form" :rules="rulesr" ref="uForm" class="is-bottom" :label-width="100" labelAlign="right"
			v-show="!overShow">
			<view class="formBg">
				<u-form-item :required="true" label="选择车辆 :" prop="carNumber" borderBottom @tap="pickerBtn(1)">
					<u--input v-model="form.carNumber" disabled disabledColor="#ffffff" placeholder="请选择车辆"
						border="none"></u--input>
					<view class="mag" slot="right">

						<u-icon slot="right" name="arrow-right"></u-icon>
					</view>
				</u-form-item>
				<u-form-item label="品牌型号 :" prop="brandModelName" borderBottom>
					<u-input v-model="form.brandModelName" border="none" placeholder="根据选择车辆自动填充品牌型号" disabled
						disabledColor="#ffffff" />
				</u-form-item>
			</view>
			<view class="formBg">
				<block v-if="typer==1">
					<u-form-item :required="true" label="加油时间: " prop="oilTime" borderBottom @tap="tiemBtn">
						<u--input v-model="form.oilTime" disabled disabledColor="#ffffff" placeholder="请选择加油时间"
							border="none">
						</u--input>
						<view class="mag" slot="right">
							<u-icon slot="right" name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<u-form-item :required="true" label="加油费用 :" prop="oilCost" borderBottom>
						<u-input v-model="form.oilCost" border="none" type='number' placeholder="请输入加油费用"
							@input='monryIpt' />
						<text slot="right" class="mag">元</text>
					</u-form-item>
					<u-form-item :required="true" label="燃油标号 :" prop="oilNamer" borderBottom @tap='allOpen(1)'>
						<u--input v-model="form.oilNamer" disabled disabledColor="#ffffff" placeholder="请选择燃油标号"
							border="none"></u--input>
						<view class="mag" slot="right">
							<u-icon slot="right" name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<u-form-item :required="true" label="燃油单价 :" prop="oilUnitPrice" borderBottom>
						<u-input v-model="form.oilUnitPrice" border="none" type='number' placeholder="请输入燃油单价"
							@input='monryIpt' />
						<text slot="right" class="mag">元</text>
					</u-form-item>
					<u-form-item label="加油量 :" prop="oilNumber" borderBottom>
						<u-input v-model="form.oilNumber" border="none" placeholder="自动计算" disabled
							disabledColor="#ffffff" />
						<text slot="right" class="mag">升</text>
					</u-form-item>
				</block>
				<!-- 充电 -->
				<block v-if="typer==8">
					<u-form-item :required="true" label="充电时间: " prop="oilTime" borderBottom @tap="tiemBtn">
						<u--input v-model="form.oilTime" disabled disabledColor="#ffffff" placeholder="请选择加油时间"
							border="none">
						</u--input>
						<view class="mag" slot="right">
							<u-icon slot="right" name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<u-form-item label="充电类型 :" prop="PayName" :required="true" borderBottom @tap='allOpen(2)'>
						<u--input v-model="form.PayName" disabled disabledColor="#ffffff" placeholder="请选择支付方式"
							border="none"></u--input>
						<view class="mag" slot="right">
							<u-icon slot="right" name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<u-form-item :required="true" label="订单费用 :" prop="oilCost" borderBottom>
						<u-input v-model="form.oilCost" border="none" type='number' placeholder="请输入订单费用"
							@input='monryIpt' />
						<text slot="right" class="mag">元</text>
					</u-form-item>
					<u-form-item :required="true" label="充电度数 :" prop="oilCost" borderBottom>
						<u-input v-model="form.oilCost" border="none" type='number' placeholder="请输入充电度数"
							@input='monryIpt' />
						<text slot="right" class="mag">度</text>
					</u-form-item>
					<u-form-item label="充电时长 :" prop="oilNumber" borderBottom>
						<u-input v-model="form.oilNumber" border="none" placeholder="自动计算" disabled
							disabledColor="#ffffff" />
						<text slot="right" class="mag">分钟</text>
					</u-form-item>
					<u-form-item label="电费单位 :" prop="oilNumber" borderBottom>
						<u-input v-model="form.oilNumber" border="none" placeholder="自动计算" disabled
							disabledColor="#ffffff" />
						<text slot="right" class="mag">元/度</text>
					</u-form-item>

				</block>
				<u-form-item :label="`${typer==1?'加油':'充电'}地点 : `" prop="oilAddress" borderBottom @click='mapBtn'>
					<u--input v-model="form.oilAddress" disabled disabledColor="#ffffff" placeholder="请选择加油地点"
						border="none"></u--input>
					<view class="mag" slot="right">
						<u-icon slot="right" name="map-fill"></u-icon>
					</view>
				</u-form-item>
			</view>

			<view class="formBg">
				<u-form-item label="支付方式 :" prop="PayName" :required="true" borderBottom @tap='allOpen(2)'>
					<u--input v-model="form.PayName" disabled disabledColor="#ffffff" placeholder="请选择支付方式"
						border="none"></u--input>
					<view class="mag" slot="right">
						<u-icon slot="right" name="arrow-right"></u-icon>
					</view>
				</u-form-item>
				<u-form-item :label="`选择${typer==1?'油卡':'电卡'} :`" prop="oilCardName" borderBottom @tap='allOpen(3)'
					v-if="form.PayName=='油卡'">
					<u--input v-model="form.oilCardName" disabled disabledColor="#ffffff" placeholder="请选择油卡"
						border="none"></u--input>
					<view class="mag" slot="right">
						<u-icon slot="right" name="arrow-right"></u-icon>
					</view>
				</u-form-item>
			</view>

			<view class="formBg">
				<u-form-item label="里程数 :" prop="dashboardMileage" borderBottom>
					<u-input v-model="form.dashboardMileage" type='number' border="none" placeholder="请输入里程数" />
					<text slot="right" class="mag">公里</text>
				</u-form-item>
				<u-form-item label="经办人 :" prop="userName" borderBottom>
					<u--input v-model="form.userName" disabled disabledColor="#ffffff" placeholder="默认当前登录人员"
						border="none"></u--input>
				</u-form-item>
			</view>
			<view class="formBg pding">
				<u-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic" multiple :maxCount="8">
				</u-upload>
			</view>
			<view class="formBg pding wx_pd">
				<text>备注说明 :</text>
				<u--textarea v-model="form.remark" placeholder="请输入备注说明" confirmType="done"></u--textarea>
			</view>
		</u--form>
		<view class="formBg pding btn u-align-items u-row-around" v-show="!overShow">
			<view class="" style="width: 70%;">
				<u-button type="primary" color="#346CF2" text="提交确认" @tap="submit()"></u-button>
			</view>
			<view class="" style="width: 25%;">
				<u-button type="primary" color="#346CF2" text="上报列表" @click="goRouter()"></u-button>
			</view>
		</view>

		<!-- 日期选择 -->
		<u-datetime-picker :show="timeShow" v-model="timeMode" mode="date" :formatter="formatter" @cancel="tiemBtn"
			:closeOnClickOverlay='true' @close="tiemBtn" @confirm='confirms'>
		</u-datetime-picker>

		<!-- 车牌选择器 -->
		<u-picker :show="pickerShow" :columns="carList" keyName="carNumber" :closeOnClickOverlay='true'
			@confirm="carConfirm" @cancel="pickerBtn" @close="pickerShow=false"></u-picker>

		<!-- 燃油标号、支付、油卡 -->
		<u-picker :show="gradeShow" :columns="allList" keyName="dicDescribe" :closeOnClickOverlay='true'
			@confirm="gradeConfirm" @cancel="gradeBtn" @close="gradeBtn"></u-picker>

		<u-picker :show="cardShow" :columns="cardList" keyName="dicDescribe" :closeOnClickOverlay='true'
			@confirm="gradeConfirm" @cancel="gradeBtn" @close="gradeBtn"></u-picker>

		<!-- 地图获取 -->
		<view v-if="overShow">
			<u-navbar title="选择加油地址" @leftClick="leftClick">
			</u-navbar>
			<!-- #ifdef MP-WEIXIN -->
			<web-view src='https://zqcx.di-digo.com/app-h5/hybrid/html/comeon.html' style="top: 88rpx;"></web-view>
			<!-- #endif -->
			<!-- #ifdef H5 -->
			<web-view src="/hybrid/html/comeon.html" style="top: 88rpx;"></web-view>
			<!-- #endif -->
		</view>

		<getSeat @seatClose='seatClose' v-if="seatShow" :seatIndex='0'></getSeat>
	</view>
</template>

<script>
	import {
		appCardlist,
		uploadImg
	} from '@/config/consoler.js'
	import getSeat from '@/components/getSeat/getSeat.vue'
	export default {
		components: {
			getSeat
		},
		props: ['carList', 'typer'],
		data() {
			return {
				form: {
					"areaCode": null,
					"carId": null,
					"dashboardMileage": null,
					"imgUrls": null,
					"oilAddress": null,
					"oilCardId": null,
					"oilCost": null,
					"oilLat": null,
					"oilLevel": null,
					"oilLng": null,
					"oilNumber": null,
					"oilPayWay": null,
					"oilTime": null,
					"oilUnitPrice": null,
					"operatorUserId": null,
					"remark": null,
				},
				rulesr: {
					'carNumber': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'oilTime': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'oilCost': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'oilNamer': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'PayName': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'oilUnitPrice': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
				},
				fileList: [],
				gradeShow: false,
				carShow: false,
				timeShow: false,
				timeMode: Number(new Date()),
				pickerShow: false,
				allList: [],
				gradeList: [],
				cardList: [],
				oilPayWayList: [],
				gradeType: null,
				overShow: false,
				seatShow: false,
				cardShow: false
			}
		},
		created() {
			window.addEventListener('message', this.handleMessage);
		},
		beforeDestroy() {
			window.removeEventListener('message', this.handleMessage);
		},
		onReady() {
			//onReady 为uni-app支持的生命周期之一
			this.$refs.uForm.setRules(this.rulesr)
		},
		mounted() {
			this.initr()
		},
		methods: {
			seatClose(e) {
				if (!e) return this.seatShow = false
				let loction = e.location.split(',')
				if (this.typer == 8 || this.typer == 1) {
					this.form.oilAddress = `${e.name}`
					this.form.oilLat = loction[0]
					this.form.oilLng = loction[1]
				}
				this.seatShow = false
				console.log(this.form, 'this.form');
			},
			goRouter() {
				uni.$u.route({
					url: '/pager/vehicleEscala/vehicleEscala',
					params: {
						id: this.typer,
					},
				})
			},
			// 接收webview 参数
			handleMessage(evt) {
				let data = evt.data.data
				if (data && data.arg) {
					this.form.oilAddress = data.arg.oliName
					this.form.oilLat = data.arg.oilLat
					this.form.oilLng = data.arg.oilLng
					this.leftClick()
				}
			},
			leftClick() {
				this.overShow = !this.overShow
			},
			// 计算
			monryIpt() {
				let sum3 = (parseFloat(this.form.oilCost) / parseFloat(this.form.oilUnitPrice)).toFixed(2);
				if (isNaN(sum3)) {
					sum3 = "";
				}
				this.$set(this.form, 'oilNumber', sum3)
			},
			// 燃油标号选择
			gradeConfirm(e) {
				let o = e.value[0]
				if (this.gradeType == 1) {
					this.$set(this.form, 'oilLevel', o.dicValue)
					this.$set(this.form, 'oilNamer', o.dicDescribe)
				}
				if (this.gradeType == 2) {
					this.$set(this.form, 'oilPayWay', o.dicValue)
					this.$set(this.form, 'PayName', o.dicDescribe)
				}
				if (this.gradeType == 3) {
					let car = null
					if (!this.form.carNumber) return uni.$u.toast('请选择车辆')
					if (o.carNumbers) {
						car = o.carNumbers.split(",")
					}
					if (!car.includes(this.form.carNumber)) return uni.$u.toast('未与该油卡绑定车辆')

					this.$set(this.form, 'oilCardId', o.oilCardId)
					this.$set(this.form, 'oilCardName', o.dicDescribe)
				}
				// 如果==1(现金)  置空油卡的id和name
				if (this.form.oilPayWay == 1) {
					this.form.oilCardName = null
					this.form.oilCardId = null
				}
				this.gradeBtn()
			},
			// 车牌选择
			carConfirm(e) {
				let o = e.value[0]
				this.$set(this.form, 'carId', o.carId)
				this.$set(this.form, 'brandModelName', o.brandModelName)
				this.$set(this.form, 'carNumber', o.carNumber)
				this.pickerBtn()
			},
			// 选中时间
			confirms(e) {
				this.form.oilTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				this.tiemBtn()
			},

			//标号、支付类型
			allOpen(type) {
				this.gradeType = type
				if (type == 1) {
					this.allList = this.gradeList
					this.gradeBtn()
				}
				if (type == 2) {
					this.allList = this.oilPayWayList
					this.gradeBtn()
				}
				if (type == 3) {
					this.allList = this.cardList
					this.cardShow = true
					return false
				}
			},

			// 获取位置
			mapBtn() {
				this.seatShow = true
			},
			// 上传附件
			afterRead(event) {
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList`].length
				lists.map((item) => {
					this[`fileList`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				lists.forEach(r => {
					uploadImg(r, 4).then(res => {
						if (res) {
							let item = this[`fileList`][fileListLen]
							this[`fileList`].splice(fileListLen, 1, Object.assign(item, {
								status: 'success',
								message: '',
								urlr: res
							}))
							fileListLen++
						} else {
							this[`fileList`].splice(fileListLen, 1)
							uni.$u.toast('上传失败')
						}
					})
				})
			},

			// 删除附件
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},

			// 时间开关
			tiemBtn() {
				this.timeShow = !this.timeShow
			},
			// 车牌开关
			pickerBtn() {
				this.pickerShow = !this.pickerShow
			},
			// 燃油标号、支付方式 开关
			gradeBtn() {
				if (this.gradeType == 3) {
					this.cardShow = false
				} else {
					this.gradeShow = !this.gradeShow
				}
			},
			// 时间格式化
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				return value
			},
			// 保存
			submit() {
				this.$refs.uForm.validate().then(res => {
					this.form.imgUrls = this.fileList.map(res => {
						return res.urlr
					}).join()
					setTimeout(() => {
						this.$emit('preserva', this.form)
					}, 500)
				}).catch(errors => {})
			},
			initr() {
				let user = this.$common.getItem('userInfo')
				let arrs = this.$common.getItem('dicVoList')
				this.form.userName = user.name
				this.form.operatorUserId = user.userId
				arrs.forEach(item => {
					if (item.dicCode == "oil_pay_way") {
						this.oilPayWayList = [item.dicValueList]
					}
					if (item.dicCode == "oil_level") {
						this.gradeList = [item.dicValueList]
					}
				})


				appCardlist({
					params: {
						pageNum: 1,
						pageSize: 999
					}
				}).then(res => {
					let data = res.pageList
					data.forEach(e => {
						e.dicDescribe = e.oilCardName
					})
					this.cardList = [res.pageList]
				})
			},


		}
	}
</script>

<style lang="scss" scoped>
	.is-bottom {
		padding-bottom: 120rpx !important;
	}

	.formBg {
		margin: 20rpx 0;
		background-color: #fff;

		/deep/.u-form-item__body__left__content__label {
			display: flex;
			justify-content: flex-end !important;
			padding-right: 10rpx;
		}

		/deep/.item__body__right__content__icon {
			padding-right: 30rpx;
		}

		/deep/.u-form-item__body {
			padding: 30rpx 0;
		}

	}

	.btn {
		position: fixed;
		bottom: 0;
		width: calc(100% - 60rpx);
		margin: 0 !important;
		border-top: 4rpx solid #f3f3f3;
		z-index: 99;
	}

	.pding {
		padding: 20rpx 30rpx;

	}

	.wx_pd {
		/* #ifdef MP-WEIXIN */
		padding-bottom: 120rpx;
		/* #endif */
	}

	.open {
		position: fixed;
		top: 0;
		height: 100vh;
		width: 100%;
		z-index: 200;
	}

	.contenr {
		height: calc(100% - 88rpx);
	}

	.mag {
		margin-right: 10rpx;
	}
</style>