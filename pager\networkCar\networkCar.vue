<template>
	<view>
		<web-view :src="netUrl" v-if="netUrl"></web-view>
	</view>
</template>

<script>
	import {
		applynetcarurl
	} from '@/config/consoler.js';
	export default {
		data() {
			return {
				netUrl: null
			}
		},
		onLoad(on) {
			uni.showLoading({
				title: '加载中',
				mask: true
			})
			this.getUrl(on)
		},
		methods: {
			getUrl(val) {
				let obj = {
					id: val.id,
					type: val.type
				}
				applynetcarurl(obj).then(res => {
					this.netUrl = res
					uni.hideLoading()
				}).catch(err => {
					setTimeout(() => {
						uni.navigateBack()
					}, 500)
				})
			}

		}
	}
</script>

<style>

</style>