// 计算隔夜
export const overnightDay = (startTime, endTime) => {
	// 将时间字符串转换为Date对象
	const startDate = new Date(startTime);
	const endDate = new Date(endTime);

	// 获取开始日期和结束日期（不含时间部分）
	const startDay = new Date(
		startDate.getFullYear(),
		startDate.getMonth(),
		startDate.getDate()
	);
	const endDay = new Date(
		endDate.getFullYear(),
		endDate.getMonth(),
		endDate.getDate()
	);

	// 计算相差的天数
	const diffTime = endDay.getTime() - startDay.getTime();
	const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

	// 根据描述：只要有跨0点就算1夜，两个0点就算2夜，以此类推
	return diffDays;
};