@media screen and (max-width: 319px) {
	html {
		font-size: 85.33333px
	}
}

@media screen and (min-width: 320px) and (max-width: 359px) {
	html {
		font-size: 85.33333px
	}
}

@media screen and (min-width: 360px) and (max-width: 374px) {
	html {
		font-size: 96px
	}
}

@media screen and (min-width: 375px) and (max-width: 383px) {
	html {
		font-size: 100px
	}
}

@media screen and (min-width: 384px) and (max-width: 399px) {
	html {
		font-size: 102.4px
	}
}

@media screen and (min-width: 400px) and (max-width: 411px) {
	html {
		font-size: 106.66667px
	}
}

@media screen and (min-width: 412px) and (max-width: 413px) {
	html {
		font-size: 109.86667px
	}
}

@media screen and (min-width: 414px) {
	html {
		font-size: 110.4px
	}
}

body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td,
header,
hgroup,
nav,
section,
article,
aside,
footer,
figure,
figcaption,
menu,
button {
	margin: 0;
	padding: 0
}

body {
	font-family: "Helvetica Neue", Helvetica, STHeiTi, sans-serif;
	line-height: 1.5;
	font-size: 16px;
	color: #000;
	background-color: #f2f2f2;
	-webkit-user-select: none;
	-webkit-text-size-adjust: 100%;
	-webkit-tap-highlight-color: transparent;
	outline: 0
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-size: 100%;
	font-weight: normal
}

table {
	border-collapse: collapse;
	border-spacing: 0
}

caption,
th {
	text-align: left
}

fieldset,
img {
	border: 0
}

li {
	list-style: none
}

ins {
	text-decoration: none
}

del {
	text-decoration: line-through
}

input,
button,
textarea,
select,
optgroup,
option {
	font-family: inherit;
	font-size: inherit;
	font-style: inherit;
	font-weight: inherit;
	outline: 0
}

button {
	-webkit-appearance: none;
	border: 0;
	background: none
}

a {
	-webkit-touch-callout: none;
	text-decoration: none
}

:focus {
	outline: 0;
	-webkit-tap-highlight-color: transparent
}

em,
i {
	font-style: normal
}

input[type="search"],
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button {
	-webkit-appearance: none !important
}

a {
	color: #00a5e0
}

em {
	color: #ff8444
}

::-webkit-input-placeholder {
	color: #bbb
}

h1 {
	font-size: 18px
}

h2 {
	font-size: 18px
}

h3,
h4 {
	font-size: 16px
}

h5,
.ui-txt-sub {
	font-size: 14px
}

h6,
.ui-txt-tips {
	font-size: 14px
}

.ui-txt-default {
	color: #000
}

.ui-txt-white {
	color: white
}

.ui-txt-info {
	color: #777
}

.ui-txt-muted {
	color: #bbb
}

.ui-txt-warning,
.ui-txt-red {
	color: #ff4222
}

.ui-txt-feeds {
	color: #314c83
}

.ui-txt-highlight {
	color: #ff8444
}

.ui-txt-justify {
	text-align: justify
}

.ui-txt-justify-one {
	text-align: justify;
	overflow: hidden;
	height: 24px
}

.ui-txt-justify-one:after {
	display: inline-block;
	content: '';
	overflow: hidden;
	width: 100%;
	height: 0
}

.ui-arrowlink {
	position: relative
}

.ui-arrowlink:after {
	content: "";
	display: block;
	position: absolute;
	right: 12px;
	top: 50%;
	margin-top: -7px;
	width: 8px;
	height: 14px;
	background: url(//i.gtimg.cn/vipstyle/qui/2.0.0/img/icon_arrowlink.png) no-repeat;
	-webkit-background-size: 100% auto;
	background-size: 100% auto
}

.ui-arrowlink.active {
	background: #f2f2f2
}

@font-face {
	font-family: "icon-min";
	src: url(data:font/ttf;charset=utf-8;base64,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) format("truetype")
}

.ui-icon-search {
	font-family: "icon-min" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5)
}

.ui-icon-search {
	font-size: 14px;
	color: #ccc
}

.ui-icon-search:before {
	content: "飥�"
}

.ui-arrowlink-iconfont {
	position: relative
}

.ui-arrowlink-iconfont:after {
	font-family: "icon-min" !important;
	font-size: 14px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: #bbb;
	content: "飥€";
	position: absolute;
	right: 12px;
	top: 50%;
	margin-top: -22.5px;
	width: 8px
}

.ui-arrowlink.active {
	background: #f2f2f2
}

.ui-avatar,
.ui-square,
.ui-avatar-lg,
.ui-avatar-s,
.ui-avatar-one,
.ui-avatar-tiled {
	display: block;
	-webkit-background-size: cover;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAMAAABHPGVmAAAA8FBMVEXp8Peat8+jwNidutKhvtagvdWcudGfu9Kpwteiv9ecudCsxNifvNTM2+ieu9PE1eSdudDO3OmkvtSyyNu/0eKhvNOivdPm7vWlv9WeutHI2Oa5zd+dudG+0eHn7/bG1+Xi6/SzydzT4Ozc5vDe6PLY5O/a5e/P3eqmv9WmwNW6zt/j7PTX4+6uxtno8Pff6fLh6vPW4u280ODg6fLC1OObuM+buNDD1eTU4eza5fDd5/GhvNKowdarw9jA0+K1y93k7PTk7fXl7fW7zt/F1uXc5/DZ5O/R3+vB0+OnwNXg6vKcuNDM2+mtxNjO3eq3zN1UQ75QAAACR0lEQVR4Xu3W1a7cMBAG4PnHDi4zMx5mxiLD+79Ne7YXq6hKHMU+Ui/8XVpKfo0nMwr9hyzLsizLsqx5ZTfX9DyvmXtXOaNXsd+rYqs9mJFx454HiLwMXsi8CzTO35JZ0x1ABLwlBZAzW0yhAzfgKOmiekLmVEII/peAd22u5ZGMSEpzSWYc30cyoim+oe4/wuU4LgZkwq0HyXEkPCMX9hmC4wmcHpK2VhWS40ncHZG2KcBJBAom2l7kJA6eSFsNDicJsB5qt8SH5EToz0nT1zUCRUi4IE3zqjLkm/aaPGsrQ8oz0nSkDgm1Z750AU4mtL/hYQ1FThZgZ4+0HH9BoAzx9knL8hKsoL9YChCsksdAd3PlWcXBhHSM15CsEqCsNY49uKwm4Lcos5MyAk7BRYmyOpxAcBoOqkca/1sBpyKyl1KH4HQc5J4pmzYkpwQsKJsQnFYRI8qmnD7EwdPrh0gcZA9xio76piBY4iFziACUMw+EcLNXEgKd7o5qVtD52UYeu5RNB3iiifIP0qcRgAplU4N/TNdILsVFgVq/0My6Vxa9lyeTF5jAwzPRsF4gLbfNhBSJ/pRMKPThxGbgkcy4iu19HqdkxN7oR2wlDmqrQ9K39JPm8RLYbZGu8T1cJ3mp1ElXJVqGLAKI7DOJxpA0Le8gJP8VSIGN7RE7Lmr6XfneACCKfwgAjfPFdP8qcpSbk76bgX+BDe+gPqMXs3quj43OQekNGTH+WBmV3nc/fdi+b+9m1S2VuqvZM6lZlmVZlmVZvwEAnS9LHbI74gAAAABJRU5ErkJggg==)
}

.ui-avatar {
	width: 50px;
	height: 50px;
	-webkit-border-radius: 200px;
	overflow: hidden
}

.ui-avatar>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

.ui-avatar-lg,
.ui-avatar-one {
	width: 70px;
	height: 70px;
	-webkit-border-radius: 200px;
	overflow: hidden
}

.ui-avatar-lg>span,
.ui-avatar-one>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

.ui-avatar-s {
	width: 34px;
	height: 34px;
	-webkit-border-radius: 200px;
	overflow: hidden
}

.ui-avatar-s>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

.ui-avatar-tiled {
	width: 30px;
	height: 30px;
	-webkit-border-radius: 200px;
	overflow: hidden;
	display: inline-block
}

.ui-avatar-tiled>span {
	width: 100%;
	height: 100%;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	-webkit-border-radius: 200px
}

.ui-border-b:before {
	border-bottom: 1px solid #e9e9e9;
	content: '';
	display: block;
	width: 100%;
	position: absolute;
	left: 0;
	bottom: 0;
	-webkit-transform-origin: left bottom
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-border-b:before {
		-webkit-transform: scaleY(0.5)
	}
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
	.ui-border-b:before {
		-webkit-transform: scaleY(0.3333)
	}
}

.ui-border-t:before {
	border-top: 1px solid #e9e9e9;
	content: '';
	display: block;
	width: 100%;
	position: absolute;
	left: 0;
	top: 0;
	-webkit-transform-origin: left top
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-border-t:before {
		-webkit-transform: scaleY(0.5)
	}
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
	.ui-border-t:before {
		-webkit-transform: scaleY(0.3333)
	}
}

.ui-border-l:before {
	border-left: 1px solid #e9e9e9;
	content: '';
	display: block;
	bottom: 0;
	position: absolute;
	left: 0;
	top: 0;
	-webkit-transform-origin: left top
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-border-l:before {
		-webkit-transform: scaleX(0.5)
	}
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
	.ui-border-l:before {
		-webkit-transform: scaleX(0.3333)
	}
}

.ui-border-r:before {
	border-right: 1px solid #e9e9e9;
	content: '';
	display: block;
	bottom: 0;
	position: absolute;
	right: 0;
	top: 0;
	-webkit-transform-origin: right top
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-border-r:before {
		-webkit-transform: scaleX(0.5)
	}
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
	.ui-border-r:before {
		-webkit-transform: scaleX(0.3333)
	}
}

.ui-border-t,
.ui-border-l,
.ui-border-b,
.ui-border-r,
.ui-border-tb {
	position: relative
}

.ui-border-tb:before {
	border-top: 1px solid #e9e9e9;
	content: '';
	display: block;
	width: 100%;
	position: absolute;
	left: 0;
	top: 0;
	-webkit-transform-origin: left top
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-border-tb:before {
		-webkit-transform: scaleY(0.5)
	}
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
	.ui-border-tb:before {
		-webkit-transform: scaleY(0.3333)
	}
}

.ui-border-tb:after {
	border-bottom: 1px solid #e9e9e9;
	content: '';
	display: block;
	width: 100%;
	position: absolute;
	left: 0;
	bottom: 0;
	-webkit-transform-origin: left bottom
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-border-tb:after {
		-webkit-transform: scaleY(0.5)
	}
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
	.ui-border-tb:after {
		-webkit-transform: scaleY(0.3333)
	}
}

.ui-border:before {
	content: "";
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	border: 1px solid #e9e9e9;
	-webkit-transform-origin: 0 0;
	padding: 1px;
	-webkit-box-sizing: border-box;
	pointer-events: none;
	z-index: 10;
	pointer-events: none
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-border:before {
		width: 200%;
		height: 200%;
		-webkit-transform: scale(0.5)
	}
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
	.ui-border:before {
		width: 300%;
		height: 300%;
		-webkit-transform: scale(0.3333)
	}
}

.ui-border-radius:before {
	content: "";
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	border: 1px solid #e9e9e9;
	-webkit-transform-origin: 0 0;
	padding: 1px;
	-webkit-box-sizing: border-box;
	border-radius: 4px;
	pointer-events: none;
	z-index: 10
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-border-radius:before {
		width: 200%;
		height: 200%;
		-webkit-transform: scale(0.5);
		border-radius: 8px
	}
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {
	.ui-border-radius:before {
		width: 300%;
		height: 300%;
		-webkit-transform: scale(0.3333);
		border-radius: 12px
	}
}

.ui-list>li.ui-border-t:first-child:before,
.ui-list>li:first-child .ui-border-t:before,
.ui-list-active>li:active:before,
.ui-list>li.active:before,
.ui-list>li.active .ui-border-t:before,
.ui-list>li.active+li .ui-border-t:before,
.ui-list>li.active+li.ui-border-t:before {
	content: none
}

.ui-row {
	display: block;
	overflow: hidden
}

.ui-col {
	float: left;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 100%
}

.ui-col-10 {
	width: 10%
}

.ui-col-20 {
	width: 20%
}

.ui-col-25 {
	width: 25%
}

.ui-col-33 {
	width: 33.33%
}

.ui-col-50 {
	width: 50%
}

.ui-col-67 {
	width: 66.66%
}

.ui-col-75 {
	width: 75%
}

.ui-col-80 {
	width: 80%
}

.ui-col-90 {
	width: 90%
}

.ui-col-img {
	padding-top: 100%
}

.ui-row-flex {
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box
}

.ui-row-flex .ui-col {
	float: none;
	-webkit-box-flex: 1;
	width: 0
}

.ui-row-flex .ui-col-2 {
	-webkit-box-flex: 2
}

.ui-row-flex .ui-col-3 {
	-webkit-box-flex: 3
}

.ui-row-flex .ui-col-4 {
	-webkit-box-flex: 4
}

.ui-row-flex-ver {
	-webkit-box-orient: vertical
}

.ui-row-flex-ver .ui-col {
	width: 100%;
	height: 0
}

.ui-flex-center {
	width: 100%;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	text-align: center;
	height: 150px
}

.ui-flex,
.ui-tiled {
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box
}

.ui-flex-ver {
	-webkit-box-orient: vertical
}

.ui-flex-pack-start {
	-webkit-box-pack: start
}

.ui-flex-pack-end {
	-webkit-box-pack: end
}

.ui-flex-pack-center {
	-webkit-box-pack: center
}

.ui-flex-align-start {
	-webkit-box-align: start
}

.ui-flex-align-end {
	-webkit-box-align: end
}

.ui-flex-align-center {
	-webkit-box-align: center
}

.ui-flex>li,
.ui-tiled>li {
	-webkit-box-flex: 1;
	width: 100%;
	text-align: center;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center
}

.ui-img-vertical,
.ui-img-horizontal,
.ui-img,
.ui-img-banner,
.ui-img-cover,
.ui-img-icon {
	position: relative;
	width: 100%;
	overflow: hidden
}

.ui-img-vertical>span,
.ui-img-horizontal>span,
.ui-img>span,
.ui-img-banner>span,
.ui-img-cover>span,
.ui-img-icon>span {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	background-repeat: no-repeat;
	-webkit-background-size: cover
}

.ui-img-vertical>span,
.ui-img-horizontal>span,
.ui-img>span,
.ui-img-banner>span,
.ui-img-cover>span,
.ui-img-icon>span {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-background-clip: content-box;
	background-clip: content-box
}

.ui-img-vertical>img,
.ui-img-horizontal>img,
.ui-img>img,
.ui-img-banner>img,
.ui-img-cover>img,
.ui-img-icon>img {
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0
}

.ui-img-vertical.active,
.ui-img-horizontal.active,
.ui-img.active,
.ui-img-banner.active,
.ui-img-cover.active,
.ui-img-icon.active {
	opacity: .5
}

.ui-img {
	width: 100%;
	padding-top: 100%
}

.ui-img-vertical {
	width: 100%;
	padding-top: 142.8%
}

.ui-img-horizontal {
	width: 100%;
	padding-top: 56.25%
}

.ui-img-banner {
	width: 100%;
	padding-top: 53.33%
}

.ui-img-cover {
	width: 100%;
	padding-top: 30.94%
}

.ui-justify {
	text-align: justify;
	font-size: 0
}

.ui-justify:after {
	content: '';
	display: inline-block;
	width: 100%;
	height: 0;
	overflow: hidden
}

.ui-justify li {
	display: inline-block;
	text-align: center
}

.ui-justify p {
	font-size: 16px
}

.ui-justify-flex {
	width: 100%;
	display: -webkit-box;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between
}

.ui-header,
.ui-footer {
	position: fixed;
	width: 100%;
	z-index: 100;
	left: 0
}

.ui-header {
	top: 0;
	height: 45px;
	line-height: 45px
}

.ui-header-stable,
.ui-header-positive {
	padding: 0 10px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-header-stable,
.ui-footer-stable {
	background-color: #f2f2f2
}

.ui-header-positive,
.ui-footer-positive {
	background-color: #18b4ed;
	color: #fff
}

.ui-header-positive a,
.ui-header-positive a:active,
.ui-header-positive i,
.ui-footer-positive a,
.ui-footer-positive a:active,
.ui-footer-positive i {
	color: #fff
}

.ui-footer-btn {
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #f9f9f9), to(#e0e0e0));
	color: #00a5e0
}

.ui-footer-btn .ui-tiled {
	height: 100%
}

.ui-footer {
	bottom: 0;
	height: 55px
}

.ui-header~.ui-container {
	border-top: 45px solid transparent
}

.ui-footer~.ui-container {
	border-bottom: 55px solid transparent
}

.ui-header h1 {
	text-align: center;
	font-size: 18px
}

.ui-header .ui-btn {
	display: block;
	position: absolute;
	right: 10px;
	top: 50%;
	margin-top: -15px
}

.ui-nowrap {
	max-width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis
}

.ui-nowrap-flex {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	-webkit-box-flex: 1;
	height: inherit
}

.ui-nowrap-multi {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2
}

.ui-placehold-wrap {
	padding-top: 31.25%;
	position: relative
}

.ui-placehold {
	color: #bbb;
	position: absolute;
	top: 0;
	width: 100%;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	-webkit-box-sizing: border-box;
	text-align: center;
	height: 100%;
	z-index: -1
}

.ui-placehold-img {
	padding-top: 31.25%;
	position: relative
}

.ui-placehold-img>span {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	background-repeat: no-repeat;
	-webkit-background-size: cover
}

.ui-placehold-img img {
	width: 100%;
	height: 100%
}

.ui-whitespace {
	padding-left: 12px;
	padding-right: 12px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-whitespace-left {
	padding-left: 12px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-whitespace-right {
	padding-right: 12px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-actionsheet {
	position: fixed;
	z-index: 9999;
	top: 0;
	left: 0;
	display: -webkit-box;
	width: 100%;
	height: 100%;
	pointer-events: none;
	opacity: 0;
	background: rgba(0, 0, 0, 0.4);
	-webkit-box-orient: horizontal;
	-webkit-box-pack: center;
	-webkit-box-align: end
}

.ui-actionsheet:active {
	opacity: .84
}

.ui-actionsheet .ui-actionsheet-cnt {
	position: fixed;
	bottom: 0;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 100%;
	-webkit-transition: all 0.2s linear;
	-webkit-transform: translate3d(0, 100%, 0);
	text-align: center;
	font-size: 21px
}

.ui-actionsheet .ui-actionsheet-cnt button {
	font-size: 18px
}

.ui-actionsheet button,
.ui-actionsheet h4 {
	display: block;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 100%;
	background: #fff;
	font-size: 14px
}

.ui-actionsheet h4 {
	color: #777
}

.ui-actionsheet button {
	color: #000;
	height: 50px;
	line-height: 50px
}

.ui-actionsheet button:not(:last-child) {
	border-top: 1px #e9e9e9 solid
}

.ui-actionsheet h4 {
	padding: 13px 20px;
	line-height: 24px
}

.ui-actionsheet.show {
	pointer-events: inherit;
	opacity: 1
}

.ui-actionsheet.show .ui-actionsheet-cnt {
	-webkit-transition: all 0.2s linear;
	-webkit-transform: translate3d(0, 0, 0)
}

.ui-actionsheet-split-line {
	height: 10px;
	background-color: rgba(255, 255, 255, 0.7)
}

.ui-actionsheet .ui-actionsheet-del {
	color: #fd472b
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-actionsheet button:not(:last-child) {
		border: 0;
		background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.5, transparent), color-stop(0.5, #e9e9e9), to(#e9e9e9));
		background-repeat: repeat-x;
		background-position: left top;
		-webkit-background-size: 100% 1px
	}
}

.ui-btn-s,
.ui-btn,
.ui-btn-lg,
.ui-btn-lg-nowhole {
	position: relative;
	text-align: center;
	background-color: #fff;
	vertical-align: top;
	color: #000;
	-webkit-box-sizing: border-box;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 1px solid #c3c8cc;
	border: 1px solid #c3c8cc;
	border-radius: 3px
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {

	.ui-btn-s,
	.ui-btn,
	.ui-btn-lg,
	.ui-btn-lg-nowhole {
		position: relative;
		border: 0
	}

	.ui-btn-s:before,
	.ui-btn:before,
	.ui-btn-lg:before,
	.ui-btn-lg-nowhole:before {
		content: "";
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #c3c8cc;
		-webkit-transform: scale(0.5);
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		border-radius: 6px;
		pointer-events: none
	}
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {

	.ui-btn-s,
	.ui-btn,
	.ui-btn-lg,
	.ui-btn-lg-nowhole {
		position: relative;
		border: 0
	}

	.ui-btn-s:before,
	.ui-btn:before,
	.ui-btn-lg:before,
	.ui-btn-lg-nowhole:before {
		content: "";
		width: 300%;
		height: 300%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #c3c8cc;
		-webkit-transform: scale(0.3333);
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		border-radius: 6px;
		pointer-events: none
	}
}

.ui-btn-s:not(.disabled):not(:disabled):active,
.ui-btn-s.active,
.ui-btn:not(.disabled):not(:disabled):active,
.ui-btn.active,
.ui-btn-lg:not(.disabled):not(:disabled):active,
.ui-btn-lg.active,
.ui-btn-lg-nowhole:not(.disabled):not(:disabled):active,
.ui-btn-lg-nowhole.active {
	background-color: #f0f0f0;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border-color: #D0D1D2
}

.ui-btn-s:after,
.ui-btn:after,
.ui-btn-lg:after,
.ui-btn-lg-nowhole:after {
	content: "";
	position: absolute;
	top: -7px;
	bottom: -7px;
	left: 0;
	right: 0
}

.ui-btn-s.disabled,
.ui-btn-s:disabled,
.ui-btn.disabled,
.ui-btn:disabled,
.ui-btn-lg.disabled,
.ui-btn-lg:disabled,
.ui-btn-lg-nowhole.disabled,
.ui-btn-lg-nowhole:disabled {
	border: 0;
	color: #bbb;
	background: #e9ebec;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn {
	height: 30px;
	line-height: 30px;
	padding: 0px 16px;
	display: block;
	text-align: center;
	font-size: 14px;
	border-radius: 2px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	overflow: hidden
}

.ui-btn-s {
	width: 60px;
	height: 30px;
	line-height: 30px;
	display: block;
	text-align: center;
	font-size: 14px;
	border-radius: 2px;
	padding: 0px;
	overflow: hidden;
	white-space: nowrap
}

.ui-btn-highlight {
	background-color: transparent;
	color: #fff
}

.ui-btn-highlight:not(.disabled):not(:disabled):active,
.ui-btn-highlight.active {
	background-color: rgba(255, 255, 255, 0.3);
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-highlight.disabled,
.ui-btn-highlight:disabled {
	font-size: 14px;
	color: #a9a9a9;
	background-color: #8d8d8d;
	letter-spacing: 0px;
	line-height: 30px;
	border: 0
}

.ui-btn-lg {
	font-size: 17px;
	height: 40px;
	line-height: 40px;
	display: block;
	text-align: center;
	width: 100%;
	border-radius: 3px
}

.ui-btn-primary {
	border: 0;
	background-color: #12B7F5;
	color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-primary:not(.disabled):not(:disabled):active,
.ui-btn-primary.active {
	background: #0E98CB;
	color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-danger {
	border: 0;
	background-color: #FB6155;
	color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-danger:not(.disabled):not(:disabled):active,
.ui-btn-danger.active {
	background: #CC3428;
	color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-highlight {
	background-color: transparent
}

.ui-btn-lg-nowhole {
	display: block;
	text-align: center;
	width: 220px;
	height: 40px;
	line-height: 40px;
	font-size: 17px;
	border-radius: 3px;
	margin: auto
}

.ui-btn-wrap {
	padding: 10px 12px;
	display: -webkit-box;
	-webkit-box-pack: center
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {

	.ui-btn,
	.ui-btn-lg,
	.ui-btn-s,
	.ui-btn-lg-nowhole {
		border: 0
	}

	.ui-btn.disabled:before,
	.ui-btn:disabled:before,
	.ui-btn-lg.disabled:before,
	.ui-btn-lg:disabled:before,
	.ui-btn-s.disabled:before,
	.ui-btn-s:disabled:before,
	.ui-btn-lg-nowhole.disabled:before,
	.ui-btn-lg-nowhole:disabled:before {
		content: none
	}

	.ui-btn-primary:before,
	.ui-btn-danger:before {
		content: none
	}
}

.ui-btn-progress {
	width: 60px;
	padding: 0
}

.ui-btn-progress .ui-btn-inner {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	overflow: hidden;
	background-color: #18b4ed;
	border-bottom-left-radius: 2px;
	border-top-left-radius: 2px;
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center
}

.ui-btn-progress .ui-btn-inner span {
	display: block;
	color: #000;
	width: 60px;
	line-height: normal
}

.ui-btn-progress.disabled,
.ui-btn-progress:disabled {
	color: #bbb;
	background: #e9ebec;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-progress.disabled .ui-btn-inner,
.ui-btn-progress:disabled .ui-btn-inner {
	background-color: #e9ebec
}

.ui-btn-progress.disabled .ui-btn-inner span,
.ui-btn-progress:disabled .ui-btn-inner span {
	color: #bbb
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {

	.ui-btn-progress.disabled,
	.ui-btn-progress:disabled {
		border: 0
	}

	.ui-btn-progress.disabled:before,
	.ui-btn-progress:disabled:before {
		content: none
	}
}

.ui-btn-group {
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-box-align: center
}

.ui-btn-group button {
	display: block;
	-webkit-box-flex: 1;
	margin-right: 10px
}

.ui-btn-group button:first-child {
	margin-left: 10px
}

.ui-dialog {
	position: fixed;
	top: 0px;
	left: 0px;
	width: 100%;
	height: 100%;
	z-index: 9999;
	-webkit-box-orient: horizontal;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	background: rgba(0, 0, 0, 0.4);
	display: none
}

.ui-dialog.show {
	display: -webkit-box
}

.ui-dialog-cnt {
	border-radius: 3px;
	width: 296px;
	-webkit-background-clip: padding-box;
	pointer-events: auto;
	background-color: #fff;
	position: relative;
	border-top: 4px solid #12B7F5;
	-webkit-box-sizing: border-box;
	color: #000
}

.ui-dialog-hd {
	width: 100%;
	text-align: center;
	min-height: 45px;
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	overflow: hidden;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px
}

.ui-dialog-bd {
	min-height: 46px;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
	padding: 18px 28px 18px 28px;
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	-webkit-box-orient: vertical
}

.ui-dialog-bd h2,
.ui-dialog-bd h3 {
	font-size: 20px;
	width: auto;
	margin: 1px auto 5px
}

.ui-dialog-bd>p {
	font-size: 16px;
	width: auto;
	margin: 2px auto
}

.ui-dialog-ft {
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
	display: -webkit-box;
	width: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-box-align: center;
	border-top: 1px solid #e9e9e9;
	height: 45px;
	line-height: 45px
}

.ui-dialog-ft button {
	font-size: 18px;
	text-align: center;
	width: 100%;
	line-height: 45px;
	display: block;
	margin: 0;
	-webkit-box-flex: 1
}

.ui-dialog-ft button:active {
	background-color: rgba(0, 0, 0, 0.1)
}

.ui-dialog-ft button:first-child {
	border-bottom-left-radius: 3px
}

.ui-dialog-ft button:last-child {
	border-bottom-right-radius: 3px
}

.ui-dialog-ft button:first-child:nth-last-child(2) {
	position: relative
}

.ui-dialog-ft button:first-child:nth-last-child(2):after {
	content: "";
	position: absolute;
	right: 0;
	top: 0;
	display: block;
	width: 1px;
	height: 100%;
	border-right: 1px #e9e9e9 solid
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-dialog-ft {
		position: relative;
		border: 0;
		background-position: left top;
		background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.5, transparent), color-stop(0.5, #e9e9e9));
		background-repeat: repeat-x;
		-webkit-background-size: 100% 1px
	}

	.ui-dialog-ft button:first-child:nth-last-child(2):after {
		content: "";
		position: absolute;
		right: 0;
		top: 0;
		display: block;
		width: 1px;
		height: 100%;
		border-right: 0;
		background-position: right top;
		background-image: -webkit-gradient(linear, left top, right top, color-stop(0.5, transparent), color-stop(0.5, #e9e9e9));
		background-repeat: repeat-y;
		-webkit-background-size: 1px 100%
	}
}

.ui-dialog-function .ui-dialog-bd {
	padding: 18px 20px 24px;
	-webkit-box-align: inherit
}

.ui-dialog-function .ui-dialog-item {
	margin-top: 6px;
	background-color: #fff;
	width: 100%;
	display: -webkit-box
}

.ui-dialog-function .ui-img {
	width: 70px;
	height: 70px;
	padding-top: 0;
	margin-right: 10px
}

.ui-dialog-function .ui-dialog-info {
	-webkit-box-flex: 1;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center
}

.ui-dialog-function .ui-dialog-info>p {
	color: #777
}

.ui-dialog-function .ui-dialog-info>span {
	display: block;
	font-size: 14px;
	color: #777
}

.ui-dialog-operate .ui-dialog-ft button:first-child:nth-last-child(2):after {
	content: "";
	position: absolute;
	top: -7px;
	bottom: -7px;
	left: 0;
	right: 0;
	width: auto;
	height: auto;
	border: 0;
	background-image: none !important
}

.ui-dialog-operate .ui-dialog-cnt {
	width: 320px;
	border-radius: 3px;
	border: 0;
	margin-top: -30px
}

.ui-dialog-operate .ui-img {
	width: 100%;
	height: 150px;
	padding-top: 0
}

.ui-dialog-operate .ui-dialog-bd {
	padding: 19px 16px;
	min-height: 0
}

.ui-dialog-operate h3 {
	margin: 0
}

.ui-dialog-operate h3+p {
	margin-top: 7px
}

.ui-dialog-operate p {
	margin: 0 auto
}

.ui-dialog-operate .ui-dialog-ft {
	padding: 0 16px 16px 16px;
	border: none;
	height: auto;
	line-height: auto;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0
}

.ui-dialog-operate .ui-dialog-ft button {
	height: 40px;
	line-height: 40px
}

.ui-dialog-operate .ui-dialog-ft .ui-btn:first-child {
	margin-right: 5px
}

.ui-dialog-operate .ui-dialog-ft .ui-btn:last-child {
	margin-left: 5px
}

.ui-dialog-operate-icon .ui-dialog-hd {
	padding: 30px 0 23px 0
}

.ui-dialog-operate-icon .ui-dialog-bd {
	padding-top: 0;
	min-height: 0
}

.ui-dialog-operate-icon .ui-img {
	width: 210px;
	height: 110px;
	margin: 0 auto
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-dialog-operate .ui-dialog-ft {
		background-image: none
	}
}

.ui-dialog-close {
	position: absolute;
	bottom: -80px;
	left: 50%;
	width: 40px;
	height: 40px;
	margin-left: -20px
}

.ui-dialog-close:before {
	font-family: "icon-min" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5);
	font-size: 38px;
	content: "飥�";
	color: #fff;
	display: block;
	line-height: 40px;
	position: absolute;
	bottom: 0;
	left: 0
}

.ui-dialog-close.active,
.ui-dialog-close:active {
	opacity: .5
}

@media screen and (max-width: 320px) {
	.ui-dialog-operate .ui-dialog-cnt {
		width: 270px;
		margin-top: -24px
	}

	.ui-dialog-operate .ui-img {
		height: 127px
	}

	.ui-dialog-operate .ui-dialog-bd {
		padding: 12px 16px
	}

	.ui-dialog-operate h3 {
		font-size: 16px
	}

	.ui-dialog-operate h3+p {
		margin-top: 5px
	}

	.ui-dialog-operate p {
		font-size: 14px
	}

	.ui-dialog-operate .ui-dialog-ft button {
		font-size: 16px
	}

	.ui-dialog-close {
		bottom: -48px
	}

	.ui-dialog-operate-icon .ui-img {
		height: 110px
	}

	.ui-dialog-operate-icon .ui-dialog-hd {
		padding: 18px 0 15px 0
	}

	.ui-dialog-operate-icon .ui-dialog-bd {
		padding-top: 0;
		padding-bottom: 16px
	}
}

.ui-form {
	background-color: #fff
}

.ui-form-item {
	position: relative;
	font-size: 18px;
	height: 46px;
	line-height: 46px;
	padding-right: 12px;
	padding-left: 12px;
	display: -webkit-box;
	-webkit-box-orient: horizontal
}

.ui-form-item label:not(.ui-switch):not(.ui-checkbox):not(.ui-checkbox-s):not(.ui-radio) {
	text-align: left;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	display: block
}

.ui-form-item input {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-appearance: none;
	border: 0;
	background: none;
	padding-left: 24px
}

.ui-form-item input::-webkit-input-placeholder {
	color: #bbb
}

.ui-form-item textarea {
	width: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-appearance: none;
	border: 0;
	background: none;
	padding-left: 95px
}

.ui-form-item input[type="checkbox"],
.ui-form-item input[type="radio"] {
	padding-left: 0
}

.ui-form-item .ui-icon-close {
	position: absolute;
	top: 0;
	right: 12px
}

@media (max-width: 320px) {
	.ui-form-item {
		padding-left: 12px;
		padding-right: 12px
	}
}

.ui-form .ui-btn-wrap {
	padding: 27px 12px
}

.ui-form-item-link:after {
	content: "";
	display: block;
	position: absolute;
	right: 12px;
	top: 50%;
	margin-top: -7px;
	width: 8px;
	height: 14px;
	background: url(//i.gtimg.cn/vipstyle/qui/2.0.0/img/icon_arrowlink.png) no-repeat;
	-webkit-background-size: 100% auto;
	background-size: 100% auto
}

.ui-form-item-pure input {
	padding-left: 0
}

.ui-form-tips {
	text-align: right;
	padding-right: 12px;
	margin-top: 8px;
	font-size: 14px;
	color: #777
}

.ui-form-item-textarea {
	height: 65px
}

.ui-form-item-textarea label {
	vertical-align: top
}

.ui-form-item-textarea textarea {
	margin-top: 15px;
	border: none
}

.ui-form-item-textarea textarea:focus {
	outline: none
}

.ui-form-item-textarea textarea .ui-form-item-link>li:after {
	content: "";
	display: block;
	position: absolute;
	right: 12px;
	top: 50%;
	margin-top: -7px;
	width: 8px;
	height: 14px;
	background: url(//i.gtimg.cn/vipstyle/qui/2.0.0/img/icon_arrowlink.png) no-repeat;
	-webkit-background-size: 100% auto;
	background-size: 100% auto
}

.ui-form-item-l label,
.ui-form-item-r button {
	color: #00a5e0;
	text-align: center
}

.ui-form-item-r .ui-icon-close {
	right: 125px
}

.ui-form-item-l input:not([type="checkbox"]):not([type="radio"]) {
	padding-left: 115px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-form-item-r {
	padding-right: 0
}

.ui-form-item-r input:not([type="checkbox"]):not([type="radio"]) {
	padding-left: 0;
	padding-right: 150px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-form-item-r button {
	width: 110px;
	height: 46px;
	position: absolute;
	top: 0;
	right: 0
}

.ui-form-item-r button.disabled {
	color: #bbb
}

.ui-form-item-r button:not(.disabled):active {
	background-color: #f2f2f2
}

.ui-form-item-pure textarea {
	padding-left: 0
}

.ui-form-item-show label {
	color: #777
}

.ui-form-item-link:after {
	content: "";
	display: block;
	position: absolute;
	right: 12px;
	top: 50%;
	margin-top: -7px;
	width: 8px;
	height: 14px;
	background: url(//i.gtimg.cn/vipstyle/qui/2.0.0/img/icon_arrowlink.png) no-repeat;
	-webkit-background-size: 100% auto;
	background-size: 100% auto
}

.ui-form-item-checkbox,
.ui-form-item-radio,
.ui-form-item-switch {
	display: -webkit-box;
	-webkit-box-align: center
}

.ui-form-item .ui-icon-close {
	font-size: 13px;
	color: #808080
}

.ui-form-item-link:after {
	font-size: 13px
}

@font-face {
	font-family: "icon-checkbox";
	src: url(data:font/ttf;charset=utf-8;base64,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) format("truetype")
}

.ui-checkbox,
.ui-checkbox-s {
	display: inline-block
}

.ui-checkbox input,
.ui-checkbox-s input {
	display: inline-block;
	width: 25px;
	height: 1px;
	position: relative;
	overflow: visible;
	border: 0;
	background: none;
	-webkit-appearance: none;
	outline: none;
	margin-right: 8px;
	vertical-align: middle
}

.ui-checkbox input:before,
.ui-checkbox-s input:before {
	font-family: "icon-checkbox" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5);
	content: "飫�";
	color: #18b4ed;
	position: absolute;
	top: -22px;
	left: -4px;
	color: #dedfe0
}

.ui-checkbox input:checked:before,
.ui-checkbox-s input:checked:before {
	content: "飫€";
	color: #18b4ed
}

.ui-checkbox-s input:before {
	content: "飫�"
}

.ui-checkbox-s input:checked:before {
	content: "飫�"
}

.ui-input-wrap {
	background-color: #f7f7f8;
	height: 46px;
	display: -webkit-box;
	-webkit-box-align: center
}

.ui-input-wrap .ui-btn,
.ui-input-wrap i {
	margin-right: 10px
}

.ui-input {
	height: 30px;
	line-height: 30px;
	margin-left: 12px;
	margin-right: 9px;
	background: #e6e6e6;
	padding-left: 10px;
	-webkit-box-flex: 1
}

.ui-input input::-webkit-input-placeholder {
	color: #777
}

.ui-input input {
	width: 100%;
	height: 100%;
	border: 0;
	background: 0 0;
	-webkit-appearance: none;
	outline: 0
}

.ui-input-text {
	background: #fff
}

.ui-label,
.ui-label-list li {
	display: inline-block;
	position: relative;
	line-height: 30px;
	height: 30px;
	padding: 0 15px;
	border: 1px solid #cacccd;
	border-radius: 15px
}

.ui-label:active,
.ui-label-list li:active {
	opacity: 0.5
}

.ui-label-list {
	margin: 0 10px
}

.ui-label-list .ui-label {
	margin: 0 10px 10px 0
}

.ui-label-s {
	font-size: 11px;
	line-height: 11px;
	display: inline-block;
	position: relative;
	padding: 2px 3px;
	color: #ff7f0d;
	vertical-align: middle;
	border: 1px solid #ff7f0d;
	border-radius: 2px
}

.ui-label-s:active {
	background-color: #f3f2f2
}

.ui-label-s:after {
	content: "";
	position: absolute;
	top: -5px;
	bottom: -5px;
	left: -5px;
	right: -5px
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-label-s {
		border: 0
	}

	.ui-label-s:before {
		content: "";
		width: 200%;
		height: 200%;
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #ff7f0d;
		-webkit-transform: scale(0.5);
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		border-radius: 4px;
		pointer-events: none
	}

	.ui-label,
	.ui-label-list li {
		border: 0
	}

	.ui-label:after,
	.ui-label-list li:after {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #cacccd;
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		pointer-events: none;
		z-index: 10;
		width: 200%;
		height: 200%;
		-webkit-transform: scale(0.5);
		border-radius: 30px
	}
}

@media screen and (-webkit-min-device-pixel-ratio: 3) {

	.ui-label,
	.ui-label-list li {
		border: 0
	}

	.ui-label:after,
	.ui-label-list li:after {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		border: 1px solid #cacccd;
		-webkit-transform-origin: 0 0;
		padding: 1px;
		-webkit-box-sizing: border-box;
		pointer-events: none;
		z-index: 10;
		width: 300%;
		height: 300%;
		-webkit-transform: scale(0.3333);
		border-radius: 45px
	}
}

.ui-radio {
	line-height: 25px;
	display: inline-block
}

.ui-radio input {
	display: inline-block;
	width: 26px;
	height: 26px;
	position: relative;
	overflow: visible;
	border: 0;
	background: none;
	-webkit-appearance: none;
	outline: none;
	margin-right: 8px;
	vertical-align: middle
}

.ui-radio input:before {
	content: '';
	display: block;
	width: 24px;
	height: 24px;
	border: 1px solid #dfe0e1;
	border-radius: 13px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	position: absolute;
	left: 0px;
	top: 0
}

.ui-radio input:checked:after {
	content: '';
	display: block;
	width: 14px;
	height: 14px;
	background: #18b4ed;
	border-radius: 7px;
	position: absolute;
	left: 6px;
	top: 6px
}

.ui-select {
	position: relative;
	margin-right: 6px
}

.ui-select select {
	-webkit-appearance: none;
	border: 0;
	background: none;
	width: 100%;
	padding-right: 14px
}

.ui-select:after {
	position: absolute;
	top: 50%;
	right: 0;
	margin-top: -4px;
	width: 0;
	height: 0;
	border-top: 6px solid;
	border-right: 5px solid transparent;
	border-left: 5px solid transparent;
	color: #a6a6a6;
	content: "";
	pointer-events: none
}

.ui-select-group {
	margin-left: 95px;
	overflow: hidden
}

.ui-select-group .ui-select {
	float: left
}

.ui-form-item>.ui-select {
	margin-left: 95px
}

.ui-switch {
	position: absolute;
	font-size: 14px;
	right: 12px;
	top: 50%;
	margin-top: -16px;
	width: 52px;
	height: 32px;
	line-height: 32px
}

.ui-switch input {
	width: 52px;
	height: 32px;
	position: absolute;
	z-index: 2;
	border: none;
	background: none;
	-webkit-appearance: none;
	outline: none
}

.ui-switch input:before {
	content: '';
	width: 50px;
	height: 30px;
	border: 1px solid #dfdfdf;
	background-color: #fdfdfd;
	border-radius: 20px;
	cursor: pointer;
	display: inline-block;
	position: relative;
	vertical-align: middle;
	-webkit-box-sizing: content-box;
	border-color: #dfdfdf;
	-webkit-box-shadow: #dfdfdf 0px 0px 0px 0px inset;
	-webkit-transition: border 0.4s, -webkit-box-shadow 0.4s;
	-webkit-background-clip: content-box
}

.ui-switch input:checked:before {
	border-color: #64bd63;
	-webkit-box-shadow: #64bd63 0px 0px 0px 16px inset;
	background-color: #64bd63;
	-webkit-transition: border 0.4s, -webkit-box-shadow 0.4s, background-color 1.2s;
	background-color: #64bd63
}

.ui-switch input:checked:after {
	left: 21px
}

.ui-switch input:after {
	content: '';
	width: 30px;
	height: 30px;
	position: absolute;
	top: 1px;
	left: 0;
	border-radius: 100%;
	background-color: #fff;
	-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
	-webkit-transition: left 0.2s
}

.ui-feeds {
	width: 100%;
	overflow: hidden
}

.ui-feeds>ul {
	height: auto;
	overflow: hidden;
	margin: 0 -1px
}

.ui-feeds>ul>li {
	display: block;
	height: auto;
	float: left;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	padding: 0 1px;
	position: relative
}

.ui-feeds>ul>li>span {
	display: block;
	width: 100%;
	padding-top: 100%;
	background-position: center;
	-webkit-background-size: 100% auto;
	background-size: 100% auto;
	background-color: #ccc
}

.ui-feeds>ul>li:first-child:nth-last-child(1),
.ui-feeds>ul>li:first-child:nth-last-child(1)~li {
	width: 100%;
	max-height: 3.75rem;
	max-height: 100vw
}

.ui-feeds>ul>li:first-child:nth-last-child(1) img,
.ui-feeds>ul>li:first-child:nth-last-child(1)~li img {
	display: block;
	width: 100%;
	height: auto
}

.ui-feeds>ul>li:first-child:nth-last-child(2),
.ui-feeds>ul>li:first-child:nth-last-child(2)~li {
	width: 50%
}

.ui-feeds>ul>li:first-child:nth-last-child(3),
.ui-feeds>ul>li:first-child:nth-last-child(3)~li {
	width: 50%
}

.ui-feeds>ul>li:first-child:nth-last-child(3) {
	width: 100%;
	padding-bottom: 2px
}

.ui-feeds>ul>li:first-child:nth-last-child(3):after {
	content: "";
	position: absolute;
	bottom: 0;
	height: 2px;
	width: 100%;
	background-color: #fff;
	z-index: 99
}

.ui-feeds>ul>li:first-child:nth-last-child(3)>span {
	padding-top: 50%
}

.ui-feeds>ul>li:first-child:nth-last-child(3)~li {
	width: 50%
}

.ui-feeds>ul>li:first-child:nth-last-child(3)~li>span {
	padding-top: 100%;
	position: relative
}

.ui-feeds>ul>li:first-child:nth-last-child(4),
.ui-feeds>ul>li:first-child:nth-last-child(4)~li {
	width: 50%
}

.ui-feeds>ul>li:first-child:nth-last-child(4) {
	width: 100%;
	padding-bottom: 2px
}

.ui-feeds>ul>li:first-child:nth-last-child(4):after {
	content: "";
	position: absolute;
	bottom: 0;
	height: 2px;
	width: 100%;
	background-color: #fff;
	z-index: 99
}

.ui-feeds>ul>li:first-child:nth-last-child(4)>span {
	padding-top: 50%
}

.ui-feeds>ul>li:first-child:nth-last-child(4)~li {
	width: 33.3%
}

.ui-feeds>ul>li:first-child:nth-last-child(4)~li>span {
	padding-top: 100%
}

.ui-feeds>ul>li:first-child:nth-last-child(5),
.ui-feeds>ul>li:first-child:nth-last-child(5)~li:nth-child(2) {
	width: 50%;
	padding-bottom: 2px
}

.ui-feeds>ul>li:first-child:nth-last-child(5):after,
.ui-feeds>ul>li:first-child:nth-last-child(5)~li:nth-child(2):after {
	content: "";
	position: absolute;
	bottom: 0;
	height: 2px;
	width: 100%;
	background-color: #fff;
	z-index: 99
}

.ui-feeds>ul>li:first-child:nth-last-child(5)>span,
.ui-feeds>ul>li:first-child:nth-last-child(5)~li:nth-child(2)>span {
	padding-top: 100%
}

.ui-feeds>ul>li:first-child:nth-last-child(5)~li {
	width: 33.3%
}

.ui-feeds>ul>li:first-child:nth-last-child(5)~li>span {
	padding-top: 100%
}

.ui-feeds>ul>li:first-child:nth-last-child(6) {
	width: 66.666%;
	padding-bottom: 1px
}

.ui-feeds>ul>li:first-child:nth-last-child(6):after {
	content: "";
	position: absolute;
	bottom: 0;
	height: 2px;
	width: 100%;
	background-color: #fff;
	z-index: 99
}

.ui-feeds>ul>li:first-child:nth-last-child(6)>span {
	padding-top: 100.3%
}

.ui-feeds>ul>li:first-child:nth-last-child(6)~li {
	width: 33.3%
}

.ui-feeds>ul>li:first-child:nth-last-child(6)~li>span {
	padding-top: 100%
}

.ui-feeds>ul>li:first-child:nth-last-child(6)~li:nth-child(2) {
	padding-bottom: 2px;
	width: 33.333%
}

.ui-feeds>ul>li:first-child:nth-last-child(6)~li:nth-child(2):after {
	content: "";
	position: absolute;
	bottom: 0;
	height: 2px;
	width: 100%;
	background-color: #fff;
	z-index: 99
}

.ui-feeds>ul>li:first-child:nth-last-child(6)~li:nth-child(2)>span {
	padding-top: 100.15%
}

.ui-feeds>ul>li:first-child:nth-last-child(6)~li:nth-child(3) {
	padding-bottom: 2px;
	width: 33.333%
}

.ui-feeds>ul>li:first-child:nth-last-child(6)~li:nth-child(3):after {
	content: "";
	position: absolute;
	bottom: 0;
	height: 2px;
	width: 100%;
	background-color: #fff;
	z-index: 99
}

.ui-feeds>ul>li:first-child:nth-last-child(6)~li:nth-child(3)>span {
	padding-top: 100.15%
}

.ui-feeds>ul>li:first-child:nth-last-child(6)~li:nth-child(4) {
	clear: both
}

.ui-feeds>ul>li:first-child:nth-last-child(7) {
	width: 100%;
	padding-bottom: 2px
}

.ui-feeds>ul>li:first-child:nth-last-child(7):after {
	content: "";
	position: absolute;
	bottom: 0;
	height: 2px;
	width: 100%;
	background-color: #fff;
	z-index: 99
}

.ui-feeds>ul>li:first-child:nth-last-child(7)>span {
	padding-top: 50%
}

.ui-feeds>ul>li:first-child:nth-last-child(7)~li:nth-child(2),
.ui-feeds>ul>li:first-child:nth-last-child(7)~li:nth-child(3),
.ui-feeds>ul>li:first-child:nth-last-child(7)~li:nth-child(4) {
	padding-bottom: 2px
}

.ui-feeds>ul>li:first-child:nth-last-child(7)~li:nth-child(2):after,
.ui-feeds>ul>li:first-child:nth-last-child(7)~li:nth-child(3):after,
.ui-feeds>ul>li:first-child:nth-last-child(7)~li:nth-child(4):after {
	content: "";
	position: absolute;
	bottom: 0;
	height: 2px;
	width: 100%;
	background-color: #fff;
	z-index: 99
}

.ui-feeds>ul>li:first-child:nth-last-child(7)~li {
	width: 33.3%
}

.ui-feeds>ul>li:first-child:nth-last-child(7)~li>span {
	padding-top: 100%
}

.ui-feeds>ul>li:first-child:nth-last-child(8),
.ui-feeds>ul>li:first-child:nth-last-child(8)~li:nth-child(2) {
	width: 50%;
	padding-bottom: 2px
}

.ui-feeds>ul>li:first-child:nth-last-child(8):after,
.ui-feeds>ul>li:first-child:nth-last-child(8)~li:nth-child(2):after {
	content: "";
	position: absolute;
	bottom: 0;
	height: 2px;
	width: 100%;
	background-color: #fff;
	z-index: 99
}

.ui-feeds>ul>li:first-child:nth-last-child(8) span,
.ui-feeds>ul>li:first-child:nth-last-child(8)~li:nth-child(2) span {
	padding-top: 100%
}

.ui-feeds>ul>li:first-child:nth-last-child(8)~li:nth-child(3),
.ui-feeds>ul>li:first-child:nth-last-child(8)~li:nth-child(4),
.ui-feeds>ul>li:first-child:nth-last-child(8)~li:nth-child(5) {
	padding-bottom: 2px
}

.ui-feeds>ul>li:first-child:nth-last-child(8)~li:nth-child(3):after,
.ui-feeds>ul>li:first-child:nth-last-child(8)~li:nth-child(4):after,
.ui-feeds>ul>li:first-child:nth-last-child(8)~li:nth-child(5):after {
	content: "";
	position: absolute;
	bottom: 0;
	height: 2px;
	width: 100%;
	background-color: #fff;
	z-index: 99
}

.ui-feeds>ul>li:first-child:nth-last-child(8)~li {
	width: 33.3%
}

.ui-feeds>ul>li:first-child:nth-last-child(8)~li>span {
	padding-top: 100%
}

.ui-feeds>ul>li:first-child:nth-last-child(9),
.ui-feeds>ul>li:first-child:nth-last-child(9)~li {
	width: 33.333%
}

.ui-feeds>ul>li:first-child:nth-last-child(9),
.ui-feeds>ul>li:first-child:nth-last-child(9)~li:nth-child(2),
.ui-feeds>ul>li:first-child:nth-last-child(9)~li:nth-child(3),
.ui-feeds>ul>li:first-child:nth-last-child(9)~li:nth-child(4),
.ui-feeds>ul>li:first-child:nth-last-child(9)~li:nth-child(5),
.ui-feeds>ul>li:first-child:nth-last-child(9)~li:nth-child(6) {
	padding-bottom: 2px
}

.ui-feeds>ul>li:first-child:nth-last-child(9):after,
.ui-feeds>ul>li:first-child:nth-last-child(9)~li:nth-child(2):after,
.ui-feeds>ul>li:first-child:nth-last-child(9)~li:nth-child(3):after,
.ui-feeds>ul>li:first-child:nth-last-child(9)~li:nth-child(4):after,
.ui-feeds>ul>li:first-child:nth-last-child(9)~li:nth-child(5):after,
.ui-feeds>ul>li:first-child:nth-last-child(9)~li:nth-child(6):after {
	content: "";
	position: absolute;
	bottom: 0;
	height: 2px;
	width: 100%;
	background-color: #fff;
	z-index: 99
}

.ui-grid-expand {
	position: relative;
	width: 100%;
	height: auto;
	overflow: hidden
}

.ui-grid {
	overflow: hidden
}

.ui-grid .ui-img-horizontal,
.ui-grid .ui-img-vertical,
.ui-grid .ui-img {
	margin-bottom: 2px
}

.ui-grid .ui-img-horizontal>span,
.ui-grid .ui-img-vertical>span,
.ui-grid .ui-img>span {
	padding: 0 2px 0 0
}

.ui-grid .ui-img-horizontal+.ui-grid-info,
.ui-grid .ui-img-vertical+.ui-grid-info,
.ui-grid .ui-img+.ui-grid-info {
	margin-top: -2px
}

.ui-grid ul {
	height: auto;
	margin: 0 -2px 0 0;
	overflow: hidden
}

.ui-grid li {
	float: left;
	position: relative
}

.ui-grid-pure ul {
	margin-bottom: -2px
}

.ui-grid-trisect>ul li {
	width: 33.3333%
}

.ui-grid-info {
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
	width: 100%
}

.ui-grid-info h4 {
	position: relative;
	padding: 6px 0 0 10px;
	margin-right: 1px;
	font-size: 14px;
	padding-left: 10px;
	padding-right: 10px;
	text-align: left;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-grid-info h4 span {
	display: inline-block;
	margin-left: 12px;
	color: #777
}

.ui-grid-info p {
	font-size: 12px;
	padding-left: 10px;
	padding-right: 10px;
	padding-bottom: 17px;
	color: #777
}

.ui-grid-bisect>ul li {
	width: 50%
}

.ui-grid-icon h4 {
	font-size: 16px
}

.ui-grid-icon h5 {
	font-size: 14px
}

.ui-grid-icon p {
	font-size: 12px;
	color: #9B9B9B
}

.ui-grid-icon>ul {
	display: -webkit-box;
	-webkit-box-orient: horizontal
}

.ui-grid-icon>ul li {
	width: 100%;
	-webkit-box-flex: 1;
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	-webkit-box-orient: vertical
}

.ui-grid-icon>ul li>h4,
.ui-grid-icon>ul li>h5,
.ui-grid-icon>ul li>p {
	text-align: center;
	width: auto;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis
}

.ui-grid-icon .ui-img-icon {
	width: 50px;
	height: 50px;
	margin-bottom: 6px
}

.ui-grid-icon-horizontal>ul li {
	-webkit-box-orient: horizontal
}

.ui-grid-icon-horizontal .ui-img-icon {
	width: 40px;
	height: 40px;
	margin-right: 6px;
	margin-bottom: 0
}

.ui-grid-icon-horizontal .ui-grid-info {
	width: auto;
	text-align: center;
	padding: 0
}

.ui-list {
	background-color: #fff;
	width: 100%
}

.ui-list>li {
	position: relative;
	margin-left: 12px;
	display: -webkit-box
}

.ui-list li.ui-arrowlink {
	padding-right: 12px
}

.ui-list li h4 {
	font-size: 18px;
	line-height: 24px
}

.ui-list:not(.ui-list-text) li>p,
.ui-list li>h5 {
	font-size: 14px;
	color: #777
}

.ui-list-thumb,
.ui-list-thumb-s,
.ui-list-icon,
.ui-list-icon-s {
	position: relative;
	margin: 8px 12px 8px 0px
}

.ui-list-thumb>span,
.ui-list-thumb-s>span,
.ui-list-icon>span,
.ui-list-icon-s>span {
	display: block;
	width: 100%;
	height: 100%;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	position: relative
}

.ui-list-thumb {
	width: 40px;
	height: 40px
}

.ui-list-thumb-s {
	width: 34px;
	height: 34px
}

.ui-list-single .ui-list-thumb-s {
	margin: 6px 12px 6px 0px
}

.ui-list .ui-avatar,
.ui-list .ui-avatar-s,
.ui-list .ui-avatar-lg {
	margin: 8px 12px 8px 0px
}

.ui-list-single .ui-avatar,
.ui-list-single .ui-avatar-s,
.ui-list-single .ui-avatar-lg {
	margin: 6px 12px 6px 0px
}

.ui-list-img,
.ui-list-img-square,
.ui-list-img-vertical,
.ui-list-img-horizontal {
	position: relative;
	margin: 8px 12px 8px 0;
	overflow: hidden
}

.ui-list-img>span,
.ui-list-img-square>span,
.ui-list-img-vertical>span,
.ui-list-img-horizontal>span {
	display: block;
	width: 100%;
	height: 100%;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	position: relative
}

.ui-list-nospace>li {
	margin-left: 0
}

.ui-list-nospace .ui-list-img,
.ui-list-nospace .ui-list-img-square,
.ui-list-nospace .ui-list-img-vertical,
.ui-list-nospace .ui-list-img-horizontal {
	margin: 1px 0 1px
}

.ui-list-nospace li:first-child .ui-list-img,
.ui-list-nospace li:first-child .ui-list-img-square,
.ui-list-nospace li:first-child .ui-list-img-vertical,
.ui-list-nospace li:first-child .ui-list-img-horizontal {
	margin-top: 0
}

.ui-list-nospace li:last-child .ui-list-img,
.ui-list-nospace li:last-child .ui-list-img-square,
.ui-list-nospace li:last-child .ui-list-img-vertical,
.ui-list-nospace li:last-child .ui-list-img-horizontal {
	margin-bottom: 0
}

.ui-list-nospace .ui-list-info {
	padding-left: 12px
}

.ui-list-img {
	width: 33.33%;
	height: auto;
	padding-right: 1px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-list-img-square {
	width: 94px;
	height: 94px
}

.ui-list-img-vertical {
	width: 108px;
	height: 154px
}

.ui-list-img-horizontal {
	width: 134px;
	height: 94px
}

.ui-list-function .ui-list-info {
	padding-right: 75px
}

.ui-list-function .ui-btn,
.ui-list-function .ui-btn-s {
	position: absolute;
	top: 50%;
	right: 12px;
	margin-top: -15px
}

.ui-list-function.ui-list-link .ui-list-info,
.ui-list-function.ui-list-link-iconfont .ui-list-info {
	padding-right: 90px
}

.ui-list-function.ui-list-link .ui-btn,
.ui-list-function.ui-list-link-iconfont .ui-btn {
	right: 30px
}

.ui-list-function li {
	-webkit-box-align: inherit
}

.ui-list-info {
	-webkit-box-flex: 1;
	padding-top: 8px;
	padding-bottom: 8px;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	padding-right: 12px
}

.ui-list-info p {
	color: #777;
	font-size: 14px
}

.ui-list-active>li:active,
.ui-list li.active {
	background-color: #f2f2f2;
	padding-left: 12px;
	margin-left: 0px;
	opacity: 1
}

.ui-list>li.ui-border-t:first-child,
.ui-list>li:first-child .ui-border-t,
.ui-list-active>li:active,
.ui-list>li.active,
.ui-list>li.active .ui-border-t,
.ui-list>li.active+li .ui-border-t,
.ui-list>li.active+li.ui-border-t {
	background-image: none;
	border-top-color: #f2f2f2
}

.ui-list-nospace>li.active {
	padding-left: 0
}

.ui-list-link>li:after {
	content: "";
	display: block;
	position: absolute;
	right: 12px;
	top: 50%;
	margin-top: -7px;
	width: 8px;
	height: 14px;
	background: url(//i.gtimg.cn/vipstyle/qui/2.0.0/img/icon_arrowlink.png) no-repeat;
	-webkit-background-size: 100% auto;
	background-size: 100% auto
}

.ui-list-link-iconfont>li:after {
	font-family: "icon-min" !important;
	font-size: 14px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: #bbb;
	content: "飥€";
	position: absolute;
	right: 12px;
	top: 50%;
	margin-top: -22.5px;
	width: 8px
}

.ui-list-link .ui-list-info,
.ui-list-link-iconfont .ui-list-info {
	padding-right: 30px
}

.ui-list-single>li {
	padding-top: 0;
	padding-bottom: 0
}

.ui-list-single .ui-list-info {
	-webkit-box-orient: horizontal;
	-webkit-box-align: center;
	padding-top: 11px;
	padding-bottom: 11px
}

.ui-list-single h4 {
	-webkit-box-flex: 1
}

.ui-list-pure>li {
	display: block
}

.ui-list-text>li,
.ui-list-pure>li {
	position: relative;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-right: 15px;
	-webkit-box-align: center
}

.ui-list-text h4,
.ui-list-text p {
	-webkit-box-flex: 1
}

.ui-panel {
	background-color: #fff;
	overflow: hidden;
	margin-bottom: 10px
}

.ui-panel h2,
.ui-panel>h3 {
	line-height: 55px;
	font-size: 18px;
	position: relative;
	overflow: hidden;
	display: -webkit-box;
	-webkit-box-align: center;
	padding-left: 12px;
	padding-right: 12px
}

.ui-panel h2 span,
.ui-panel>h3 span {
	display: -webkit-box
}

.ui-panel>h4 {
	font-size: 14px;
	color: #777;
	position: relative;
	line-height: 30px;
	padding-left: 12px;
	padding-right: 12px
}

.ui-panel-subtitle {
	font-size: 14px;
	color: #777;
	line-height: 1;
	letter-spacing: 2px;
	margin-right: -2px
}

.ui-panel-title-tips {
	font-size: 14px;
	color: #777;
	position: absolute;
	right: 12px;
	top: 0;
	height: 100%;
	display: -webkit-box;
	-webkit-box-align: center;
	letter-spacing: 0;
	font-weight: 400;
	text-indent: 0
}

.ui-arrowlink .ui-panel-title-tips {
	right: 26px
}

.ui-panel-more {
	height: 46px;
	line-height: 46px;
	text-align: center;
	margin: 0;
	padding: 0;
	font-size: 14px;
	color: #777;
	background-color: #fff
}

.ui-panel-more .ui-arrowlink {
	margin: 0;
	padding: 0;
	padding-right: 12px
}

.ui-panel-more .ui-arrowlink:after {
	width: 6px;
	height: 11px;
	background: url(//i.gtimg.cn/vipstyle/qui/2.0.0/img/icon_arrowlink_black.png) no-repeat;
	-webkit-background-size: 100% auto;
	background-size: 100% auto;
	margin-top: -6px;
	right: 0
}

.ui-panel-more .ui-arrowlink-iconfont {
	margin: 0;
	padding: 0;
	padding-right: 12px
}

.ui-panel-more .ui-arrowlink-iconfont:after {
	font-size: 11px;
	width: 6px;
	line-height: 46px;
	right: 0;
	margin-top: -22.5px;
	color: #777
}

.ui-panel-center h2,
.ui-panel-center>h3,
.ui-panel-center-multi h2,
.ui-panel-center-multi>h3 {
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	text-align: center;
	letter-spacing: 4px
}

.ui-panel-center h2 span,
.ui-panel-center>h3 span,
.ui-panel-center-multi h2 span,
.ui-panel-center-multi>h3 span {
	display: -webkit-box
}

.ui-panel-center h2,
.ui-panel-center>h3 {
	height: 55px;
	line-height: 55px
}

.ui-panel-center-multi h2,
.ui-panel-center-multi>h3 {
	height: 75px;
	line-height: 1.5
}

.ui-panel-center-multi>h2 span:nth-child(1) {
	font-size: 18px;
	line-height: 1;
	margin-bottom: 5px;
	letter-spacing: 4px;
	margin-right: -4px
}

.ui-panel-center-multi .ui-panel-subtitle {
	font-size: 14px;
	color: #777;
	line-height: 1;
	letter-spacing: 2px;
	margin-right: -2px
}

.ui-panel-pure h2,
.ui-panel-pure>h3,
.ui-panel-pure>h4 {
	font-size: 14px;
	line-height: 20px;
	padding: 15px 12px 5px;
	color: #777
}

.ui-list-pure>li {
	position: relative;
	padding-top: 8px;
	padding-bottom: 8px;
	padding-right: 12px;
	-webkit-box-align: center;
	display: block
}

.ui-icon-close {
	font-family: "icon-min" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5);
	font-size: 12px;
	color: #777
}

.ui-icon-close:before {
	content: "飥�"
}

.ui-icon-search {
	font-family: "icon-min" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5)
}

.ui-icon-search {
	font-size: 14px;
	color: #ccc
}

.ui-icon-search:before {
	content: "飥�"
}

.ui-searchbar-wrap {
	background: #fff;
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	height: 46px
}

.ui-searchbar-wrap button {
	margin-right: 10px
}

.ui-searchbar-wrap .ui-searchbar-cancel {
	color: #00a5e0;
	font-size: 14px;
	padding: 4px 8px
}

.ui-searchbar-wrap .ui-searchbar-input,
.ui-searchbar-wrap button,
.ui-searchbar-wrap .ui-icon-close {
	display: none
}

.ui-searchbar-wrap.focus {
	-webkit-box-pack: start
}

.ui-searchbar-wrap.focus button,
.ui-searchbar-wrap.focus .ui-searchbar-input,
.ui-searchbar-wrap.focus .ui-icon-close {
	display: block
}

.ui-searchbar-wrap.focus .ui-searchbar-text {
	display: none
}

.ui-searchbar {
	border-radius: 5px;
	margin: 0 10px;
	background: #f3f3f3;
	height: 30px;
	line-height: 30px;
	position: relative;
	padding: 0 12px;
	-webkit-box-flex: 1;
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	color: #bbb;
	font-size: 14px;
	width: 100%
}

.ui-searchbar input {
	-webkit-appearance: none;
	border: none;
	background: none;
	color: #000;
	width: 100%;
	padding: 4px 0
}

.ui-searchbar .ui-icon-search {
	line-height: 30px;
	margin-right: 4px
}

.ui-searchbar .ui-icon-close {
	line-height: 30px
}

.ui-searchbar-input {
	-webkit-box-flex: 1
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.ui-searchbar.ui-border-radius:before {
		border-radius: 10px
	}
}

.ui-tab {
	width: 100%;
	overflow: hidden
}

.ui-tab-nav {
	position: relative;
	top: 0px;
	width: 100%;
	background-color: #fff;
	display: box;
	display: -webkit-box;
	height: 40px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center
}

.ui-tab-nav li {
	text-align: center;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 100%;
	font-size: 14px;
	-webkit-box-flex: 1;
	position: relative
}

.ui-tab-nav li>p,
.ui-tab-nav li>span {
	position: relative;
	display: inline-block;
	height: 40px;
	line-height: 40px;
	width: auto;
	padding: 0px 5px
}

.ui-tab-nav li.current {
	color: #00a5e0
}

.ui-tab-nav li.current>p:before,
.ui-tab-nav li.current>span:before {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 4px;
	background-color: #00a5e0
}

.ui-tab-nav li:active {
	opacity: .7
}

.ui-tab-nav li:first-child:nth-last-child(2),
.ui-tab-nav li:first-child:nth-last-child(2)~li {
	width: 109px;
	display: inline-block
}

.ui-tab-nav-bisect-lg li:first-child:nth-last-child(2),
.ui-tab-nav-bisect-lg li:first-child:nth-last-child(2)~li {
	width: 125px;
	display: inline-block
}

.ui-tab-nav-multi>li {
	margin: 0px 16px
}

.ui-tab-nav-wrap {
	width: auto;
	overflow: auto;
	-webkit-overflow-scrolling: touch
}

.ui-tab-nav-wrap::-webkit-scrollbar {
	display: none
}

.ui-tab-fixed .ui-tab-nav {
	position: fixed;
	top: 0;
	z-index: 99
}

.ui-tab-fixed .ui-tab-content {
	margin-top: 40px
}

.ui-tab-nav-wrap-fixed .ui-tab-nav-wrap {
	position: fixed;
	top: 0;
	left: 0px;
	width: 100%;
	z-index: 99
}

.ui-tab-nav-wrap-fixed .ui-tab-content {
	margin-top: 40px
}

@media screen and (min-width: 414px) {
	.ui-tab-nav {
		height: 44px
	}

	.ui-tab-nav li {
		font-size: 15px
	}

	.ui-tab-nav li>p,
	.ui-tab-nav li>span {
		height: 44px;
		line-height: 44px;
		padding: 0 6px
	}

	.ui-tab-nav li>p.current p:before,
	.ui-tab-nav li>p.current span:before,
	.ui-tab-nav li>span.current p:before,
	.ui-tab-nav li>span.current span:before {
		height: 4px
	}

	.ui-tab-fixed .ui-tab-content {
		margin-top: 44px
	}
}

.ui-tab-content {
	height: auto;
	display: -webkit-box
}

.ui-tab-content>li {
	-webkit-box-flex: 1;
	width: 100%;
	height: auto
}

.ui-newstips-wrap {
	margin: 20px 15px;
	text-align: center
}

.ui-newstips {
	background: #383939;
	position: relative;
	height: 40px;
	line-height: 40px;
	display: -webkit-inline-box;
	-webkit-box-align: center;
	padding-right: 25px;
	border-radius: 5px;
	font-size: 14px;
	color: #fff;
	padding-left: 15px
}

.ui-newstips .ui-avatar-tiled,
.ui-newstips .ui-newstips-thumb,
.ui-newstips i {
	display: block;
	margin-left: -5px;
	margin-right: 10px
}

.ui-newstips .ui-newstips-thumb {
	width: 30px;
	height: 30px;
	position: relative
}

.ui-newstips .ui-newstips-thumb>span {
	display: block;
	width: 100%;
	height: 100%;
	background-repeat: no-repeat;
	-webkit-background-size: cover;
	position: relative
}

.ui-newstips div {
	display: -webkit-box;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	-webkit-box-flex: 1;
	height: inherit
}

.ui-newstips:after {
	content: "";
	display: block;
	position: absolute;
	right: 12px;
	top: 50%;
	margin-top: -7px;
	width: 8px;
	height: 14px;
	background: url(//i.gtimg.cn/vipstyle/qui/2.0.0/img/icon_arrowlink.png) no-repeat;
	-webkit-background-size: 100% auto;
	background-size: 100% auto
}

.ui-newstips .ui-reddot,
.ui-newstips .ui-reddot-border,
.ui-newstips .ui-reddot-s,
.ui-newstips .ui-badge-num {
	margin-left: 10px;
	margin-right: 5px
}

.ui-poptips {
	width: 100%;
	position: fixed;
	top: 0px;
	left: 0px;
	z-index: 999;
	padding: 0px 10px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-poptips-cnt {
	background-color: rgba(0, 0, 0, 0.6);
	line-height: 40px;
	height: 40px;
	color: #fff;
	font-size: 14px;
	text-align: center;
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
	max-width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis
}

.ui-poptips-cnt i {
	display: inline-block;
	width: 32px;
	height: 1px;
	vertical-align: top
}

.ui-poptips-cnt i:before {
	font-family: "icon-min" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5);
	font-size: 20px;
	margin-right: 2px;
	margin-left: 4px;
	color: #fff;
	line-height: 40px
}

.ui-poptips-info i:before {
	content: "飥�"
}

.ui-poptips-success i:before {
	content: "飥�"
}

.ui-poptips-warn i:before {
	content: "飥�"
}

.ui-tips {
	padding: 20px 15px;
	text-align: center;
	font-size: 14px;
	color: #000
}

.ui-tips i {
	display: inline-block;
	width: 32px;
	height: 1px;
	vertical-align: top
}

.ui-tips i:before {
	font-family: "icon-min" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5);
	content: "飥�";
	color: #fff;
	line-height: 21px
}

.ui-tips-success i:before {
	content: "飥�";
	color: #65d521
}

.ui-tips-warn i:before {
	content: "飥�";
	color: #f76249
}

.ui-tooltips {
	width: 100%;
	position: relative;
	z-index: 99;
	overflow: hidden;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	font-size: 14px
}

.ui-tooltips i {
	display: inline-block;
	margin-right: 6px;
	width: 20px;
	height: 1px
}

.ui-tooltips:active .ui-tooltips-cnt {
	opacity: .5
}

.ui-tooltips .ui-btn-s {
	position: absolute;
	top: 50%;
	right: 10px;
	width: auto;
	height: 25px;
	line-height: 25px;
	padding: 0 10px;
	margin-top: -12px
}

.ui-tooltips-cnt {
	line-height: 46px;
	height: 46px;
	padding-left: 12px;
	padding-right: 32px;
	background-color: #fff;
	color: #000;
	max-width: 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis
}

.ui-tooltips-cnt .ui-icon-close:before {
	color: #938F87;
	margin-left: -10px;
	position: absolute;
	right: 12px;
	top: 0
}

.ui-tooltips-warn .ui-tooltips-cnt {
	background-color: rgba(255, 218, 218, 0.95)
}

.ui-tooltips-warn i:before {
	font-family: "icon-min" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5);
	content: "飥�";
	font-size: 20px;
	color: #FE6C6C
}

.ui-tooltips-guide .ui-tooltips-cnt {
	background-color: rgba(255, 255, 255, 0.95)
}

.ui-tooltips-guide i:before {
	font-family: "icon-min" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5);
	content: "飥�";
	font-size: 20px;
	color: #12B7F5
}

.ui-tooltips-cnt-link:after {
	content: "";
	display: block;
	position: absolute;
	right: 12px;
	top: 50%;
	margin-top: -7px;
	width: 8px;
	height: 14px;
	background: url(//i.gtimg.cn/vipstyle/qui/2.0.0/img/icon_arrowlink.png) no-repeat;
	-webkit-background-size: 100% auto;
	background-size: 100% auto;
	color: #938F87
}

.ui-tooltips-hignlight {
	position: absolute;
	top: 0
}

.ui-tooltips-hignlight .ui-tooltips-cnt {
	color: #fff;
	background-color: rgba(0, 0, 0, 0.5)
}

.ui-tooltips-hignlight .ui-tooltips-cnt-link:after {
	color: #fff
}

.ui-tooltips-function .ui-tooltips-cnt {
	padding-right: 60px
}

.ui-tooltips-action {
	position: fixed;
	bottom: 0
}

.ui-tooltips-action .ui-tooltips-cnt {
	padding-right: 90px
}

.ui-tooltips-action .ui-btn-s {
	right: 40px
}

.ui-icon-qq,
.ui-icon-vip,
.ui-icon-svip,
.ui-icon-yearsvip,
.ui-icon-yearvip,
.ui-icon-qq-off,
.ui-icon-vip-off,
.ui-icon-svip-off,
.ui-icon-yearsvip-off,
.ui-icon-yearvip-off {
	display: inline-block;
	width: 24px;
	height: 1px;
	position: relative;
	vertical-align: middle
}

.ui-icon-qq:after,
.ui-icon-vip:after,
.ui-icon-svip:after,
.ui-icon-yearsvip:after,
.ui-icon-yearvip:after {
	content: "";
	display: block;
	width: 24px;
	height: 24px;
	background: url(//i.gtimg.cn/vipstyle/frozenui/2.0.0/img/vip/icon_vip.png) center;
	-webkit-background-size: 24px auto;
	background-size: 24px auto;
	background-position: 0 0;
	position: absolute;
	line-height: 100px;
	top: -13px
}

.ui-icon-qq-off:after,
.ui-icon-vip-off:after,
.ui-icon-svip-off:after,
.ui-icon-yearsvip-off:after,
.ui-icon-yearvip-off:after {
	content: "";
	display: block;
	width: 24px;
	height: 24px;
	background: url(//i.gtimg.cn/vipstyle/frozenui/2.0.0/img/vip/icon_vip_grey.png) center;
	-webkit-background-size: 24px auto;
	background-size: 24px auto;
	background-position: 0 0;
	position: absolute;
	line-height: 100px;
	top: -13px
}

.ui-icon-svip:after,
.ui-icon-svip-off:after {
	background-position: 0 -25px
}

.ui-icon-vip:after,
.ui-icon-vip-off:after {
	background-position: 0 -50px
}

.ui-icon-yearsvip:after,
.ui-icon-yearsvip-off:after {
	background-position: 0 -75px
}

.ui-icon-yearvip:after,
.ui-icon-yearvip-off:after {
	background-position: 0 -100px
}

.ui-icon-qqlevel {
	display: inline-block;
	width: 15px;
	height: 1px;
	position: relative;
	vertical-align: middle
}

.ui-icon-qqlevel:after {
	content: "";
	display: block;
	width: 16px;
	background: url(//i.gtimg.cn/vipstyle/frozenui/2.0.0/img/vip/icon_qqlevel_sprite.png) no-repeat;
	background-repeat: no-repeat;
	position: absolute;
	line-height: 100px;
	height: 16px;
	-webkit-background-size: 16px auto;
	top: -10px
}

.ui-icon-qqlevel-king:after {
	background-position: 0 0
}

.ui-icon-qqlevel-sun:after {
	background-position: 0 -48px
}

.ui-icon-qqlevel-moon:after {
	background-position: 0 -16px
}

.ui-icon-qqlevel-star:after {
	background-position: 0 -32px
}

.ui-icon-qqlevel-none:after {
	background-position: 0 -64px
}

.ui-icon-viplevel,
.ui-icon-viplevel-s,
.ui-icon-sviplevel,
.ui-icon-sviplevel-s,
.ui-icon-yearviplevel,
.ui-icon-yearviplevel-s,
.ui-icon-yearsviplevel,
.ui-icon-yearsviplevel-s,
.ui-icon-mq {
	display: inline-block;
	height: 1px;
	position: relative;
	vertical-align: middle
}

.ui-icon-viplevel span,
.ui-icon-viplevel-s span,
.ui-icon-sviplevel span,
.ui-icon-sviplevel-s span,
.ui-icon-yearviplevel span,
.ui-icon-yearviplevel-s span,
.ui-icon-yearsviplevel span,
.ui-icon-yearsviplevel-s span,
.ui-icon-mq span {
	overflow: hidden;
	display: block;
	width: 100%;
	background-repeat: no-repeat;
	position: absolute;
	line-height: 100px
}

.ui-icon-viplevel,
.ui-icon-viplevel-s,
.ui-icon-sviplevel,
.ui-icon-sviplevel-s,
.ui-icon-yearviplevel,
.ui-icon-yearviplevel-s,
.ui-icon-yearsviplevel,
.ui-icon-yearsviplevel-s,
.ui-icon-mq {
	width: 46px
}

.ui-icon-viplevel span,
.ui-icon-viplevel-s span,
.ui-icon-sviplevel span,
.ui-icon-sviplevel-s span,
.ui-icon-yearviplevel span,
.ui-icon-yearviplevel-s span,
.ui-icon-yearsviplevel span,
.ui-icon-yearsviplevel-s span,
.ui-icon-mq span {
	-webkit-background-size: auto 19px
}

.ui-icon-viplevel-s {
	width: 31px
}

.ui-icon-viplevel-s span {
	-webkit-background-size: auto 12px
}

.ui-icon-yearviplevel {
	width: 55px
}

.ui-icon-yearviplevel span {
	-webkit-background-size: auto 19px
}

.ui-icon-yearviplevel-s {
	width: 40px
}

.ui-icon-yearviplevel-s span {
	-webkit-background-size: auto 12px
}

.ui-icon-sviplevel {
	width: 48px
}

.ui-icon-sviplevel span {
	-webkit-background-size: auto 19px
}

.ui-icon-sviplevel-s {
	width: 36px
}

.ui-icon-sviplevel-s span {
	-webkit-background-size: auto 12px
}

.ui-icon-yearsviplevel {
	width: 58px
}

.ui-icon-yearsviplevel span {
	-webkit-background-size: auto 19px
}

.ui-icon-yearsviplevel-s {
	width: 45px
}

.ui-icon-yearsviplevel-s span {
	-webkit-background-size: auto 12px
}

.ui-icon-viplevel span,
.ui-icon-viplevel-s span,
.ui-icon-sviplevel span,
.ui-icon-sviplevel-s span,
.ui-icon-yearviplevel span,
.ui-icon-yearviplevel-s span,
.ui-icon-yearsviplevel span,
.ui-icon-yearsviplevel-s span,
.ui-icon-mq span,
.ui-icon-yearviplevel span {
	height: 22px;
	top: -13px
}

.ui-icon-sviplevel span,
.ui-icon-yearsviplevel span {
	height: 22px;
	top: -13px
}

.ui-icon-viplevel-s span,
.ui-icon-sviplevel-s span,
.ui-icon-yearviplevel-s span,
.ui-icon-yearsviplevel-s span {
	height: 20px;
	top: -7px
}

.ui-icon-mq {
	width: 28px
}

.ui-icon-mq span {
	height: 28px;
	top: -15px;
	-webkit-background-size: 28px auto
}

.ui-btn-vip {
	height: 26px;
	line-height: 26px;
	padding: 0 13px;
	display: inline-block;
	position: relative;
	text-align: center;
	font-size: 13px;
	background-color: #fbd54e;
	border-radius: 3px;
	vertical-align: top;
	color: #71400b;
	-webkit-box-sizing: border-box;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-lg-vip {
	font-size: 18px;
	height: 40px;
	line-height: normal;
	display: block;
	text-align: center;
	width: 100%;
	border-radius: 3px;
	background-color: #fbd54e;
	color: #71400b;
	-webkit-box-sizing: border-box;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-btn-vip:not(.disabled):not(:disabled):active,
.ui-btn-lg-vip:not(.disabled):not(:disabled):active,
.ui-btn-vip.active,
.ui-btn-lg-vip.active {
	background-color: rgba(251, 213, 78, 0.8);
	color: #71400b;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-tooltips-vip {
	width: 100%;
	position: relative;
	z-index: 99
}

.ui-tooltips-vip .ui-tooltips-cnt {
	background-color: rgba(248, 248, 248, 0.95);
	color: #000;
	line-height: 46px;
	height: 46px;
	padding-left: 10px;
	font-size: 0;
	display: -webkit-box
}

.ui-tooltips-vip .ui-tooltips-cnt span {
	font-size: 14px;
	display: block
}

.ui-tooltips-vip .ui-tooltips-cnt i {
	margin-right: 6px
}

.ui-tooltips-vip .ui-btn-vip,
.ui-tooltips-vip .ui-btn-s {
	position: absolute;
	top: 50%;
	right: 10px;
	margin-top: -13px
}

.ui-tooltips-footer-vip {
	position: fixed;
	width: 100%;
	z-index: 99;
	left: 0;
	bottom: 0;
	background-color: rgba(255, 255, 255, 0.95)
}

.ui-tooltips-footer-vip .ui-tooltips-cnt {
	display: -webkit-box;
	-webkit-box-pack: center;
	margin: 7px 10px;
	padding: 0;
	line-height: normal;
	height: auto
}

.ui-badge,
.ui-badge-muted,
.ui-badge-num,
.ui-badge-corner,
.ui-badge-cornernum {
	display: inline-block;
	text-align: center;
	background: #f74c31;
	color: #fff;
	font-size: 11px;
	height: 16px;
	line-height: 16px;
	-webkit-border-radius: 8px;
	padding: 0 6px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-badge-muted {
	background: #b6cae0
}

.ui-badge-num {
	height: 19px;
	line-height: 20px;
	font-size: 12px;
	min-width: 19px;
	-webkit-border-radius: 10px
}

.ui-badge-wrap {
	position: relative;
	text-align: center
}

.ui-badge-corner {
	position: absolute;
	border: 2px #fff solid;
	height: 20px;
	line-height: 20px;
	top: -4px;
	right: -9px
}

.ui-badge-cornernum {
	position: absolute;
	top: -4px;
	right: -9px;
	height: 19px;
	line-height: 19px;
	font-size: 12px;
	min-width: 19px;
	-webkit-border-radius: 10px;
	top: -5px;
	right: -5px
}

@font-face {
	font-family: "icon-corner";
	src: url(data:font/ttf;charset=utf-8;base64,AAEAAAAKAIAAAwAgT1MvMkiTZ6gAAACsAAAAYGNtYXDyGOXTAAABDAAAAVJnbHlmJXUGNAAAAmAAAADUaGVhZAr8ouQAAAM0AAAANmhoZWEHwwOJAAADbAAAACRobXR4EiUBgAAAA5AAAAAUbG9jYQCcAMwAAAOkAAAADG1heHAACAAZAAADsAAAACBuYW1lGVKlzAAAA9AAAAGtcG9zdDjdfHcAAAWAAAAATAAEA6EBkAAFAAACmQLMAAAAjwKZAswAAAHrADMBCQAAAgAGAwAAAAAAAAAAAAEQAAAAAAAAAAAAAABQZkVkAMDyAPIDAyz/LABcAywA1AAAAAEAAAAAAxgAAAAAACAAAQAAAAMAAAADAAAAHAABAAAAAABMAAMAAQAAABwABAAwAAAACAAIAAIAAPIB8gLyA///AADyAPIC8gP//w4BDgIOAAABAAAAAAAAAAAAAAEGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAiAAABMgKqAAMABwAANxEhESczESMiARDuzMwAAqr9ViICZgAAAAEAUP/OA+UCkAAYAAABFhcWFwYHBgchKwEGFxYXJicmJz4BNzsBAuRsSkgDA0hIbP7YAmw/Dw8bbkVFAgTHlgkIApADS0twcEtLAjAzMxslXF17mcwEAAABAB4AtAQbAZsAAwAAAQchJwEE5gP95QGb5+cAAQCgALQDaQGZAAMAAAEHIScBhOQCyeUBmeXlAAEAUAC0A+gBmgADAAABByEnATbmA5jmAZrm5gABAAAAAQAA9eRLnl8PPPUACwQAAAAAANPRibgAAAAA09LVtwAe/84EGwKqAAAACAACAAAAAAAAAAEAAAMs/ywAXAQ5AB4AHgQbAAEAAAAAAAAAAAAAAAAAAAAFAXYAIgQ1AFAEOQAeBAkAoAQ4AFAAAAAUAEAATgBcAGoAAQAAAAUAGQACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAlgABAAAAAAABAAoAAAABAAAAAAACAAYACgABAAAAAAADABsAEAABAAAAAAAEAAoAKwABAAAAAAAFAB4ANQABAAAAAAAGAAoAUwADAAEECQABABQAXQADAAEECQACAAwAcQADAAEECQADADYAfQADAAEECQAEABQAswADAAEECQAFADwAxwADAAEECQAGABQBA2ZvbnRlZGl0b3JNZWRpdW1Gb250RWRpdG9yIDEuMCA6IGZvbnRlZGl0b3Jmb250ZWRpdG9yVmVyc2lvbiAxLjA7IEZvbnRFZGl0b3IgKHYxLjApZm9udGVkaXRvcgBmAG8AbgB0AGUAZABpAHQAbwByAE0AZQBkAGkAdQBtAEYAbwBuAHQARQBkAGkAdABvAHIAIAAxAC4AMAAgADoAIABmAG8AbgB0AGUAZABpAHQAbwByAGYAbwBuAHQAZQBkAGkAdABvAHIAVgBlAHIAcwBpAG8AbgAgADEALgAwADsAIABGAG8AbgB0AEUAZABpAHQAbwByACAAKAB2ADEALgAwACkAZgBvAG4AdABlAGQAaQB0AG8AcgAAAAACAAAAAAAAADIAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUAAAECAQMBBAEFB3VuaUYyMDAHdW5pRjIwMQd1bmlGMjAzB3VuaUYyMDI=) format("truetype")
}

.ui-corner-t,
.ui-corner-pop,
.ui-corner-pop-hot,
.ui-corner-pop-new {
	position: absolute;
	z-index: 9;
	top: -14px;
	left: -17px;
	width: 40px;
	text-align: right;
	line-height: 20px;
	font-size: 11px;
	color: #fff;
	text-align: center
}

.ui-corner-t:before,
.ui-corner-pop:before,
.ui-corner-pop-hot:before,
.ui-corner-pop-new:before {
	font-family: "icon-corner" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5);
	position: absolute;
	height: 20px;
	left: 0;
	top: 0;
	line-height: 20px;
	color: #ff0000;
	z-index: -1
}

.ui-corner-pop:before,
.ui-corner-pop-hot:before,
.ui-corner-pop-new:before {
	content: "飯€";
	left: 3px;
	top: 3px
}

.ui-corner-pop:before {
	color: #f74c32
}

.ui-corner-pop-hot:before {
	color: #ff7200
}

.ui-corner-pop-new:before {
	color: #f74c32
}

.ui-corner-s,
.ui-corner,
.ui-corner-lg {
	font-size: 11px;
	color: #fff;
	text-align: center;
	height: 20px;
	line-height: 20px;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 9;
	-webkit-transform: rotate(-45deg);
	transform: rotate(-45deg)
}

.ui-corner-s:before,
.ui-corner:before,
.ui-corner-lg:before {
	font-family: "icon-corner" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5);
	position: absolute;
	color: #f74c32;
	left: 50%;
	top: 50%;
	-webkit-transform: translate3d(-50%, -50%, 0);
	transform: translate3d(-50%, -50%, 0);
	font-size: 72px;
	z-index: -1
}

.ui-corner-s {
	width: 66px;
	left: -21px;
	top: 2px
}

.ui-corner-s:before {
	content: "飯�"
}

.ui-corner {
	width: 90px;
	left: -30px;
	top: 7px
}

.ui-corner:before {
	content: "飯�"
}

.ui-corner-lg {
	width: 100px;
	left: -30px;
	top: 10px
}

.ui-corner-lg:before {
	content: "飯�"
}

.ui-corner-hot:before {
	color: #ff7200
}

.ui-corner-new:before {
	color: #f74c32
}

.ui-reddot,
.ui-reddot-border,
.ui-reddot-s {
	position: relative;
	display: inline-block;
	line-height: 22px
}

.ui-reddot:after,
.ui-reddot-border:after,
.ui-reddot-s:after {
	content: '';
	position: absolute;
	display: block;
	width: 8px;
	height: 8px;
	background-color: #f74c31;
	border-radius: 8px;
	right: -4px;
	top: -4px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box
}

.ui-reddot-static {
	display: block;
	width: 8px;
	height: 8px;
	padding: 0
}

.ui-reddot-static:after {
	top: 0;
	right: 0
}

.ui-reddot-border:before {
	content: '';
	position: absolute;
	display: block;
	width: 8px;
	height: 8px;
	background-color: #fff;
	border-radius: 8px;
	right: -4px;
	top: -4px;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	padding: 1px
}

.ui-reddot-s:after {
	width: 6px;
	height: 6px;
	top: -5px;
	right: -5px
}

@font-face {
	font-family: "icon-tag";
	src: url(data:font/ttf;charset=utf-8;base64,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) format("truetype")
}

.ui-tag-b-default,
.ui-tag {
	position: absolute;
	z-index: 9;
	min-width: 20px;
	height: 13px;
	line-height: 13px;
	right: 0;
	bottom: 0;
	padding: 0 4px;
	font-size: 10px;
	color: #fff;
	border-radius: 2px;
	text-align: center
}

.ui-tag {
	background-color: #f74c32
}

.ui-tag-b,
.ui-tag-freelimit,
.ui-tag-free,
.ui-tag-last,
.ui-tag-limit,
.ui-tag-act,
.ui-tag-xy {
	position: relative
}

.ui-tag-b:before,
.ui-tag-freelimit:before,
.ui-tag-free:before,
.ui-tag-last:before,
.ui-tag-limit:before,
.ui-tag-act:before,
.ui-tag-xy:before {
	position: absolute;
	font-size: 10px;
	width: 28px;
	height: 13px;
	line-height: 13px;
	bottom: 0;
	right: 0;
	z-index: 9;
	color: #fff;
	border-radius: 2px;
	text-align: center
}

.ui-tag-vip,
.ui-tag-svip {
	position: relative;
	width: 100%;
	height: 100%
}

.ui-tag-vip:before,
.ui-tag-svip:before {
	font-size: 32px;
	text-indent: -2px;
	border-radius: 2px;
	width: 28px;
	height: 13px;
	line-height: 13px;
	right: 0;
	bottom: 0;
	z-index: 9
}

.ui-tag-vip:before,
.ui-tag-svip:before,
.ui-tag-selected:after {
	font-family: "icon-tag" !important;
	font-size: 32px;
	line-height: 13px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5);
	position: absolute
}

.ui-tag-vip:before {
	background-color: #ff0000;
	color: #fffadf;
	content: "飳€"
}

.ui-tag-svip:before {
	background-color: #ffd400;
	color: #b7440e;
	content: "飳�"
}

.ui-tag-selected:after {
	content: "飳�";
	color: #18b4ed;
	right: 5px;
	top: 5px;
	z-index: 9;
	width: 26px;
	height: 26px;
	background: #fff;
	border-radius: 13px;
	line-height: 26px;
	text-indent: -3px
}

.ui-tag-wrap {
	display: inline-block;
	position: relative;
	padding-right: 32px
}

.ui-tag-wrap .ui-tag-vip,
.ui-tag-wrap .ui-tag-svip {
	position: static
}

.ui-tag-wrap .ui-tag-vip:before,
.ui-tag-wrap .ui-tag-svip:before {
	top: 50%;
	margin-top: -7px
}

.ui-tag-svip:before {
	background-color: #ffd400;
	color: #b7440e;
	content: "飳�"
}

.ui-tag-freelimit:before {
	background-color: #18b4ed;
	content: '闄愬厤'
}

.ui-tag-free:before {
	background-color: #4b972d;
	content: '鍏嶈垂'
}

.ui-tag-last:before {
	background-color: #8f6adb;
	content: '缁濈増'
}

.ui-tag-limit:before {
	background-color: #3385e6;
	content: '闄愰噺'
}

.ui-tag-act:before {
	background-color: #00c795;
	content: '娲诲姩'
}

.ui-tag-xy:before {
	background-color: #d7ba42;
	content: '鏄熷奖'
}

.ui-tag-freemonthly:before {
	background-color: #ff7f0d;
	content: '鍖呮湀'
}

.ui-tag-onsale:before {
	background-color: #00c795;
	content: '鐗逛环'
}

.ui-grid-halve>li {
	padding-right: 5px;
	padding-bottom: 10px;
	float: left;
	position: relative;
	-webkit-box-sizing: border-box;
	width: 50%
}

.ui-grid-halve-img {
	width: 100%;
	padding-top: 55.17%;
	position: relative
}

.ui-grid-halve-img>span {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	background-repeat: no-repeat;
	-webkit-background-size: cover
}

.ui-subscript {
	position: absolute;
	left: 0;
	top: 0;
	z-index: 9;
	height: 16px;
	line-height: 16px;
	font-size: 11px;
	background-color: #FB5050;
	white-space: nowrap;
	color: #fff;
	padding: 0px 50px;
	-webkit-transform: rotate(-45deg) translate(-31%, -205%);
	transform: rotate(-45deg) translate(-31%, -205%)
}

.ui-subscript.ui-subscript-red {
	background-color: #FB5050
}

.ui-subscript.ui-subscript-orange {
	background-color: #FF9137
}

.ui-subscript.ui-subscript-green {
	background-color: #6AC63D
}

.ui-subscript.ui-subscript-blue {
	background-color: #12B7F5
}

.ui-subscript.ui-subscript-trisection {
	-webkit-transform: rotate(-45deg) translate(-32%, -180%);
	transform: rotate(-45deg) translate(-32%, -180%)
}

.ui-loading-wrap {
	display: -webkit-box;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	text-align: center;
	height: 40px
}

.ui-loading {
	width: 20px;
	height: 20px;
	display: block;
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoBAMAAAB+0KVeAAAAMFBMVEUAAACGhoaxsbEnJycvLy9DQ0OBgYFJSUnw8PCKiopVVVXt7e21tbWqqqrj4+MdHR0dtY9IAAAAD3RSTlMAHvjpv4f9Qf7O+tebYJiZwLcAAAABk0lEQVQoz2XRsUsCYRQA8PdB4mLxWToVQt9s0ymNJhoHLhZWg4uTB20XHIj9BQeS0FAh5BAUwW1tLYK6BQ3hEIRLbU4t2vz13n363WVvOLjfvfe+974DHeMe/Av23OJ/4ZsrZK9cY8r9VBgTfY2b7oXCkdjWuOa2OCETocxV1+0TxoQIHTZxnwjXRXohnJq2AXFILRklr2TxVGw67mHLN2Avl4g1uc/ZpM0xY5gGNsIWhNKE1BdgbA3gQ/joSDnNE/kshBjQjhUpfxY4EqILoDSEO/NJowVTl9+SKQUdylg8HodQ4CsHp1gsmoE1O51OF8oSR+Iary3LOl1CprBiGEZ2uTzaaDRUoj53PkGAQaOK3j15hbq85rtVV+rIMFo3pMkcXqgux4nUzcssTx4S4bNpWXUfMxxqM/ph1XtgJz4mZjZEc1PbMVnJs1GpnJ3hBzmDcoZXvTxQBQW12GUFw054D6HdcnKPMOp5tsaInNqEUPIONG7g9D6ee48aV2ReYcS708iObIXsmEMoCDUE4QT3/wsVHI+E7CpGpgAAAABJRU5ErkJggg==);
	-webkit-background-size: auto 20px;
	-webkit-animation: am-rotate 1s steps(12, end) infinite
}

.ui-loading-bright {
	width: 37px;
	height: 37px;
	display: block;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEoAAABKBAMAAADu/t9CAAAALVBMVEUAAAD///////////////////////////////////////////////////////+hSKubAAAAD3RSTlMACnlJpf0VMNUgYD+O7L6ZrQXNAAACgUlEQVRIx+3UQWsTQRQH8LdVNqTxMItg0XhxUittQawLVatedIpY66X0gZdIVYgoHlorlHrwUgoBQUIOSuKllGDVg8Qo9OKlGlsVexF7jNeqn8I3m2ymM+y4oNBT/4eWkF/mvX07M7CTbcnBxgrE5zCOxSN3GMcn/gb2fiko5VhQcj27opTHolUn4lxbOZ4XrToQxwuxatc8ZhdC5dmUu474SykG0XmAOMaayrGrzg28Wg2UvSCNYhizqw4pZinoDk3R35OID+Ubahc0FtzT+E1zv4P4DYr53nAO+3L6uzqL2fMMUvNyrjPQVIRy1zT1EfHKGoPnjVUIIgs6OYqxXRB/9ELaVyUImWvd2yD2/RiAjnJMH8IryTZBxZXI3GiJo6TmdEX1zKQ/0bB0pZDKjcvZz1pfhCJSzmi9OvBvcTwKiztdi5ySMZ77yYwMUxvrZ57yaEH/6RtfZlLtiHyQi/po/CBVCLO7qTY11WWqjqY6pL80s2KyW9SFOFeI6H52y5PvLz2+VVKz/v/ZJoPV1foTkejM+wGt+eUI5taEWNPHtcxM5Nynp7xgDHXWZGVC5lrEjMtSotGtfbnTkk1p6q2gLIFbatWgc9ElZ39KUxVC/QyKX5vLJRaXAG4SO62fRyEyBXArsjWPzgDvKxDz/RP6nSrkLZ6qixFI0PVT5JwWdZ76r/WRDsnP14W4BHf5EbjN+QsA1abZXQ/UeB9L0f62HYNEXYxW4TipJB2ESYs6IMQIc6SisvyZRb2T0w8q0kPyfst56xZioKWoJM0iKim5qVuK6vKqTX2gf0FfULYppyJ62iptqwhp+iJU7vQgWMIgVLGp8QzEJ/1yEHayHfkD3v3C7oGTmFkAAAAASUVORK5CYII=);
	-webkit-background-size: auto 37px;
	-webkit-animation: am-rotate 1s steps(12) infinite
}

.ui-loading-wrap .ui-loading {
	margin: 10px
}

.ui-loading-block {
	position: fixed;
	top: 0px;
	left: 0px;
	width: 100%;
	height: 100%;
	z-index: 9999;
	display: -webkit-box;
	-webkit-box-orient: horizontal;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	background: rgba(0, 0, 0, 0.4);
	display: none;
	background: transparent
}

.ui-loading-block .ui-loading-cnt {
	width: 130px;
	height: 110px;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-align: center;
	text-align: center;
	background: rgba(0, 0, 0, 0.65);
	border-radius: 6px;
	color: #fff;
	font-size: 14px
}

.ui-loading-block .ui-loading-bright {
	margin: 18px 0 8px
}

.ui-loading-block.show {
	display: -webkit-box;
	display: box
}

@-webkit-keyframes am-rotate {
	0% {
		-webkit-transform: rotate3d(0, 0, 1, 0deg)
	}

	100% {
		-webkit-transform: rotate3d(0, 0, 1, 360deg)
	}
}

@font-face {
	font-family: "icon-notice";
	src: url(data:font/ttf;charset=utf-8;base64,AAEAAAAKAIAAAwAgT1MvMkqTaLkAAACsAAAAYGNtYXAADfWmAAABDAAAAUJnbHlmQnm6kwAAAlAAAAFgaGVhZAq5+tsAAAOwAAAANmhoZWEHfwM5AAAD6AAAACRobXR4BWsANwAABAwAAAAIbG9jYQCwABQAAAQUAAAABm1heHAACQBwAAAEHAAAACBuYW1lGVKlzAAABDwAAAGtcG9zdJ/RTasAAAXsAAAALgAEArUBkAAFAAACmQLMAAAAjwKZAswAAAHrADMBCQAAAgAGAwAAAAAAAAAAAAEQAAAAAAAAAAAAAABQZkVkAMD0APQAAyz/LABcAywA1AAAAAEAAAAAAxgAAAAAACAAAQAAAAMAAAADAAAAHAABAAAAAAA8AAMAAQAAABwABAAgAAAABAAEAAEAAPQA//8AAPQA//8MAQABAAAAAAAAAQYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACACIAAAEyAqoAAwAHAAA3ESERJzMRIyIBEO7MzAACqv1WIgJmAAAABgAV/0AD4AMLAB0ALwA9AEYAVgBvAAAlIi4DJy4BNTQ2MzIeCBUUDgEHDgEDIgYVFB4BFx4BMjY3PgE1NCYDIiY1ND4BMzIWFRQOASciBhQWMjY0JgMiLgI0PgIyHgIVFAADIg4FFRQeAjI+AjU0LgUB+wUKCAcGAgUgKSEHDgsLCQgGBQMBEBIDBBUNHCMREQMCEBIOBAUgJRsbJREdEholER0RFh8fLB8fFmO0gk1NgrTFtIJN/uPIOGpeUkEvGUt/sMGwf0sZL0BTXWujAgYHCQUQ8zwYNAMFBwkJCgoKCQQqmHMKDRABnS0UKphyCAgODAoS8DoULf3AJRoSHRElGxEdEXQfKx8fKx/+zE2CtMW0gk1NgrRjyP7jA8AZL0FSXmo4YLB/S0t/sGA4al5SQS8ZAAAAAQAAAAEAAOhgb1JfDzz1AAsEAAAAAADT0tu7AAAAANPS29gAFf9AA+ADCwAAAAgAAgAAAAAAAAABAAADLP8sAFwD9QAVABUD4AABAAAAAAAAAAAAAAAAAAAAAgF2ACID9QAVAAAAFACwAAAAAQAAAAIAcAAGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAlgABAAAAAAABAAoAAAABAAAAAAACAAYACgABAAAAAAADABsAEAABAAAAAAAEAAoAKwABAAAAAAAFAB4ANQABAAAAAAAGAAoAUwADAAEECQABABQAXQADAAEECQACAAwAcQADAAEECQADADYAfQADAAEECQAEABQAswADAAEECQAFADwAxwADAAEECQAGABQBA2ZvbnRlZGl0b3JNZWRpdW1Gb250RWRpdG9yIDEuMCA6IGZvbnRlZGl0b3Jmb250ZWRpdG9yVmVyc2lvbiAxLjA7IEZvbnRFZGl0b3IgKHYxLjApZm9udGVkaXRvcgBmAG8AbgB0AGUAZABpAHQAbwByAE0AZQBkAGkAdQBtAEYAbwBuAHQARQBkAGkAdABvAHIAIAAxAC4AMAAgADoAIABmAG8AbgB0AGUAZABpAHQAbwByAGYAbwBuAHQAZQBkAGkAdABvAHIAVgBlAHIAcwBpAG8AbgAgADEALgAwADsAIABGAG8AbgB0AEUAZABpAHQAbwByACAAKAB2ADEALgAwACkAZgBvAG4AdABlAGQAaQB0AG8AcgAAAAACAAAAAAAAADIAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAIAAAECB3VuaUY0MDAAAA==) format("truetype")
}

.ui-notice {
	width: 100%;
	height: 100%;
	z-index: 99;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	position: absolute;
	text-align: center
}

.ui-notice i {
	display: block;
	margin-bottom: 20px
}

.ui-notice i:before {
	font-family: "icon-notice" !important;
	font-size: 32px;
	line-height: 46px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	display: block;
	color: rgba(0, 0, 0, 0.5);
	content: "飷€";
	font-size: 100px;
	line-height: 100px;
	color: rgba(0, 0, 0, 0.3)
}

.ui-notice p {
	font-size: 14px;
	line-height: 20px;
	color: #bbb;
	text-align: center;
	padding: 0 15px
}

.ui-notice-btn {
	width: 100%;
	-webkit-box-sizing: border-box;
	padding: 50px 15px 15px
}

.ui-notice-btn button {
	margin: 10px 0px
}

.ui-progress {
	overflow: hidden;
	width: 100%;
	height: 2px;
	font-size: 0px;
	background-color: #e2e2e2;
	-webkit-box-sizing: border-box;
	box-sizing: border-box
}

.ui-progress span {
	display: block;
	width: 0%;
	background: #65d521;
	height: 100%;
	font-size: 0
}

.ui-grid-trisect li .ui-progress,
.ui-grid-halve li .ui-progress {
	position: absolute;
	height: 13px;
	bottom: 0px;
	z-index: 9;
	border: 5px solid rgba(248, 248, 248, 0.9)
}

.ui-grid-trisect li .ui-progress span,
.ui-grid-halve li .ui-progress span {
	border-radius: 3px
}

.ui-slider {
	width: 100%;
	overflow: hidden;
	position: relative;
	padding-top: 31.25%
}

.ui-slider-content {
	display: -webkit-box;
	position: absolute;
	left: 0;
	top: 0;
	height: 100%
}

.ui-slider-content>li {
	-webkit-box-flex: 1;
	width: 100%;
	height: 100%
}

.ui-slider-content>li.active {
	opacity: .5
}

.ui-slider-content>li img {
	display: block;
	width: 100%
}

.ui-slider-content>li span {
	display: block;
	width: 100%;
	height: 100%;
	background-repeat: no-repeat;
	-webkit-background-size: 100% 100%
}

.ui-slider-indicators {
	position: absolute;
	display: -webkit-box;
	-webkit-box-pack: end;
	width: 100%;
	bottom: 10px;
	right: 10px;
	font-size: 0
}

.ui-slider-indicators li {
	position: relative;
	display: block;
	text-indent: 100%;
	white-space: nowrap;
	overflow: hidden;
	font-size: 0;
	width: 7px;
	height: 7px;
	border: 1px solid rgba(0, 0, 0, 0.3);
	background-color: rgba(0, 0, 0, 0.3);
	border-radius: 10px;
	margin-right: 6px;
	box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-webkit-background-clip: padding-box
}

.ui-slider-indicators li.current:before {
	content: '';
	position: absolute;
	background-color: #fff;
	left: 0px;
	top: 0px;
	width: 5px;
	height: 5px;
	border-radius: 10px;
	-webkit-box-sizing: border-box;
	-webkit-background-clip: padding-box
}

.ui-slider-indicators-center {
	-webkit-box-pack: center;
	right: 0
}

.ui-table {
	width: 100%;
	border-collapse: collapse
}

.ui-table th {
	font-weight: 500
}

.ui-table td,
.ui-table th {
	border-bottom: 1px solid #e9e9e9;
	border-right: 1px solid #e9e9e9;
	text-align: center
}

.ui-table tr td:last-child,
.ui-table tr th:last-child {
	border-right: 0
}

.ui-table tr:last-child td {
	border-bottom: 0
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {

	.ui-table td,
	.ui-table th {
		position: relative;
		border-right: 0;
		border-bottom: 0
	}

	.ui-table td:after,
	.ui-table th:after {
		content: "";
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		background-image: -webkit-gradient(linear, left top, right top, color-stop(0.5, transparent), color-stop(0.5, #e9e9e9)), -webkit-gradient(linear, left top, left bottom, color-stop(0.5, transparent), color-stop(0.5, #e9e9e9));
		-webkit-background-size: 1px 100%, 100% 1px;
		background-size: 1px 100%, 100% 1px;
		background-repeat: no-repeat;
		background-position: right, bottom;
		pointer-events: none
	}

	.ui-table tr td:last-child:after,
	.ui-table tr th:last-child:after {
		background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0.5, transparent), color-stop(0.5, #e9e9e9));
		-webkit-background-size: 100% 1px;
		background-size: 100% 1px;
		background-repeat: no-repeat;
		background-position: bottom
	}

	.ui-table tr:last-child td:after {
		background-image: -webkit-gradient(linear, left top, right top, color-stop(0.5, transparent), color-stop(0.5, #e9e9e9));
		-webkit-background-size: 1px 100%;
		background-size: 1px 100%;
		background-repeat: no-repeat;
		background-position: right
	}

	.ui-table tr:last-child td:last-child:after {
		background-image: none
	}
}
