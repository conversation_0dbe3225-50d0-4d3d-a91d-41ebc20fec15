<template>
	<view class="contenr">
		<u--form :model="form" :rules="rulesr" ref="uForm" class="is-bottom" label-width="100" labelAlign="right">
			<view class="formBg">
				<u-form-item :required="true" label="选择车辆 :" prop="carNumber" borderBottom @tap="pickerBtn(1)">
					<u--input v-model="form.carNumber" disabled disabledColor="#ffffff" placeholder="请选择车辆"
						border="none"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item label="品牌型号 :" prop="brandModelName" borderBottom>
					<u-input v-model="form.brandModelName" border="none" placeholder="根据选择车辆自动填充品牌型号" disabled
						disabledColor="#ffffff" />
				</u-form-item>
			</view>
			<view class="formBg">
				<u-form-item :required="true" label="投保日期 :" prop="insuranceDate" borderBottom @click="tiemOpen(1)">
					<u--input v-model="form.insuranceDate" disabled disabledColor="#ffffff" placeholder="请选择投保日期"
						border="none">
					</u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item :required="true" label="保险单号 :" prop="insuranceCode" borderBottom>
					<u-input v-model="form.insuranceCode" border="none" placeholder="请输入保险单号" />
				</u-form-item>
				<u-form-item :required="true" label="投保单位 :" prop="insuranceCompany" borderBottom>
					<u-input v-model="form.insuranceCompany" border="none" placeholder="请输入投保单位" />
				</u-form-item>
				<u-form-item label="生效日期 :" prop="startDate" borderBottom :required="true" @click="tiemOpen(2)">
					<u--input v-model="form.startDate" disabled disabledColor="#ffffff" placeholder="请选择本次生效日期"
						border="none"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item label="到期日期 :" prop="endDate" borderBottom :required="true" @click="tiemOpen(3)">
					<u--input v-model="form.endDate" disabled disabledColor="#ffffff" placeholder="请选择本次到期日期"
						border="none"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
			</view>
			<view class="formBg">
				<u-form-item label="保单费用 :" prop="insurancePayCost" :required="true" borderBottom>
					<u-input v-model="form.insurancePayCost" border="none" placeholder="请输入实付金额" />
					<text slot="right">元</text>
				</u-form-item>
			</view>
			<view class="formBg">
				<u-form-item label="保单明细 :" prop="timer" borderBottom>
					<u-icon slot="right" name="plus" @tap="collapseOpen('add')"></u-icon>
				</u-form-item>
				<view>
					<u-collapse :value="popupList.map((e,idx)=>{return idx})">
						<u-collapse-item v-for="(item,index) in popupList" :key="index">
							<view slot="title" class="u-align-items u-row-between">
								<text>项目{{index+1}} :</text>
								<text>{{item.itemCost}}元</text>
							</view>
							<view class="collBox">
								<u-cell-group>
									<u-cell title="保单项目 :" :value="item.insuranceItem">
										<u-icon name="edit-pen" slot='right-icon' :size='20'
											@tap="collapseOpen('edit',item,index)">
										</u-icon>
									</u-cell>
									<u-cell title="金额 :" :value="`${item.itemCost} 元`">
										<u-icon name="edit-pen" slot='right-icon' :size='20'
											@tap="collapseOpen('edit',item,index)">
										</u-icon>
									</u-cell>
									<u-cell title=" ">
										<u-icon name='trash-fill' slot='right-icon' :size='22' color='red'
											@tap="delBtn(index)"></u-icon>
									</u-cell>
								</u-cell-group>
							</view>
						</u-collapse-item>
					</u-collapse>
				</view>
			</view>
			<view class="formBg">
				<u-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic" multiple :maxCount="8"
					class="pding"></u-upload>
			</view>

			<view class="formBg pding wx_pd">
				<text>备注说明 :</text>
				<u--textarea v-model="form.remark" placeholder="请输入备注说明" confirmType="done"></u--textarea>
			</view>

			<view class="formBg">
				<u-form-item label="是否开启保险到期提醒 :" prop="valShow" borderBottom label-width="180">
					<u-switch slot="right" v-model="form.valShow" @change="switchChange"></u-switch>
				</u-form-item>
				<view class="map_detail_scoll" :class="{'active':form.valShow}" v-if="form.valShow">
					<u-form-item :required="true" label="提醒人员 :" prop="userNamer" borderBottom @tap='peopleShow=true'>
						<u--input v-model="form.userNamer" disabled disabledColor="#ffffff" placeholder="请选择提醒人员"
							border="none"></u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
					<u-form-item :required="true" label="提醒天数 :" prop="Daysr" borderBottom>
						<u-input v-model="form.Daysr" type='number' border="none" placeholder="请输入提醒天数" />
					</u-form-item>
					<u-form-item :required="true" label="提醒时间 :" prop="tiemr" borderBottom @tap='remindShow=true'>
						<u--input v-model="form.tiemr" disabled disabledColor="#ffffff" placeholder="请选择提醒时间"
							border="none"></u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>
			</view>
		</u--form>
		<!-- <view class="formBg pding btn">
			<u-button type="primary" color="#346CF2" text="提交确认" @click="submit()"></u-button>
		</view> -->
		<view class="formBg pding btn u-align-items u-row-around">
			<view class="" style="width: 70%;">
				<u-button type="primary" color="#346CF2" text="提交确认" @tap="submit()"></u-button>
			</view>
			<view class="" style="width: 25%;">
				<u-button type="primary" color="#346CF2" text="上报列表" @click="goRouter()"></u-button>
			</view>
		</view>

		<!-- 弹窗 -->
		<popupr :show="popupShow" :title="'添加保单项目'" @close="popupShow=false">
			<slot>
				<u--form :model="popupForm" :rules="popupRules" ref="popupForm" label-width="90" class="is-popup">
					<u-form-item label="保单项目 :" prop="insuranceItem">
						<u-input v-model="popupForm.insuranceItem" placeholder="请输入保单项目" />
					</u-form-item>
					<u-form-item label="金额 :" prop="itemCost">
						<u-input v-model="popupForm.itemCost" placeholder="请输入金额 :" type="number" />
						<text slot="right">元</text>
					</u-form-item>
				</u--form>
				<u-button type="primary" color="#346CF2" text="保存" @click="popuprBtn()"></u-button>
			</slot>
		</popupr>

		<!-- 日期选择 -->
		<u-datetime-picker :show="timeShow" v-model="timeMode" mode="date" :formatter="formatter" @cancel="tiemBtn"
			:closeOnClickOverlay='true' @close="tiemBtn" @confirm='confirms'>
		</u-datetime-picker>

		<!-- 车牌选择器 -->
		<u-picker :show="pickerShow" :columns="carList" keyName="carNumber" :closeOnClickOverlay='true'
			@confirm="carConfirm" @cancel="pickerBtn" @close="pickerBtn"></u-picker>

		<!-- 提醒人员 -->
		<popupr :show="peopleShow" title="选择提醒人员" @close="peopleShow=false">
			<slot>
				<people :list='peopleList' @peopleBtn='peopleVal' v-if="peopleShow"></people>
			</slot>
		</popupr>

		<!-- 时间选择 -->
		<u-datetime-picker :show="remindShow" v-model="remindTiem" mode="time" @cancel="remindShow=false"
			@close="remindShow=false" @confirm='remindConf'></u-datetime-picker>
	</view>
</template>

<script>
	import {
		uploadImg
	} from '@/config/consoler.js'
	import popupr from '../popupr/popupr.vue'
	import people from '../people/people.vue'
	export default {

		props: ['carList', 'typer'],
		components: {
			popupr,
			people
		},
		data() {
			let tiemrs = () => {
				let myDate = new Date()
				let er = (v) => {
					return v < 10 ? `0${v}` : v
				}
				return `${myDate.getHours()}:${er(myDate.getMinutes())}`
			}
			return {
				form: {
					valShow: false
				},
				rulesr: {
					'carNumber': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'insuranceDate': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'insuranceCode': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'insuranceCompany': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'startDate': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'endDate': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'userNamer': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'Daysr': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'tiemr': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'insurancePayCost': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
				},
				fileList: [],
				timeShow: false,
				timeMode: Number(new Date()),
				pickerShow: false,
				columnsr: [],
				maintain: [],
				popupidx: null,
				popupType: null,
				popupShow: false,
				popupForm: {
					insuranceItem: '',
					itemCost: '',
				},
				popupRules: {
					'insuranceItem': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'itemCost': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
				},
				popupList: [],
				tiemType: null,
				peopleShow: false,
				peopleList: [],
				remindShow: false,
				remindTiem: tiemrs(),
			}
		},
		mounted() {

		},
		methods: {
			goRouter() {
				uni.$u.route({
					url: '/pager/vehicleEscala/vehicleEscala',
					params: {
						id: this.typer,
					},
				})
			},
			// 选择时间
			remindConf(e) {
				this.$set(this.form, 'tiemr', e.value)
				this.remindShow = false
			},
			// 选择人员
			peopleVal(arrs) {
				let that = this
				if (arrs.length == 0) return uni.$u.toast('请选择人员')
				let listr = that.peopleList.map((v) => {
					return v.empId
				})
				let newListr = []
				arrs.forEach((v, index) => {
					if (!listr.includes(v.empId)) {
						newListr.push(v)
					}
				})
				let text = newListr.map((e) => {
					return e.name
				}).join()
				console.log(text, 'text');
				if (!text) return uni.$u.toast('请勿选择重复提醒人员')
				uni.showModal({
					title: '提示',
					content: `您本次选择提醒的人员：${text}`,
					success: function(res) {
						if (res.confirm) {
							that.peopleList.push(...newListr)
							let namer = that.peopleList.map((e) => {
								return e.name
							}).join()
							that.$set(that.form, 'userNamer', namer)
							that.peopleShow = !that.peopleShow
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				})
			},
			// 打开弹窗
			collapseOpen(type, t, i) {
				this.popupType = type
				if (type == 'edit') {
					this.popupidx = i
					this.$set(this.popupForm, 'itemCost', t.itemCost)
					this.$set(this.popupForm, 'insuranceItem', t.insuranceItem)
				}
				this.popupShow = true
			},
			// 车牌选择
			carConfirm(e) {
				let o = e.value[0]
				this.$set(this.form, 'carId', o.carId)
				this.$set(this.form, 'brandModelName', o.brandModelName)
				this.$set(this.form, 'carNumber', o.carNumber)
				this.pickerBtn()
			},
			// 是否开启保养提醒
			switchChange(e) {
				if (!e) {
					this.form.userNamer = null
					this.form.Daysr = null
					this.form.tiemr = null
				}
			},
			// 删除
			delBtn(idx) {
				this.popupList.splice(idx, 1)
			},
			// 保存保养项目
			popuprBtn() {
				this.$refs.popupForm.validate().then(res => {
					if (this.popupType == 'add') {
						this.popupList.push(JSON.parse(JSON.stringify(this.popupForm)))
					} else {
						this.popupList.splice(this.popupidx, 1, JSON.parse(JSON.stringify(this.popupForm)))
						this.$set(this.popupForm, 'itemCost', null)
						this.$set(this.popupForm, 'insuranceItem', null)
					}
					this.popupShow = false
					this.$refs.popupForm.resetFields()
				}).catch(errors => {
					console.log(errors, 'errors');
					uni.$u.toast('校验失败')
				})
			},

			// 选中时间
			confirms(e) {
				let valr = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				if (this.tiemType == 1) {
					this.$set(this.form, 'insuranceDate', valr)
				}
				if (this.tiemType == 2) {
					this.$set(this.form, 'startDate', valr)
				}
				if (this.tiemType == 3) {
					this.$set(this.form, 'endDate', valr)
				}
				this.tiemBtn()
			},
			// 时间打开
			tiemOpen(t) {
				this.tiemType = t
				this.tiemBtn()
			},
			// 开关
			tiemBtn() {
				this.timeShow = !this.timeShow
			},
			pickerBtn() {
				this.pickerShow = !this.pickerShow
			},
			// 时间格式化
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				return value
			},
			// 保存
			submit() {

				this.$refs.uForm.validate().then(res => {
					this.form.isOpenPush = this.form.valShow ? 1 : 0
					let obj = {
						date: this.form.Daysr,
						time: this.form.tiemr
					}
					if (this.popupList.length > 0) {
						this.form.insuranceCost = 0
						this.popupList.forEach(v => {
							this.form.insuranceCost += Number(v.itemCost) 
						})
					}
					this.form.advancePushTime = JSON.stringify(obj)
					this.form.pushUserIds = this.peopleList.filter(v => v.radio == true).map(e => e.userId)
						.join()
					this.form.insuranceDetail = JSON.stringify(this.popupList)
					this.form.imgUrls = this.fileList.map(res => {
						return res.urlr
					}).join()
				
					uni.$u.toast('提交成功')
					setTimeout(() => {
						this.$emit('preserva', this.form)
					}, 500)
				}).catch(errors => {
					uni.$u.toast('校验失败')
				})
			},
			// 上传附件
			afterRead(event) {
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList`].length
				lists.map((item) => {
					this[`fileList`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				lists.forEach(r => {
					uploadImg(r, 4).then(res => {
						if (res) {
							let item = this[`fileList`][fileListLen]
							this[`fileList`].splice(fileListLen, 1, Object.assign(item, {
								status: 'success',
								message: '',
								urlr: res
							}))
							fileListLen++
						} else {
							this[`fileList`].splice(fileListLen, 1)
							uni.$u.toast('上传失败')
						}
					})
				})
			},
			// 删除附件
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},

		}
	}
</script>

<style lang="scss" scoped>
	.is-bottom {
		padding-bottom: 120rpx;
	}

	.formBg {
		margin: 20rpx 0;
		background-color: #fff;

		/deep/.u-form-item__body__left__content__label {
			display: flex;
			justify-content: flex-end !important;
			padding-right: 10rpx;
		}

		/deep/.item__body__right__content__icon {
			padding-right: 30rpx;
		}

		/deep/.u-form-item__body {
			padding: 30rpx 0;
		}
	}

	.btn {
		position: fixed;
		bottom: 0;
		width: calc(100% - 60rpx);
		margin: 0 !important;
		border-top: 4rpx solid #f3f3f3;
		z-index: 99;
	}

	.pding {
		padding: 20rpx 30rpx;

	}

	.wx_pd {
		/* #ifdef MP-WEIXIN */
		padding-bottom: 120rpx;
		/* #endif */
	}

	.map_detail_scoll {
		height: 0;
		transition: all .3s linear 0s;
		overflow-y: auto;
	}

	.map_detail_scoll.active {
		height: 324rpx;
		transition: all .3s linear 0s;
	}

	/deep/.u-form-item__body__left__content__label {
		display: flex;
		justify-content: end !important;
		padding-right: 10rpx;
	}

	/deep/.u-collapse-item__content__text {
		padding: 0;
	}

	.is-popup {
		/deep/.item__body__right__content__icon {
			padding-left: 20rpx;
		}

		/deep/.u-form-item__body__left__content__required {
			left: 0;
		}
	}

	.is-bottom {
		/deep/.u-form-item__body {
			padding: 30rpx 0;
		}

		/deep/.item__body__right__content__icon {
			padding-right: 30rpx;
		}

		/deep/.u-form-item__body__left__content__required {
			left: 10rpx;
		}
	}
</style>
