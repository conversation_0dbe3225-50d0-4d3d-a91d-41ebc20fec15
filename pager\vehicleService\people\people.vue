<template>
	<view>
		<view class="search-top">
			<u-search placeholder="请输入员工姓名" :showAction="true" @clear='initr' v-model="keyword" actionText="搜索"
				@search='initr' @custom='initr' :animation="true"></u-search>
		</view>

		<scroll-view scroll-y="true" class="scroll-content">
			<view class="itemBox" :class="{'borders':item.radio}" v-for="(item,index) in peopleList" :key="index"
				@tap="radioBtn(item)">
				<u-icon name="checkmark-circle-fill" :color="item.radio?'#2979ff':'#ccc'" size="20"></u-icon>
				<view class="itemLeft">
					<text>部门 : {{item.orgName}}</text>
					<text>姓名 : {{item.name}}</text>
				</view>
			</view>
		</scroll-view>
		<u-button type="primary" text="确定" @tap='peopleBtn'></u-button>
	</view>
</template>

<script>
	import {
		employeelist,
	} from '@/config/consoler.js'
	export default {
		props: ['list'],
		data() {
			return {
				peopleList: [],
				keyword: ''
			};
		},
		mounted() {
			// if (this.list.length != 0) {
			// 	this.peopleList = this.list
			// } else {
			// 	this.initr()
			// }
			this.initr()
		},
		methods: {
			searchBtn() {
				console.log(1);
			},
			peopleBtn() {
				let arr = this.peopleList.filter((v) => {
					return v.radio
				})
				this.$emit('peopleBtn', arr)
			},
			// 选中人员
			radioBtn(item) {
				this.$set(item, 'radio', !item.radio)
			},
			// 获取列表
			initr() {
				employeelist({
					params: {
						pageNum: 1,
						pageSize: 999,
						name: this.keyword,
					}
				}).then(res => {
					// if (this.list.length != 0) {
					// 	this.list.forEach(v => {
					// 		res.pageList.forEach(vv => {
					// 			if (v.radio) {
					// 				if (v.empId == vv.empId) {
					// 					vv.radio = true
					// 				}
					// 			}
					// 		})
					// 	})
					// }
					res.pageList.forEach(res => {
						res.radio = false
					})
					this.peopleList = res.pageList
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.search-top {
		padding-bottom: 10rpx;
	}

	.scroll-content {
		width: 100%;
		height: 800rpx;
		margin-bottom: 20rpx;

		.itemBox {
			border: 1rpx solid #ccc;
			margin: 10rpx 0;
			padding: 10rpx;
			border-radius: 10rpx;
			display: flex;

			// justify-content: space-between;
			.itemLeft {
				display: flex;
				flex-direction: column;
				margin-left: 40rpx;
			}
		}

		.borders {
			border: 1rpx solid #3c9cff;
		}
	}
</style>
