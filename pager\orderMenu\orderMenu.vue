<template>
	<view class="my">
		<u-navbar title="我的订单" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="cell_list">
			<u-cell-group class="cellgroup" :border='false'>

				<block v-for='item in list' :key="item.id">
					<u-cell :title="item.title" @click="goPage(item.id,item.title)" isLink>
						<u-icon class="iconStyle" slot="icon" size="18" :name="item.img"></u-icon>
					</u-cell>
				</block>

				<!-- 	<u-cell title="企业用车订单" @click="goPage(1,'企业用车订单')" isLink>
					<u-icon class="iconStyle" slot="icon" size="18"
						name="https://zqcx.di-digo.com/app/image/icon-10.png"></u-icon>
				</u-cell>
				<u-cell title="私车公用订单" @click="goPage(2,'私车公用订单')" isLink>
					<u-icon class="iconStyle" slot="icon" size="18"
						name="https://zqcx.di-digo.com/app/image/icon-11.png"></u-icon>
				</u-cell>
				<u-cell title="网约车订单" :border='false' @click="goPage(3,'网约车订单')" isLink>
					<u-icon class="iconStyle" slot="icon" size="18"
						name="https://zqcx.di-digo.com/app/image/icon-12.png"></u-icon>
				</u-cell> -->
			</u-cell-group>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				indicatorDots: true,
				autoplay: false,
				interval: 2000,
				duration: 500,
				list: [{
						id: '1',
						title: '企业用车订单',
						img: 'https://zqcx.di-digo.com/app/image/icon-10.png'
					},
					{
						id: '2',
						title: '私车公用订单',
						img: 'https://zqcx.di-digo.com/app/image/icon-11.png'
					},
					{
						id: '3',
						title: '网约车订单',
						img: 'https://zqcx.di-digo.com/app/image/icon-12.png'
					},
				]
			};
		},
		onLoad(option) {
			let list = option.list.split(',')
			let att = this.list.filter(v => {
				return list.includes(v.id)
			})
			this.list = att
		},
		methods: {
			goPage(type, name) {

				uni.$u.route('/pager/order/order', {
					typer: type,
					namer: name
				});

				// }
			},
		}
	}
</script>

<style lang="scss">
	.my {
		padding-bottom: 80rpx;

		.name_box {
			padding: 30rpx 32rpx 0 32rpx;
			color: #fff;
			background: linear-gradient(30deg, #7D83FB 0%, #06AFFF 100%);
			overflow: hidden;

			.name_b {
				margin: 56rpx 0;
			}

			.name_sbox {
				margin-left: 24rpx;

				.name_t {
					font-size: 32rpx;
					margin-bottom: 24rpx;
				}

				.name_txt {
					font-size: 28rpx;
				}
			}

			.right_icon {
				position: absolute;
				right: 32rpx;
			}
		}

		.wallet_box {
			border-radius: 27rpx;
			background-color: #fff;
			padding: 32rpx;
			margin-top: -20rpx;
			font-size: 28rpx;

			.wallet {
				background-color: #06AFFF;
				color: #fff;
				padding: 20rpx 21rpx;
				border-radius: 13rpx;

				.wallet_money {
					text-align: center;

					.wallet_money_t {
						font-size: 44rpx;
						margin-top: 10rpx;
					}

					.wallet_money_b {
						font-size: 24rpx;
						margin-top: 10rpx;
						margin-bottom: 16rpx;
					}
				}
			}
		}

		.car_box {
			border-radius: 27rpx;
			background-color: #fff;
			font-size: 28rpx;
			margin-top: 32rpx;

			.car_tit {
				padding: 32rpx 32rpx 0 32rpx;
			}

			.car {
				background: #6F88FC;
				box-shadow: 0rpx 5rpx 7rpx 0rpx rgba(111, 136, 252, 0.67);
				border-radius: 13rpx;
				padding: 43rpx 69rpx 35rpx 47rpx;
				margin: 0 32rpx;

				.car_name_sbox {
					color: #fff;
					margin-left: 20rpx;

					.car_t {
						font-size: 28rpx;
						margin-bottom: 6rpx;
					}

					.car_txt {
						font-size: 24rpx;
					}
				}

				.car_icon {
					position: absolute;
					right: 79rpx;

					.car-btn {
						padding: 9rpx 20rpx;
						font-size: 24rpx;
						color: #346CF2;
						background-color: #fff;
						border-radius: 7rpx;
					}
				}
			}

			.swiper_box {
				margin-top: 34rpx;
				height: 220rpx;
			}
		}


		/deep/ uni-swiper .uni-swiper-dot {
			background-color: transparent;
			border: 1px solid #ccc;
		}

		/deep/ uni-swiper .uni-swiper-dot-active {
			background-color: #346CF2;
		}

		.cell_list {
			background-color: #fff;
			margin-top: 27rpx;

			/deep/ .u-line {
				border-color: #E9ECF7 !important;
				margin: 0 32rpx !important;
				width: calc(100% - 64rpx) !important;
			}

			.iconStyle {
				margin-right: 14rpx;
			}
		}
	}
</style>