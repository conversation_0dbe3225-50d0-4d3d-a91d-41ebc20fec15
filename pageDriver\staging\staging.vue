<template>
	<view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				optionr: {}
			}
		},
		onLoad(option) {
			let that = this
			that.optionr = option.objr
			if (option.objr != "{}") {
				let objs = JSON.parse(option.objr)
				uni.openLocation({
					latitude: Number(objs.latitude),
					longitude: Number(objs.longitude),
					name: objs.name,
					address: objs.address,
					complete: (res) => {
						that.optionr = '{}'
						console.log(that.optionr, 'res');
					}
				})
			}

		},
		onShow() {
			if (this.optionr == '{}') {
				uni.navigateBack({
					delta: 1
				})
			}
		},

		methods: {

		}
	}
</script>

<style>

</style>
