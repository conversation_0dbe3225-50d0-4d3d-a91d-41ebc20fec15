<template>
	<view>
		<u-navbar title="调度中心" :autoBack="true" :placeholder="true">
		</u-navbar>
		<u-cell-group :customStyle="{'background':'#fff'}">
			<u-cell title="所属车队" :value='datar.carTeamName'></u-cell>
			<u-cell title="车队编号" :value='datar.carTeamCode'></u-cell>
			<u-cell title="车队类型">
				<span slot='value'>{{datar.carTeamCategory==1?'企业自用':'对外服务'}}</span>
			</u-cell>
			<u-cell title="调度负责人" :value='datar.dispatchUserNames'>
				<span slot='value' style='flex: 2;text-align: right;'>{{datar.dispatchUserNames}}</span>
			</u-cell>
			<u-cell title="调度电话" :value='datar.carTeamMobile'>
				<a :href="'tel:'+datar.carTeamMobile" slot='value'
					style='text-decoration:none;display: flex;' >{{datar.carTeamMobile}}
					<!-- <u-image class="tab_icon" :width="22" :height="22" src="/static/svg/phone.svg"></u-image> -->
				</a>
			</u-cell>
		</u-cell-group>
	</view>
</template>

<script>
	import {
		appCardrivercarteam
	} from '@/config/consoler.js';
	export default {
		data() {
			return {
				datar: {}
			};
		},
		onLoad() {
			this.getDtaile()
		},
		methods: {
			getDtaile() {
				appCardrivercarteam().then(res => {
					this.datar = res
				})
			}
		}
	}
</script>


<style lang="scss" scoped>
	/deep/.u-cell__body {
		padding: 32rpx;
	}
</style>
