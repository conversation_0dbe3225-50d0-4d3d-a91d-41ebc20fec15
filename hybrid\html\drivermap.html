<!doctype html>
<html>
	<head>
		<meta charset="utf-8">
		<title>订单详情</title>
		<meta name="keywords" content="" />
		<meta name="description" content="" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
		<meta name="format-detection" content="telephone=no" />
		<!-- 	<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black"> -->
		<meta name="author" content="CSS5, css5.com.cn" />
		<link rel="stylesheet" href="https://i.gtimg.cn/vipstyle/frozenui/2.0.0/css/frozen.css">
		<link rel="stylesheet" href="css/font-awesome.min.css" />
		<script src="https://cdn.jsdelivr.net/npm/vue@2.7.10"></script>
		<!-- <script src="https://cdn.bootcss.com/vue/2.6.11/vue.js"></script> -->
		<!-- 引入样式 -->
		<link rel="stylesheet" href="js/element-ui/lib/theme-chalk/index.css">
		<!-- 引入组件库 -->
		<script src="js/element-ui/lib/index.js"></script>
		<link rel="stylesheet" type="text/css" href="css/hybird.css">
		<script type="text/javascript">
			window._AMapSecurityConfig = {
				securityJsCode: 'a0ec29ceac10861c863c75bcf78565a4',
				serviceHost: 'https://************:8080/_AMapService'
			}
		</script>

		<script src="https://webapi.amap.com/loader.js"></script>
		<!-- 引入样式和js文件 -->
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		<script type="text/javascript" src="js/axios.js"></script>
	</head>

	<style type="text/css">
		body {
			/* background-color: #999; */
			/* overflow: hidden !important;
			touch-action: none !important; */
		}
	</style>

	<body>
		<div id="app">

			<div class="backbtnr" v-if="urlObj.type=='H5'">
				<div class="backbtnr-top">
					<i class="fa fa-angle-left" @click="goBack()"></i>
					<h3>{{carWash(detail.compOrderState)}}</h3>
					<p></p>
				</div>
			</div>
			<div class="top_tips" v-show="!loderShow">
				<div class="top_tips_item" style="background: #ffffff">
					预计里程: <span>{{tipsObj.distance}}</span> 公里 - 预估用时: <span>
						{{tipsObj.time}}</span></div>
			</div>
			<!-- v-if="urlObj.type=='WEIXIN'" -->



			<div class="mapdown" style="bottom:10px;margin: 4px 7px;width:calc(100vw - 14px);" v-show="!loderShow">
				<div class="mapdown-down drmp-down" style="background-color: #fff;">
					<ul class="ui-list ui-list-function toum">
						<li class="mapdownbox">
							<div class="ui-list-info" style="padding-right: 0;">
								<h5 class="ui-nowrap" style="font-size: 16px;">{{detail.carNumber}}
									<span v-if="detail.numberColor">【{{detail.numberColor}}】</span>
								</h5>
								<p>{{detail.brandModelFullName}} {{detail.carColor}}</p>
							</div>

							<!-- 导航按钮 aplavat-->
							<!--  -->
							<!-- <div style="margin: 0 10px;"
								v-if="detail.compOrderState==50||detail.compOrderState==60||detail.compOrderState==80">
								<div class="dhBtn" @click="dhBtnFun()">
									导航
								</div>
							</div> -->

							<!-- 新点点点  司机头像(旧) -->
							<div class="mapdown-imgbox">

								<!-- <div class="ui-avatar aplavatimg">
									<span :style="'background-image:url('+detail.driverHeadUrl+')'"></span>
								</div>
								<div>
									{{detail.driverName}}
								</div> -->
								<span class="dhBtn"
									style="margin-top: 0;background-color: #E9F3FF;margin-right: 20px;">{{detail.psgNums}}
									人</span>


								<span class="ui-img-icon"
									style="background-image:url(https://zqcx.di-digo.com/app/image/diandian.png)"
									@click="openAir"></span>


							</div>


						</li>
					</ul>
					<div class="trip_s_airClass" v-if="airShow" style="top:0;right: 40px;">
						<div class="footbtn footbtnCenter" @click="telBtn(detail.psgMobile)"
							>
							<i class="fa fa-volume-control-phone"></i>联系乘客
						</div>
						<div class="footbtn "
							@click="telBtn(detail.dispMobile)" >
							<i class="fa fa-headphones"></i>联系调度
						</div>
						<div class="footbtn " @click="stepShow=true,airShow=false"
							:class='{"border":browserType!="Safari"}'>
							<i class="fa fa-list"></i>订单跟踪
						</div>
						<div class="footbtn border" @click="dhBtnFun()"
							v-if="detail.compOrderState==50||detail.compOrderState==60||detail.compOrderState==80">
							<i class="fa fa-location-arrow"></i>导航位置
						</div>
						<div class="footbtn " @click="canceShow=true,airShow=false" v-if="detail.compOrderState==30">
							<i class="fa fa-times-circle"></i>取消订单
						</div>
					</div>
					<div class="drmp_txt_box">
						<div class="drmp_txt ui-row-flex ui-whitespace cssFont">
							<div class="ui-col ui-col-2">
								<!-- <i class="fa fa-list-alt" style="color: #E9ECF7;"></i> -->
								<span style="color: #a5a5a5;">行程单号 :</span> {{detail.travelCode}}
								<span class='text-c' v-if='detail.dispatchMode==1'>拆</span>
							</div>
							<!-- <el-button type="text" @click='stepShow=true' style="padding: 0;">订单跟踪</el-button> -->
						</div>
						<div class="drmp_txt ui-row-flex ui-whitespace cssFont" v-if="detail.travelType==2">
							<div class="ui-col ui-col-2">
								<span style="color: #a5a5a5;">用车单位 :</span>
								{{detail.applyUnitName}}
							</div>
						</div>

						<div class="drmp_txt ui-row-flex ui-whitespace cssFont" v-if="detail.travelType==1">
							<div class="ui-col">
								<span style="color: #a5a5a5;">用车时间 :</span>
								<!-- <i class="fa fa-clock-o" style="color: #E9ECF7;"></i> -->
								{{detail.reserveStartTimeSeconds | timeFormat('mm月dd日 hh:MM')}} -
								{{detail.reserveEndTimeSeconds | timeFormat('mm月dd日 hh:MM')}}
							</div>
							<!-- <div class="ui-col">
								<i class="fa fa-clock-o" style="color: #E9ECF7;"></i>
								全日租(8小时100公里)
							</div> -->
						</div>

						<div class="drmp_txt ui-row-flex ui-whitespace cssFont" v-else>
							<div class="ui-col">
								<span style="color: #a5a5a5;">用车时间 :</span>
								{{detail.reserveStartTimeSeconds | timeFormat('mm月dd日 hh:MM')}}
							</div>
							<!-- <div class="ui-col">
								<i class="fa fa-clock-o" style="color: #E9ECF7;"></i>
								{{detail.valuationFullName}}
							</div> -->
						</div>
						<div class="drmp_txt ui-row-flex ui-whitespace cssFont" v-if="detail.travelType!=1">
							<div class="ui-col">
								<span style="color: #a5a5a5;">用车套餐 :</span>
								{{detail.valuationFullName}}
							</div>
						</div>
						<!-- <div class="drmp_txt ui-row-flex ui-whitespace">
							<div class="ui-col">
								<i class="fa fa-circle" style="color: #FF7031;"></i>
								{{detail.fromAddrName}}
							</div>
						</div>
						<div class="drmp_txt ui-row-flex ui-whitespace" v-if='detail.throughAddrInfo'>
							<div class="ui-col">
								<i class="fa fa-circle" style="color: #67c23a;"></i>
								途经：{{detail.throughAddrInfoName}}
							</div>
						</div>
						<div class="drmp_txt ui-row-flex ui-whitespace">
							<div class="ui-col">
								<i class="fa fa-circle" style="color: #404040;"></i>
								{{detail.toAddrName}}
							</div>
						</div> -->
					</div>

					<div class="ui-grid ui-grid-trisect mapbtnbox" v-if='false'>
						<ul>
							<li>
								<div class="ui-grid-info ui-border-r text-center">
									乘车人数：{{detail.psgNums}}人
								</div>
							</li>
							<li>
								<div class="ui-grid-info ui-border-r text-center">
									<a :href="'tel:'+detail.psgMobile">联系乘客</a>
								</div>
							</li>
							<li>
								<div class="ui-grid-info ui-border-r text-center">
									<a :href="'tel:'+detail.dispMobile">联系调度</a>
								</div>
							</li>

						</ul>
					</div>

				</div>

				<div class="drmp_btn_bg"
					v-if="detail.compOrderState!=100 && detail.compOrderState!=200 && detail.compOrderState!=205">
					<button v-if="detail.compOrderState==30" class="ui-btn-lg ui-btn-primary drmp_btn_color"
						@click="impShow=true">确认接单</button>
					<button v-if="detail.compOrderState==40" class="ui-btn-lg ui-btn-primary drmp_btn_color"
						@click="impShowOne=true">开始执行</button>
					<button v-if="detail.compOrderState==50" class="ui-btn-lg ui-btn-primary drmp_btn_color"
						@click="arriveSetOut()">到达出发地</button>
					<button v-if="detail.compOrderState==60" class="ui-btn-lg ui-btn-primary drmp_btn_color"
						@click="arriveDes()">到达目的地</button>
					<button v-if="detail.compOrderState==80" class="ui-btn-lg ui-btn-primary drmp_btn_color"
						@click="impHcShow=true">确定回场</button>
					<button v-if="detail.compOrderState==90" class="ui-btn-lg ui-btn-primary drmp_btn_color"
						@click="endOne()">结束订单</button>
				</div>

				<div class="map_detail"
					v-if="detail.compOrderState==100 || detail.compOrderState==200 || detail.compOrderState==205"
					style="margin: 7px;background: #f9f9f9;">
					<div class="map_detail_num u-flex u-row-between" @click="switchFun()">
						<div class="map_detail_num_t">
							<span>{{detail.actualFee}}</span>元
						</div>
						<div class="map_detail_num_r">
							<span>{{isshow? "收起":"展开"}}</span>
							<i :class="{ fa : true, 'fa-angle-down' : !isshow, 'fa-angle-up' : isshow}"></i>
						</div>
					</div>

					<div :class="{map_detail_scoll : true, active : isshow}">
						<ul class="map_detail_ul">
							<li class="u-flex u-row-between cssFont">
								<div class="detail_tit">
									<p>预估里程</p>
								</div>
								<div class="detail_val text-right">
									<p>{{(detail.aboutDistance/1000).toFixed(1)}}公里</p>
								</div>
							</li>
							<li class="u-flex u-row-between cssFont">
								<div class="detail_tit">
									<p>服务里程</p>
								</div>
								<div class="detail_val text-right">
									<p>{{(detail.actualUseCarMileage).toFixed(1)}}公里</p>
								</div>
							</li>
							<li class="u-flex u-row-between cssFont">
								<div class="detail_tit">
									<p>服务时长</p>

								</div>
								<div class="detail_val text-right">
									<p>{{detail.actualUseCarTime}}</p>
								</div>
							</li>
							<li class="u-flex u-row-between cssFont">
								<div class="detail_val" style="font-weight: bold;color:#000">
									<p>费用信息：</p>
								</div>
							</li>
							<div v-for="(item,index) in detail.feeDetail" :key="index">
								<div v-if="item.feeDetailCode==101">
									<li class="u-flex u-row-between">
										<div class="detail_val">
											<p>{{item.key}}</p>
											<p class="detail_label" v-if='item.detail'>({{item.detail}})</p>
										</div>
										<div class="detail_val text-right">
											<p>{{item.value}}元</p>
										</div>
									</li>
								</div>
								<div v-else>
									<li class="u-flex u-row-between" v-if="Number(item.value)>0">
										<div class="detail_val">
											<p>{{item.key}}</p>
											<p class="detail_label" v-if='item.detail'>({{item.detail}})</p>
										</div>
										<div class="detail_val text-right">
											<p>{{item.value}}元</p>
										</div>
									</li>
								</div>
							</div>

							<div v-for="(item,index) in detail.feeDetail" :key="200+index">
								<template v-if="item.feeDetailCode==200">
									<template v-if="item.unit&&item.unit.length>0">
										<div v-for="(ite,index) in item.unit">
											<li class="u-flex u-row-between" v-if="Number(ite.value)>0">
												<div class="detail_val">
													<p>{{ite.key}}</p>
													<p class="detail_label" v-if='ite.detail'>({{ite.detail}})</p>
												</div>
												<div class="detail_val text-right">
													<p>{{ite.value}}元</p>
												</div>
											</li>
										</div>
									</template>
								</template>
							</div>

							<li class="u-flex u-row-between">
								<div class="detail_tit" style="font-weight: 600;">
									<p>订单金额</p>
								</div>
								<div class="detail_valT text-right" style="font-weight: 600;">
									<p>{{detail.totalFee}} 元</p>
								</div>
							</li>

							<li class="u-flex u-row-between" v-if="detail.discountReduceFee">
								<div class="detail_tit" style="font-weight: 600;">
									<p>企业折扣</p>
								</div>
								<div class="detail_val text-right">
									<p style="font-weight: 600;">
										<span>-{{detail.discountReduceFee}}</span>
										<span style="color:#000">元</span>
									</p>
								</div>
							</li>

							<li class="u-flex u-row-between" v-if="detail.reduceFee">
								<div class="detail_tit" style="font-weight: 600;">
									<p>优惠金额</p>
								</div>
								<div class="detail_val text-right">
									<p style="font-weight: 600;">
										<span>-{{detail.reduceFee}}</span>
										<span style="color:#000">元</span>
									</p>
								</div>
							</li>

							<li class="u-flex u-row-between">
								<div class="detail_tit" style="font-weight: 600;">
									<p>实付金额</p>
								</div>
								<div class="detail_valT text-right" style="font-weight: 600;">
									<p>{{detail.actualFee}} 元</p>
								</div>
							</li>

						</ul>
						<!-- 	<div class="down_box_s">
							<button class="ui-btn-lg commonbtn">查看计价</button>
						</div> -->

						<!-- <div class="down_box">
							<button class="ui-btn-lg mu_btn">请确认行程费用，如有疑问请联系调度员</button>
						</div>
 -->

					</div>

				</div>

				<div class="star_box" v-if="detail.compOrderState==200 && detail.isIsv!=1">
					<div class="star_tit">
						服务评价
					</div>
					<div class="star_list" style="font-size: 24px;padding:4px 0;">
						<i v-for="(i,index) in starcount"
							:class="{fa :true, 'fa-star bright' : index < star ,'fa-star-o dark' : index >= star}"
							@click="clickStars(index)"></i>
					</div>
				</div>

			</div>


			<div id="container" style="height:calc(100vh - 160px)"></div>
			<div id="containeFu"></div>



			<!-- 组件 -->
			<div :class="{show:impShow,'ui-dialog':true}">
				<div class="ui-dialog-cnt-m">
					<div class="popup_tit">
					</div>
					<div class="drmp_dia_txt">
						是否确定接单？
					</div>
					<div class="u-flex u-row-between popup_btn_box">
						<button class="popup_btn_yes" @click="receiving()">确定</button>
						<button class="popup_btn_no" @click="impShow=false">取消</button>
					</div>
				</div>
			</div>
			<!-- 取消组件 -->

			<div :class="{show:canceShow,'ui-dialog':true}">
				<div class="ui-dialog-cnt-m">
					<div class="popup_tit">
					</div>
					<div class="drmp_dia_txt">
						是否确定取消订单？
					</div>
					<div class="u-flex u-row-between popup_btn_box">
						<button class="popup_btn_yes" @click="cancelOrder()">确定</button>
						<button class="popup_btn_no" @click="canceShow=false">取消</button>
					</div>
				</div>
			</div>
			<!-- 组件 -->
			<div :class="{show:impHcShow,'ui-dialog':true}">
				<div class="ui-dialog-cnt-m">
					<div class="popup_tit">
						确定回场
					</div>
					<div class="drmp_dia_txt">
						是否确认回场，结束任务订单？
					</div>
					<div class="u-flex u-row-between popup_btn_box">
						<button class="popup_btn_yes" @click="huichang()">确定</button>
						<button class="popup_btn_no" @click="impHcShow=false">取消</button>
					</div>
				</div>
			</div>


			<!-- 组件 -->
			<div :class="{show:impShowOne,'ui-dialog':true}">
				<div class="ui-dialog-cnt-m">
					<div class="popup_tit">
						填入开始里程数
					</div>
					<div class="dialog_flex">
						<div class="dialog_flex_l u-flex-6">
							开始里程数
						</div>
						<div class="dialog_flex_c">
							<input type="number" class="dialog_input" v-model="actualStartMileage"
								placeholder="请输入仪表盘当前里程数" style="font-size: 13px;" @input="inputAbout">
						</div>
						<div class="dialog_flex_r u-flex-3 u-text-right">
							公里
						</div>
					</div>

					<div class="u-flex startBox">
						<div :class="{ drmp_file_box: true, haveImgOne: ishaveImgOne }">
							<!-- <el-upload action="#" ref="uploadr" list-type="picture-card"
								:http-request="startHandleAvatar" v-if="!startImage">
								<i class="el-icon-plus"></i>
							</el-upload> -->

							<div class="upload-input" v-if="!startImage">
								<input type="file" accept="image/*" capture="camera" @change='flieChange($event,true)'
									class="upload-ipt">
								<span class="u-flex u-row-center" style="height: 100%;">
									<i class="el-icon-plus" style="font-size: 18px;font-weight: bold;"></i>
								</span>
							</div>


							<div style="position: relative;" v-else>
								<img class="startImg" :src="startImage" alt="">
								<div class="startGen">
									<i class="el-icon-zoom-in" @click="()=>{handlePictureCardPreview({url:startImage})}"
										style="margin-left:5px;"></i>
									<i class="el-icon-delete" style="margin-left:12px;" @click="startDelUpload"></i>
								</div>
							</div>



							<span>开始里程照片</span>
						</div>
					</div>

					<div class="u-flex u-row-between popup_btn_box">
						<button class="popup_btn_yes" @click="implement()">确定</button>
						<button class="popup_btn_no" @click="impShowOne=false">取消</button>
					</div>
				</div>
			</div>



			<!-- 结束订单 -->
			<!-- one -->
			<div :class="{show:endOneshow,'ui-dialog':true}">
				<div class="ui-dialog-cnt-m">
					<div class="mileage_tit">
						填入里程与费用
					</div>

					<ul class="ui-list ui-list-single ui-border-b">
						<li>
							<div class="ui-list-info">
								<h5 class="ui-nowrap">实际开始里程(公里)</h5>
								<div class="ui-txt-info">
									{{endParam.actualStartMileage}}
									<!-- 	<input type="number" class="mileage_input" placeholder="请输入" disabled
										v-model="endParam.actualStartMileage"> -->
									<!-- <i class="fa fa-edit"></i> -->
								</div>
							</div>
						</li>

						<li>
							<div class="ui-list-info">
								<h5 class="ui-nowrap" style="min-width: 140px;">实际结束里程(公里)</h5>
								<div class="ui-txt-info" style="min-width: 145px;">
									<input type="number" class="mileage_input" placeholder="请输入"
										v-model="endParam.actualEndMileage" @blur='endBlur' @input="inputAboutr" style="width:130px ;">
									<i class="fa fa-edit"></i>
								</div>
							</div>
						</li>

						<div class="u-flex u-row-between startEndBox">
							<div style="position: relative;">
								<img class="startImg" :src="endParam.startMileageImgUrl" alt="">
								<div class="startGen">
									<i class="el-icon-zoom-in"
										@click="()=>{handlePictureCardPreview({url:endParam.startMileageImgUrl})}"></i>
								</div>
								<div>开始里程照片</div>
							</div>


							<div :class="{ drmp_file_box: true, haveImg: ishaveImg }">
								<div>
									<div class="upload-input" v-if="!endImage">
										<input type="file" accept="image/*" capture="camera"
											@change='flieChange($event,false)' class="upload-ipt">
										<!-- <i class="el-icon-plus"></i> -->
										<span class="u-flex u-row-center" style="height: 100%;">
											<i class="el-icon-plus" style="font-size: 18px;font-weight: bold;"></i>
										</span>
									</div>

									<!-- v-if="urlObj.type=='H5'"  WEIXIN -->
									<!-- <el-upload action="#" ref="uploadr" list-type="picture-card"
										:http-request="handleAvatar" v-if="!endImage">
										<i class="el-icon-plus"></i>
									</el-upload> -->

									<div style="position: relative;" v-else>
										<img class="startImg" :src="endImage" alt="">
										<div class="startGen">
											<i class="el-icon-zoom-in"
												@click="()=>{handlePictureCardPreview({url:endImage})}"
												style="margin-left:5px;"></i>
											<i class="el-icon-delete" style="margin-left:12px;" @click="delUpload"></i>
										</div>
									</div>
								</div>
								<span>结束里程照片</span>
							</div>
						</div>


						<!-- wxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx -->
						<!-- <li>
						    <div class="ui-list-info">
						        <h5 class="ui-nowrap">实际开始时间：</h5>
						        <div class="ui-txt-info">
									<input type="datetime" class="mileage_input" placeholder="请输入" v-model="endParam.actualStartTime">
									<i class="fa fa-edit"></i>
								</div>
						    </div>
						</li>
						
						<li>
						    <div class="ui-list-info">
						        <h5 class="ui-nowrap">实际结束时间：</h5>
						        <div class="ui-txt-info">
									<input type="datetime" class="mileage_input" placeholder="请输入" v-model="endParam.actualEndTime">
									<i class="fa fa-edit"></i>
								</div>
						    </div>
						</li> -->
						<!-- wxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx -->
					</ul>

					<div class="mileage_tit_s">
						费用信息：
					</div>

					<ul class="ui-list ui-list-single ui-border-t u-m-b-10">
						<li class="ui-border-t" v-for="(item,index) in endParam.otherFee" :key="index">
							<div class="ui-list-info">
								<h5 class="ui-nowrap"  style="min-width: 140px;">{{item.key}}（元）：</h5>
								<div class="ui-txt-info">
									<input class="mileage_input" v-model="item.value" type="text" placeholder="请输入"
										@input="inputAbuo($event,item)">
									<i class="fa fa-edit"></i>
								</div>
							</div>
						</li>

					</ul>

					<div class="u-flex u-row-between popup_btn_box">
						<button class="popup_btn_nor"
							@click="endOneshow=false,endImage='',endParam.actualEndMileage=''">取消</button>
						<button class="popup_btn_nor popup_btn_color" @click="endTwo()">下一步</button>
					</div>

				</div>
			</div>

			<!-- two -->
			<div :class="{show:endTwoshow,'ui-dialog':true}">
				<div class="ui-dialog-cnt-m">
					<div class="mileage_tit">
						订单费用信息确认
					</div>

					<ul class="ui-list ui-list-single ui-border-b overHeight">
						<li class="ui-border-t">
							<div class="ui-list-info">
								<h5 class="ui-nowrap">预估里程：</h5>
								<div class="ui-txt-info u-m-r-15" style="color: #262626;">
									{{costDetail.aboutDistance/1000 | numFilter}}公里
								</div>
							</div>
						</li>

						<li class="ui-border-t">
							<div class="ui-list-info">
								<h5 class="ui-nowrap">服务里程：</h5>
								<div class="ui-txt-info u-m-r-15" style="color: #262626;">
									{{costDetail.actualDistance/1000 | numFilter}}公里
								</div>
							</div>
						</li>

						<li class="ui-border-t">
							<div class="ui-list-info">
								<h5 class="ui-nowrap">服务时长：</h5>
								<div class="ui-txt-info u-m-r-15" style="color: #262626;">
									{{costDetail.actualDuration| transDay}}
								</div>
							</div>
						</li>

						<!-- 自定义费用 费用信息-->
						<div v-for="(item,index) in costDetail.feeDetail" :key="index">
							<template v-if="item.feeDetailCode!=200">
								<li class="ui-border-t">
									<div class="fyli">
										<div class="u-flex u-row-between">
											<h5 class="ui-nowrap">{{item.key}}：</h5>
											<div class="ui-txt-info u-m-r-15" style="color: #262626;">{{item.value}}元
											</div>
										</div>
										<p class="detail_label" v-if='item.detail'>{{item.detail}}</p>
									</div>
								</li>
							</template>
						</div>
						<div v-for="(item,index) in costDetail.feeDetail" :key="200+index">
							<template v-if="item.feeDetailCode==200">
								<template v-if="item.unit&&item.unit.length>0">
									<div v-for="(ite,index) in item.unit" style="width: 100%;">
										<li class="ui-border-t">
											<div class="fyli">
												<div class="u-flex u-row-between">
													<h5 class="ui-nowrap">{{ite.key}}：</h5>
													<div class="ui-txt-info u-m-r-15" style="color: #262626;">
														{{ite.value}}元
													</div>
												</div>
												<p class="detail_label" v-if='item.detail'>{{ite.detail}}</p>
											</div>
										</li>
									</div>
								</template>
							</template>
						</div>


						<li class="ui-border-t">
							<div class="ui-list-info">
								<h5 class="ui-nowrap font_bold">订单金额：</h5>
								<div class="ui-txt-info u-m-r-15" style="color: #262626;">{{costDetail.totalFee}}元</div>
							</div>
						</li>

						<!-- <li class="ui-border-t">
							<div class="ui-list-info">
								<h5 class="ui-nowrap font_bold">优惠金额：</h5>
								<div class="ui-txt-info">
									<input class="mileage_input" v-model="endParam.reduceFee" type="text"
										placeholder="请输入">
									<i class="fa fa-edit"></i>
								</div>
							</div>
						</li> -->

						<li class="ui-border-t" v-if='detail.travelType==2'>
							<div class="ui-list-info">
								<h5 class="ui-nowrap font_bold">企业折扣金额：</h5>
								<div class="ui-txt-info u-m-r-15" style="color: red">
									- {{costDetail.discountReduceFee}}元
								</div>
							</div>
						</li>

						<li class="ui-border-t" v-if='detail.travelType==2'>
							<div class="ui-list-info">
								<h5 class="ui-nowrap font_bold">实付金额：</h5>
								<div class="ui-txt-info u-m-r-15" style="color: #262626;">
									{{costDetail.actualFee}}元
								</div>
							</div>
						</li>

					</ul>


					<div class="u-flex u-row-around popup_btn_boxr">
						<button class="popup_btn_nor" @click="endTwoshow=false">取消</button>
						<el-button icon="el-icon-arrow-left" type="primary" circle size='mini'
							@click="endTwoshow=false"></el-button>
						<button class="popup_btn_nor popup_btn_color" @click="endThree()" style="">确定</button>
					</div>

				</div>
			</div>
			<!-- 弹出图篇片大图 -->
			<el-dialog :visible.sync="dialogVisible" custom-class='dorg' :show-close='false' :fullscreen='true'>
				<div class="u-flex u-row-right" @click='()=>{dialogVisible=false}'>
					<i class="el-icon-close dorg_icon"></i>
				</div>

				<img width="100%" :src="dialogImageUrl" alt="" />
			</el-dialog>

			<!-- 订单跟踪 -->
			<div :class="{show:stepShow,'ui-dialog':true}">
				<div class="ui-dialog-cnt-m" style="padding:20px;height: 80%;">
					<el-steps direction="vertical" :space='50' :active="0" process-status='finish'
						style="overflow-y: auto;margin-bottom:5px;height: 90%;">
						<el-step v-for='item in detail.orderEventHis' icon="el-icon-s-custom">
							<div slot="icon"><i class="el-icon-s-custom" style="font-size: 18px;"></i></div>
							<div slot="title" style="font-size: 13px;">{{item.operatorName}}: {{item.evtDetContent}}
							</div>
							<div slot="description">
								<div>{{item.operatorTimes}}</div>
							</div>
						</el-step>
					</el-steps>
					<div class="u-flex" style="display: flex; justify-content: center;">
						<button class="popup_btn_no" @click="stepShow=false">关闭</button>
					</div>
				</div>
			</div>
			<!-- 导航弹出选择目的地 -->
			<el-drawer :visible.sync="drawer" :size="drawerSize" direction="btt" :show-close='false'
				:before-close="handleClose" style="z-index: 9999;">
				<div slot="title" class="drawer_tit">
					<div class="u-flex u-row-between">
						<span>要去哪里</span>
						<span style="color: #a5a5a5;" @click="drawer=false">关闭</span>
					</div>
					<ul class="drawerUl" style="overflow-y: auto;height: 50%;z-index: 99999;">
						<li class="u-flex u-row-between">
							<div>{{ detail.fromAddrName}}</div>
							<div class="gobtn"
								@click="gonav({siteLng:detail.fromLng,siteLat:detail.fromLat,siteAddrDetail:detail.fromAddrDetail,})">
								去这里</div>
						</li>
						<li class="u-flex u-row-between" v-for="(item,index) in detail.throughAddrInfo" :key="index">
							<div>{{item.siteAddrName}}</div>
							<div class="gobtn" @click="gonav(item)">去这里</div>
						</li>
						<li class="u-flex u-row-between">
							<div>{{ detail.toAddrName}}</div>
							<div class="gobtn"
								@click="gonav({siteLng:detail.toLng,siteLat:detail.toLat,siteAddrDetail:detail.toAddrDetail,})">
								去这里</div>
						</li>
					</ul>
				</div>
			</el-drawer>

			<!-- 加载中 -->
			<div :class="{show:loderShow,'ui-dialog':true}">
				<div class="loder">
					<div></div>
					<div></div>
					<div></div>
				</div>
			</div>

		</div>
	</body>
	<!-- 微信 JS-SDK 如果不需要兼容小程序，则无需引用此 JS 文件。 -->
	<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
	<!-- uni 的 SDK -->
	<script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script>
	<script>
		document.addEventListener('UniAppJSBridgeReady', function() {});
	</script>
	<script>
		new Vue({
			el: '#app',
			data: {
				isshow: false,
				starcount: 5,
				star: 0,
				urlObj: {},
				detail: {},
				dialogImageUrl: '',
				dialogVisible: false,
				disabled: false,
				impShow: false,
				impShowOne: false,
				endOneshow: false,
				endTwoshow: false,
				actualStartMileage: '',
				startMileageImgUrl: '',
				actualEndMileage: '',
				endParam: {
					actualEndMileage: '',
					actualEndTime: '',
					startMileageImgUrl: '',
					endMileageImgUrl: '',
					actualStartMileage: '',
					actualStartTime: '',
					otherFee: '',
					reduceFee: '',
				},

				costDetail: {},
				// 上传相关
				ishaveImgOne: false,
				ishaveImg: false,
				fileListOne: [],
				fileList: [],
				headersObj: {
					accessToken: null
				},
				uploadParam: {
					filePathType: '3'
				},
				// 确定回场
				impHcShow: false,
				// 导航弹窗
				drawer: false,
				drawerSize: "50%",
				posiAll: {},
				stepShow: false,
				endImage: "",
				startImage: "",
				loderShow: true,
				airShow: false,
				browserType: '',
				canceShow: false,
				filer: null,
				canvasRef: null,
				addres: null,
				tipsObj: {
					distance: '',
					time: '',
					show: false
				},
				loderhoust: "",
				contenShow: false
			},
			filters: {
				numFilter(value) {
					let realVal = "";
					if (!isNaN(value) && value !== "") {
						// 截取当前数据到小数点后两位,改变toFixed的值即可截取你想要的数值
						realVal = parseFloat(value).toFixed(1);
					} else {
						realVal = "--";
					}
					return realVal;
				},
				timeFormat(dateTime = null, fmt = 'yyyy-mm-dd') {
					// 如果为null,则格式化当前时间
					if (!dateTime) dateTime = Number(new Date())
					// 如果dateTime长度为10或者13，则为秒和毫秒的时间戳，如果超过13位，则为其他的时间格式
					if (dateTime.toString().length == 10) dateTime *= 1000
					const date = new Date(dateTime)
					let ret
					const opt = {
						'y+': date.getFullYear().toString(), // 年
						'm+': (date.getMonth() + 1).toString(), // 月
						'd+': date.getDate().toString(), // 日
						'h+': date.getHours().toString(), // 时
						'M+': date.getMinutes().toString(), // 分
						's+': date.getSeconds().toString() // 秒
						// 有其他格式化字符需求可以继续添加，必须转化成字符串
					}
					for (const k in opt) {
						ret = new RegExp(`(${k})`).exec(fmt)
						if (ret) {
							fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1]
								.length, '0')))
						}
					}
					return fmt
				},
				transDay(mss) {
					mss = mss * 1000;
					let days = Math.floor(mss / (1000 * 60 * 60 * 24));
					let hours = Math.floor((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
					let minutes = Math.floor((mss % (1000 * 60 * 60)) / (1000 * 60));
					let seconds = Math.round((mss % (1000 * 60)) / 1000);
					// return days + "天" + hours + "小时" + minutes + "分钟" + seconds + "秒";
					// return `${days?days+'天':''}${hours?hours+'小时':''}${minutes?minutes+'分钟':''}${seconds?seconds+'秒':''}`
					let timeStr = ''
					if (days > 0) {
						timeStr += days + "天"
					}
					if (hours > 0) {
						timeStr += hours + "小时"
					}
					let mr = Math.ceil(Number(minutes) + Number(`0.${seconds}`))
					timeStr += `${mr}分钟`
					return timeStr;
				}
			},
			computed: {
				heightStyle() {
					return {
						height: this.currentHeight + 'vh',
					}
				},
			},
			watch: {
				loderhoust(newr, used) {
					if (newr == 503 || newr == 502) {
						setTimeout(() => {
							uni.navigateTo({
								url: '/pages/login/login'
							})
						}, 1500)
					} else if (newr == 0) {
						setTimeout(() => {
							uni.navigateBack()
						}, 1500)
					}
				},
			},
			created() {
				this.loderShow = true
			},
			mounted() {
				this.urlObj = this.getQueryString()
				this.headersObj.accessToken = this.urlObj.accessToken
				this.getDetail()
				const container = document.getElementById("container");
				container.style.filter =
					"Alpha(opacity=10,style=0)"
				// container.style.opacity = "0.1"
				this.telBtm()
			},
			methods: {
				// 加水印
				flieChange(event, type) {

					const file = event.target.files[0];
					// console.log(file, 'file');
					if (file.type.match('image.*')) {
						const reader = new FileReader();
						reader.readAsDataURL(file);
						let canvas = document.createElement('canvas'); //新建canvas
						let ctx = canvas.getContext('2d'); //获取画笔对象

						reader.onload = (e) => {
							const img = new Image();
							img.onload = () => {
								canvas.width = img.width;
								canvas.height = img.height;

								ctx.drawImage(img, 0, 0);

								const date = new Date();
								const timestamp = date.toLocaleString();

								let maTop = 0
								let maLeft = 100
								/* 设置水印字体大小*/
								if (canvas.width < 1000) {
									ctx.font = 40 + 'px Arial'
									maTop = 50
									maLeft = 40
								}
								if (canvas.width < 2000) {
									ctx.font = 70 + 'px Arial'
									maTop = 110
									maLeft = 100
								} else {
									ctx.font = 150 + 'px Arial'
									maTop = 160
									maLeft = 100
								}

								ctx.fillStyle = '#fff';
								ctx.textAlign = 'left'; // 设置文本对齐方式为左对齐
								ctx.textBaseline = 'top'; // 设置文本基线为顶部对齐

								const textYBottom = canvas.height - maTop;

								ctx.fillText(`${this.detail.driverName}`, maLeft, textYBottom - maTop * 2);
								ctx.font = "normal bold 80px Arial";
								ctx.fillText(timestamp, maLeft, textYBottom - maTop + 40)
								ctx.font = "normal bold 80px Arial";
								ctx.fillText(`◉ ${addres?addres:'定位失败'}`, maLeft, textYBottom);

								const dataURLtoFile = (dataurl, filename) => {
									var arr = dataurl.split(',');
									var mime = arr[0].match(/:(.*?);/)[1]
									var bstr = atob(arr[1]);
									var n = bstr.length;
									var u8arr = new Uint8Array(n)
									while (n--) {
										u8arr[n] = bstr.charCodeAt(n)
									}
									return new File([u8arr], filename, {
										type: mime
									})
								}

								let newCanvas = canvas.toDataURL("image/jpeg", 0.2)
								let files = dataURLtoFile(newCanvas, file.name)
								// console.log(files, '压缩');
								setTimeout(() => {
									// this.endImage = URL.createObjectURL(files)
									this.downloadWatermarkedFile(files, type)
								}, 0)
								// canvas.toBlob((blob) => {
								// 	const atermarkedFile = new File([blob], file.name, {
								// 		type: file.type
								// 	});
								// });

							};
							img.src = e.target.result;
						};

					}
				},
				downloadWatermarkedFile(watermarkedFile, type) {
					let that = this
					let formData = new FormData()
					formData.append('file', watermarkedFile)
					formData.append('filePathType', 4)

					axios.post("sys/file/fileupload", formData, {
						headers: {
							"Content-Type": "multipart/form-data",
							accessToken: this.urlObj.accessToken,
							typer: 'up'
						},
					}).then(res => {
						this.loderhoust = res.data.code
						if (res.data.code == 1) {
							if (type) {
								that.startImage = URL.createObjectURL(watermarkedFile)
								that.startMileageImgUrl = res.data.data
							} else {
								that.endImage = URL.createObjectURL(watermarkedFile)
								that.endParam.endMileageImgUrl = res.data.data
							}
						} else {
							this.canvasRef.value = ''
						}
						this.tipsr(res.data.msg)
					})

					return
					const downloadLink = document.createElement('a');
					downloadLink.href = URL.createObjectURL(watermarkedFile);
					downloadLink.download = watermarkedFile.name;
					downloadLink.click();


				},
				cancelOrder(id) {
					axios.put("order/app/cancelorder/" + this.detail.applyId, {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
					}).then(res => {
						this.canceShow = false
						this.loderhoust = res.data.code
						this.tipsr(res.data.msg)
						if (res.data.code == 1) {
							uni.navigateBack({
								delta: 1,
								success: function() {
									beforePage.$vm.queryList(); // 执行前一个页面的刷新
								}
							});
						}
						// this.canceShow = false
						// uni.navigateBack({
						// 	delta: 1,
						// 	success: function() {
						// 		beforePage.$vm.queryList(); // 执行前一个页面的刷新
						// 	}
						// });
					})
				},
				openAir() {
					this.$set(this, 'airShow', true)
					setTimeout(() => {
						this.$set(this, 'airShow', false)
					}, 3000)
				},
				telBtn(tel) {
					if (!tel) return this.tipsr('暂无联系方式')
					window.location.href = 'tel://' + tel
				},
				inputAbuo(val, item) {
					return item.value = (val.target.value.match(/^\d*(\.?\d{0,2})/g)[0]) || null
				},
				inputAbout(val) {
					// val.target.value = (val.target.value.match(/^\d*(\.?\d{0,2})/g)[0]) || null
					//重新赋值给input
					this.$nextTick(() => {
						this.$set(this, 'actualStartMileage', (val.target.value.match(/^\d*(\.?\d{0,1})/g)[
							0]) || null)
					})
					// this.$set(this, 'actualStartMileage', Number(Math.floor(val.target.value * 100) / 100))
					// this.$set(this, 'actualStartMileage', Number(val.target.value.toString().match(/^\d+(?:\.\d{0,1})?/)))
				},
				inputAboutr(val) {
					this.$nextTick(() => {
						this.$set(this.endParam, 'actualEndMileage', (val.target.value.match(
							/^\d*(\.?\d{0,1})/g)[0]) || null)
					})
					// this.$set(this.endParam, 'actualEndMileage', Number(Math.floor(val.target.value * 100) / 100))
					// this.$set(this.endParam, 'actualEndMileage', Number(val.target.value.toString().match(/^\d+(?:\.\d{0,1})?/)))
				},
				startHandleAvatar(file) {
					this.startImage = URL.createObjectURL(file.file)
					let formData = new FormData()
					formData.append('file', file.file)
					formData.append('filePathType', 4)
					axios.post("sys/file/fileupload", formData, {
						headers: {
							"Content-Type": "multipart/form-data",
							accessToken: this.urlObj.accessToken,
							typer: 'up'
						},
					}).then(res => {
						this.loderhoust = res.data.code
						if (res.data.code == 1) {
							this.startImage = URL.createObjectURL(file.file)
							this.startMileageImgUrl = res.data.data
						} else {
							this.startImage = ''
							this.startMileageImgUrl = ""
						}
						this.tipsr(res.data.msg)
					})
				},
				startDelUpload() {
					this.startImage = ''
					this.startMileageImgUrl = ""
				},
				delUpload() {
					this.endImage = ''
					this.endParam.endMileageImgUrl = ''
				},
				handleAvatar(file) {
					console.log(file);
					this.endImage = window.URL.createObjectURL(file.file)
					console.log(file, 'file');
					let formData = new FormData()
					formData.append('file', file.file)
					formData.append('filePathType', 4)
					axios.post("sys/file/fileupload", formData, {
						headers: {
							"Content-Type": "multipart/form-data",
							accessToken: this.urlObj.accessToken,
							typer: 'up'
						},
					}).then(res => {
						this.loderhoust = res.data.code
						if (res.data.code == 1) {
							console.log(res.data, 'res.data');
							this.endImage = window.URL.createObjectURL(file.file)
							this.endParam.endMileageImgUrl = res.data.data

						} else {
							this.endImage = ''
							this.endParam.endMileageImgUrl = ''

						}
						this.tipsr(res.data.msg)
						// console.log(`/dpc/` + res.data.data);
					})
				},
				init(item) {
					let that = this
					// 高德地图控件
					AMapLoader.load({
						"key": "1cb53fa0e69dc44036161409f1d4039c", // 申请好的Web端开发者Key，首次调用 load 时必填
						"version": "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
						"plugins": ['AMap.Driving', 'AMap.Geocoder'], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
						"AMapUI": { // 是否加载 AMapUI，缺省不加载
							"version": '1.1', // AMapUI 版本
							"plugins": ['overlay/SimpleMarker'], // 需要加载的 AMapUI ui插件
						},
						"Loca": { // 是否加载 Loca， 缺省不加载
							"version": '2.0' // Loca 版本
						},
					}).then((AMap) => {
						///基本地图加载
						var map = new AMap.Map("container", {
							center: [item.toLng, item.toLat],
							resizeEnable: true,
							// isGestureScaleByMapCenter:false
						});

						//构造路线导航类
						var driving = new AMap.Driving({
							map: map
						});

						//  AMap.setPointToCenter(100,100); 118.116,24.467489   118.115948,24.470662  118.082837, 24.450496
						let markers = [{
								position: [that.detail.toLng, that.detail.toLat],
								text: that.detail.toAddrName
							},
							{
								position: [that.detail.fromLng, that.detail.fromLat],
								text: that.detail.fromAddrName
							},
						];
						let wayArr = []
						if (item.throughAddrInfo) {
							item.throughAddrInfo.forEach(v => {
								v.text = v.siteAddrName
								v.position = [v.siteLng, v.siteLat]
								wayArr.push(new AMap.LngLat(v.siteLng, v.siteLat))
							})
							markers.push(...item.throughAddrInfo)
						}

						// 根据起终点经纬度规划驾车导航路线
						driving.search(new AMap.LngLat(that.detail.fromLng, that.detail.fromLat), new AMap
							.LngLat(that.detail.toLng, that.detail.toLat), {
								waypoints: wayArr
							},
							function(status, result) {
								// result 即是对应的驾车导航信息，相关数据结构文档请参考  https://lbs.amap.com/api/javascript-api/reference/route-search#m_DrivingResult
								if (status === 'complete') {

									var distance = (item.aboutDistance / 1000).toFixed(1);
									var time = that.second(result.routes[0].time);
									that.tipsObj.distance = distance
									that.tipsObj.time = time

									// 添加多个点标记
									markers.forEach(marker => {

										let type = marker.text.length > 12 ? true : false
										let html = type ? that.createText(
											marker.text) : marker.text
										let sum = type ? -65 : -45

										const textMarker = new AMap.Text({
											text: html,
											position: marker.position,
											map: map,
											offset: new AMap.Pixel(0, sum)
										});
									});
									return
									//实例化信息窗体
									var infoWindow = new AMap.InfoWindow({
										isCustom: true, //使用自定义窗体
										content: that.createInfoWindow(distance, time),
										offset: new AMap.Pixel(0, -40)
									});

									infoWindow.open(map, position);
								}
								// map.setZoom(4);
								// map.setCenter([item.toLng,item.toLat]);
							});

						var geocoder = new AMap.Geocoder({
							radius: 1000 //范围，默认：500
						});

						// 获取定位
						geocoder.getAddress([this.urlObj.longitude, this.urlObj.latitude], function(status,
							result) {
							if (status === 'complete' && result.info === 'OK') {
								this.addres = result.regeocode.formattedAddress
							} else {
								this.addres = '定位失败'
								console.log(this.addres, 'result');
							}
						});

						map.on("complete", function() {
							setTimeout(() => {
								that.loderShow = false
							}, 800)
						})



					}).catch((e) => {
						this.tipsr('网络不佳,请返回重试')

						that.loderShow = false
						console.error(e); //加载错误提示
					});

				},

				goBack() {
					uni.navigateTo({
						url: '/pageDriver/driveIndex/driveIndex'
					});
					// uni.navigateBack();
				},
				createText(text) {
					return `<div class='mapText'>${text}</div>`
				},
				//构建自定义信息窗体
				createInfoWindow(distance, time) {
					var info = '<div class="mapTip">' +
						'<div class="mapTipt">' +
						'预计里程<span>' + distance + '</span>公里' +
						'</div>' +
						'<div class="mapTipd">' +
						'预估用时<span>' + time + '</span>' +
						'</div>' +
						'</div>';
					return info;
				},

				switchFun() {
					this.isshow = !this.isshow
				},

				getQueryString() {
					let url = window.location.href
					console.log(url)
					let p = url.split('?')[1]
					let keyValue = p.split('&');
					let obj = {};
					for (let i = 0; i < keyValue.length; i++) {
						let item = keyValue[i].split('=');
						let key = item[0];
						let value = item[1];
						obj[key] = value;
					}
					return obj
				},
				// 获取详情
				getDetail() {
					this.loderShow = true
					axios.get("order/app/driverorderinfo/" + this.urlObj.id, {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
						params: {}
					}).then(res => {
						this.loderhoust = res.data.code
						if (!res.data.success) return this.tipsr('请求出错')
						// wxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
						if (res.data.data.throughAddrInfo) {
							let arrs = JSON.parse(res.data.data.throughAddrInfo)
							this.$set(res.data.data, 'throughAddrInfo', arrs)
							this.$set(res.data.data, 'throughAddrInfoName', arrs.map(v => v.siteAddrName)
								.join(
									' → '))
						}
						this.detail = res.data.data
						this.star = this.detail.evaluateStar
						if (this.detail.feeDetail) {
							this.detail.feeDetail = JSON.parse(this.detail.feeDetail)
							this.detail.feeDetail.map((item, index) => {
								if (item.feeDetailCode == 200) {
									let unit = JSON.parse(item.unit)
									this.detail.feeDetail.push(...unit)
								}
								if (item.key == "自定义项目费用") {
									this.detail.feeDetail.splice(index, 1)
								}
							})
						}
						// wxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
						this.endParam.actualStartMileage = this.detail.actualStartMileage
						this.endParam.actualEndTime = this.detail.actualEndTime
						this.endParam.actualStartTime = this.detail.actualStartTime
						this.endParam.startMileageImgUrl = this.detail.startMileageImgUrl
						this.$nextTick(() => {
							this.init(res.data.data)
						})
					})
				},
				onlyGetDetail() {
					axios.get("order/app/driverorderinfo/" + this.urlObj.id, {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
						params: {}
					}).then(res => {
						this.loderhoust = res.data.code
						// wxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
						if (res.data.data.throughAddrInfo) {
							let arrs = JSON.parse(res.data.data.throughAddrInfo)
							this.$set(res.data.data, 'throughAddrInfo', arrs)
							this.$set(res.data.data, 'throughAddrInfoName', arrs.map(v => v.siteAddrName)
								.join(
									' → '))
						}
						this.detail = res.data.data
						this.star = this.detail.evaluateStar
						if (this.detail.feeDetail) {
							this.detail.feeDetail = JSON.parse(this.detail.feeDetail)
							this.detail.feeDetail.map((item, index) => {
								if (item.feeDetailCode == 200) {
									let unit = JSON.parse(item.unit)
									this.detail.feeDetail.push(...unit)
								}
								if (item.key == "自定义项目费用") {
									this.detail.feeDetail.splice(index, 1)
								}
							})
						}


						// wxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
						this.endParam.actualStartMileage = this.detail.actualStartMileage
						this.endParam.actualEndTime = this.detail.actualEndTime
						this.endParam.actualStartTime = this.detail.actualStartTime
						this.endParam.startMileageImgUrl = this.detail.startMileageImgUrl
						// wxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
					})
				},
				telBtm(tel) {
					var explorer = navigator.userAgent;
					let text = 'not'
					if (explorer.indexOf("MSIE") >= 0) {
						this.browserType = 'ie'
					} else if (explorer.indexOf("Firefox") >= 0) {
						this.browserType = 'firefox'
					} else if (explorer.indexOf("Chrome") >= 0) {
						this.browserType = 'Chrome'
					} else if (explorer.indexOf("Opera") >= 0) {
						this.browserType = 'Opera'
					} else if (explorer.indexOf("Safari") >= 0) {
						this.browserType = 'Safari'
					} else if (explorer.indexOf("Netscape") >= 0) {
						this.browserType = 'Netscape'
					}
				},
				second(value) {
					var theTime = parseInt(value); // 秒
					var middle = 0; // 分
					var hour = 0; // 小时

					if (theTime >= 60) {
						middle = parseInt(theTime / 60);
						theTime = parseInt(theTime % 60);
						if (middle >= 60) {
							hour = parseInt(middle / 60);
							middle = parseInt(middle % 60);
						}
					}
					var result = "";
					// if (theTime > 0) {
					// 	result = "" + parseInt(theTime) + "秒"
					// }
					if (middle > 0) {
						result = "" + parseInt(middle) + "分" + result;
					}
					if (hour > 0) {
						result = "" + parseInt(hour) + "小时" + result;
					}
					return result;
				},
				// 开始执行
				implement() {
					if (!this.startMileageImgUrl) return this.tipsr('请上传图片')
					axios.put("order/app/driverbeginorder/" + this.urlObj.id, {
						actualStartMileage: this.actualStartMileage,
						startMileageImgUrl: this.startMileageImgUrl
					}, {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
					}).then(res => {
						this.loderhoust = res.data.code
						this.impShowOne = false
						this.tipsr(res.data.msg)
						this.onlyGetDetail()
					})
				},
				// 确认接单
				receiving() {
					axios.put("order/app/driveracceptorder/" + this.urlObj.id, {
						isAccept: '1',
					}, {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
					}).then(res => {
						this.loderhoust = res.data.code
						this.impShow = false
						this.tipsr(res.data.msg)
						this.onlyGetDetail()
					})

				},
				// 到达出发地
				arriveSetOut() {
					axios.put("order/app/driverarrivefromaddr/" + this.urlObj.id, {}, {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
					}).then(res => {
						this.loderhoust = res.data.code
						this.tipsr(res.data.msg)
						this.onlyGetDetail()
					})
				},
				// 到达目的地
				arriveDes() {
					let that = this

					axios.put("order/app/driverarrivetoaddr/" + that.urlObj.id, {}, {
						headers: {
							accessToken: that.urlObj.accessToken,
						},
					}).then(res => {
						this.loderhoust = res.data.code
						this.tipsr(res.data.msg)
						that.onlyGetDetail()
					})
				},
				// 确定回场
				// one
				endOne() {
					// 1.根据包车套餐ID请求套餐详情，取出otherFee渲染；
					// 2.请求计算费用接口，算出费用展示出来；
					// 3.请求结束订单接口
					axios.get("car/app/tempvaluationinfo/" + this.urlObj.valuationId, {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
						params: {}
					}).then(res => {
						this.loderhoust = res.data.code
						if (res.data.code == '1') {
							this.endParam.otherFee = JSON.parse(res.data.data.otherFee)
							this.endOneshow = true
						} else {
							this.tipsr(res.data.msg)
						}
					})
				},
				endTwo() {
					// this.endParam.otherFee 要转换为字符串wxxxxxxxxxxxxxxxxxxxxxx
					if (!this.endParam.endMileageImgUrl) return this.tipsr('请上传结束里程图片')
					this.endParam.otherFee.forEach(res => {
						res.value = res.value ? res.value : 0
					})
					let param = {
						...this.endParam,
						otherFee: JSON.stringify(this.endParam.otherFee)
					}
					axios.post("order/app/costcalculate/" + this.urlObj.id, param, {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
					}).then(res => {
						this.loderhoust = res.data.code
						if (res.data.code == 1) {
							this.costDetail = res.data.data;
							if (this.costDetail.feeDetail) {
								this.costDetail.feeDetail = JSON.parse(this.costDetail.feeDetail)
								this.costDetail.feeDetail.map((item, index) => {
									if (item.feeDetailCode == 200) {
										item.unit = JSON.parse(item.unit)
									}
								})
							}
							this.endParam.reduceFee = res.data.data.reduceFee;
							this.endTwoshow = true;
							// this.endOneshow = false;
						} else {
							this.tipsr(res.data.msg)
						}
					})

				},
				endThree() {
					// this.endParam.otherFee 要转换为字符串wxxxxxxxxxxxxxxxxxxxxxx
					let param = {
						...this.endParam,
						otherFee: JSON.stringify(this.endParam.otherFee)
					}
					axios.put("order/app/driveroverorder/" + this.urlObj.id, param, {
						headers: {
							accessToken: this.urlObj.accessToken,
						},
					}).then(res => {
						this.loderhoust = res.data.code
						this.tipsr(res.data.msg)
						this.endTwoshow = false;
						this.endOneshow = false;
						this.onlyGetDetail()
					})
				},
				// 实际结束里程
				endBlur(e) {
					if (e.target.value < this.endParam.actualStartMileage) {
						this.$set(this.endParam, 'actualEndMileage', null)
						this.tipsr('实际结束里程要大于实际开始里程')
					}
				},
				// wxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
				// 上传相关
				handleAvatarSuccessOne(res, file) {

					if (res.code == 1) {
						this.startMileageImgUrl = res.data
						this.ishaveImgOne = true
						this.tipsr('上传成功')
					} else {
						this.fileListOne = []
						this.ishaveImgOne = false
					}
				},
				beforeAvatarUploadOne(file) {
					this.ishaveImgOne = true
				},
				handleRemoveOne(file) {
					this.fileListOne = []
					this.startMileageImgUrl = ""
					this.ishaveImgOne = false
				},
				handlePictureCardPreviewOne(file) {
					this.dialogImageUrl = file.url;
					this.dialogVisible = true;
				},
				handleAvatarSuccess(res, file) {

					if (res.code == 1) {
						this.endParam.endMileageImgUrl = res.data
						this.ishaveImg = true
						this.tipsr('上传成功')
					} else {
						this.fileList = []
						this.ishaveImg = false
					}
				},
				beforeAvatarUpload(file) {
					this.ishaveImg = true
				},
				handleRemove(file) {
					this.fileList = []
					this.endParam.endMileageImgUrl = ''
					this.ishaveImg = false
				},
				handlePictureCardPreview(file) {
					this.dialogImageUrl = file.url;
					this.dialogVisible = true;
				},

				// 确定回场
				huichang() {
					let that = this
					axios.put("order/app/drivercomeback/" + that.urlObj.id, {}, {
						headers: {
							accessToken: that.urlObj.accessToken,
						},
					}).then(res => {
						this.loderhoust = res.data.code
						that.impHcShow = false
						this.tipsr(res.data.msg)
						that.onlyGetDetail()
					})
				},

				// 评价
				clickStars(index) {
					if (this.detail.compOrderState == '205') {
						this.star = index + 1
						let param = {
							evaluateStar: this.star
						}
						axios.post("order/app/saveevaluated/" + this.urlObj.id, param, {
							headers: {
								accessToken: this.urlObj.accessToken,
							},
						}).then(res => {
							this.loderhoust = res.data.code
							this.tipsr(res.data.data)
						})
					} else {
						return false
					}
				},

				// 导航弹窗相关
				dhBtnFun() {
					let throughAddrInfoArr = this.detail.throughAddrInfo

					let type = this.detail.compOrderState
					let lngr = type == 60 ? this.detail.toLng : this.detail.fromLng
					let latr = type == 60 ? this.detail.toLat : this.detail.fromLat
					let namer = type == 60 ? this.detail.toAddrDetail : this.detail.fromAddrName

					if (type == 50) {
						this.goMap(lngr, latr, namer)
					} else {
						if (throughAddrInfoArr && throughAddrInfoArr.length > 0) {
							this.drawer = true
						} else {
							this.goMap(lngr, latr, namer)
						}
					}

				},
				goMap(lngr, latr, namer) {
					if (this.urlObj.type == 'H5') {
						window.open(
							`https://uri.amap.com/marker?position=${lngr},${latr}&name=${namer}&coordinate=gaode&callnative=1`,
							"_blank")
					} else {
						let objr = {
							latitude: Number(latr),
							longitude: Number(lngr),
							name: namer,
							address: namer,
						}
						uni.navigateTo({
							url: `/pageDriver/staging/staging?objr=${JSON.stringify(objr)}`,
						});
					}
				},
				handleClose(done) {
					this.drawer = false
					done();
					this.onlyGetDetail()
				},
				gonav(item) {
					if (this.urlObj.type == 'H5') {
						window.open(
							`https://uri.amap.com/marker?position=${item.siteLng},${item.siteLat}&name=${item.siteAddrDetail}&coordinate=gaode&callnative=1`,
							"_blank")
					} else {
						let objr = {
							latitude: Number(item.siteLat),
							longitude: Number(item.siteLng),
							name: item.siteAddrDetail,
							address: item.siteAddrDetail,
						}
						uni.navigateTo({
							url: `/pageDriver/staging/staging?objr=${JSON.stringify(objr)}`,
						});
					}
					// window.location.href =
					// 	`https://uri.amap.com/marker?position=${item.siteLng},${item.siteLat}&name=${item.siteAddrDetail}&coordinate=gaode&callnative=1`
					this.drawer = false
					// setTimeout(() => {
					// 	this.getDetail()
					// }, 1200)
					// alert(1)
				},
				tipsr(data, time) {
					let msg_c = document.createElement('div');
					msg_c.id = "msg_c"
					let alertForm = document.createElement('div');
					alertForm.id = "successMsg";
					alertForm.innerHTML = `<div class='tiprInner'>正其出行提示:</div><div class='tiprData'>${data}</div>`;
					msg_c.appendChild(alertForm);
					document.getElementsByTagName("body")[0].appendChild(msg_c);
					if (time) {
						setTimeout(function() {
							msg_c.style.display = "none";
							msg_c.remove()
						}, time)
					} else {
						setTimeout(function() {
							msg_c.style.display = "none";
							msg_c.remove()
						}, 1500)
					}
				},

				carWash(e) {
					return e == 10 ? '待审批' : e == 95 ? '行后待审批' : e == 20 ? '待派车' : e == 24 ? '租赁待审批' : e == 25 ?
						'租赁待派车' :
						e == 30 ? '待接单' : e == 40 ? '待执行' : e == 50 ? '前往出发地' : e == 60 ? '前往目的地' : e == 70 ? '进行中' :
						e ==
						80 ? '到达目的地' : e == 90 ? '已回场' : e == 100 ? '待确认' : e == 200 ? '已完成' : e == 205 ? '待评价' : e ==
						210 ?
						'已取消' : e == 220 ? '审批驳回' : e == 230 ? '调度驳回' : e == -1 ? '订单异常' : ''
				},

			}
		});
	</script>

</html>