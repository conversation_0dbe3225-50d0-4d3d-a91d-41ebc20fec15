const http = uni.$u.http;

import qs from "qs";

import { SET_TOKEN, GET_TOKEN, REMOVE_TOKEN } from "@/common/token.js";

/**
 * 公共接口
 */

// 上传附件
export const uploadImg = (event, type) => {
  uni.showLoading({
    title: "加载中",
    mask: true,
  });
  let urlr;
  // #ifdef MP-WEIXIN
  // urlr = 'https://zqcxdev.di-digo.com/api/zqcx/sys/file/fileupload'
  urlr = "https://zqcx.di-digo.com/api/zqcx/sys/file/fileupload";
  // #endif
  // #ifdef H5
  urlr = "/dpc/sys/file/fileupload";
  // #endif
  return new Promise((resolve, reject) => {
    // console.log(GET_TOKEN(), 'GET_TOKEN()')
    let a = uni.uploadFile({
      url: urlr,
      filePath: event.url,
      name: "file",
      formData: {
        filePathType: type,
      },
      header: {
        accessToken: GET_TOKEN(),
      },
      success: (res) => {
        setTimeout(() => {
          try {
            let data = JSON.parse(res.data);
			if(data.code==503){
				uni.removeStorageSync('userInfo');
				REMOVE_TOKEN()
				uni.showToast({
				  title: "用户登录信息已失效，请重新登录",
				  icon: "none",
				  duration: 3000,
				});
				
				setTimeout(()=>{
					uni.navigateTo({
						url: '/pages/login/login'
					})
				},1500)
				return
			}
			console.log("返回数据查看:", data); // 只打印前100个字符，避免日志过长
            resolve(data.data);

          } catch (error) {
            console.error("JSON解析错误---:", error);
            console.log("返回数据:", res.data.substring(0, 100)); // 只打印前100个字符，避免日志过长
            
            // 如果返回的是HTML或其他非JSON格式，可能是服务器错误
            if (typeof res.data === "string" && res.data.trim().startsWith("<")) {
              uni.showToast({
                title: "服务器返回了非JSON数据，请稍后再试",
                icon: "none",
                duration: 3000
              });
              // 返回null或空对象，避免后续代码出错
              resolve(null);
            } else {
              // 尝试直接返回原始数据
              resolve(res.data);
            }
          }
        }, 1000);
      },
      complete: () => {
        uni.hideLoading();
      },
    });
  });
};

// 调度
export const appCardrivercarteam = (params) =>
  http.get(`/merchant/app/drivercarteam`, params);

/**
 * 车务上报
 */
// 车辆列表
export const appCarlist = (params) => http.get(`/car/carlist`, params);
// 油卡选择
export const appCardlist = (params) => http.get(`/car/oilcardlist`, params);
// 车辆选择
export const appTakecarinfoby = (data, config = {}) =>
  http.post("/car/plf/takecarinfobycompid", data, config);
// 人员
export const employeelist = (params) =>
  http.get(`/merchant/app/employeelistbyorg`, params);
// 新增车务信息
export const appCartraffic = (data, config = {}) =>
  http.post("/car/app/cartraffic", data, config);
// 查询车务详情
export const appCartrafficinfo = (params) =>
  http.get(`/car/app/cartrafficinfo/${params.params.id}`, params);
// 查询车务列表
export const appCartrafficlist = (params) =>
  http.get(`/car/app/cartrafficlist`, params);

/**
 * 车辆管理
 */
// 获取品牌车辆
export const carGetcarbrandlist = (params) =>
  http.get(`/car/carbrandlist`, params);
// 获取品牌车辆系列
export const carGetcarbrandmodellist = (params) =>
  http.get(`/car/carbrandmodellist`, params);
// 获取车队列表
export const carGetcarteamlist = (params) =>
  http.get(`/car/app/carteamlist`, params);

/**
 * 我的车辆
 */
// 车辆列表
export const carGetcarbyuserList = (params) =>
  http.get(`/car/app/getcarbyuserlist`, params);
// 新增车辆
export const carApplyForcar = (data, config = {}) =>
  http.post("/car/app/applyforcar", data, config);

/**
 * 私车管理
 */
// 列表
export const carGetprivatecarList = (params) =>
  http.get(`/car/app/privatecarlist`, params);
// 详情
export const carGetcarinfo = (params) =>
  http.get(`/car/app/carinfo/${params.id}`);
// 编辑
export const carPostEditorcar = (data) =>
  http.post(`/car/app/editorcar/${data.carId}`, data);
// 删除
export const carDeletecar = (params) =>
  http.delete(`/car/app/deletecar/${params.id}`);
// 同意、驳回
export const carPutSelfcarcheck = (data) =>
  http.put(`/car/app/selfcarcheck/${data.id}`, data);
// 补贴设置
export const carPutSetsubsidies = (data) =>
  http.put(`/car/app/setsubsidies/${data.id}`, data);
export const carPostUndocar = (data) =>
  http.post(`/car/app/undocar/${data.id}`);
// 私车行程详情
export const carPrivateorderinfo = (params) =>
  http.get(`/order/app/privateorderinfo/${params.id}`);
// 私车行程开始执行
export const carPrivatestateorder = (data) =>
  http.put(`/order/app/privatestateorder/${data.id}`, data);
// 私车行程结束执行
export const carPrivaRivedes = (data) =>
  http.post(`/order/app/privatearrivedestination/${data.id}`, data);

/**
 * 网约车管理
 */
// 网约车提交申请
export const applyNetCar = (data, config = {}) =>
  http.post("/order/applyNetCar", data, config);
// 网约车取消行程
export const cancelnetorder = (data) =>
  http.put(`/order/app/cancelnetorder/${data.id}`, data);
// 网约车申请详情
export const applyNetCarDetai = (params) =>
  http.get(`/order/applyNetCar/${params.id}`);
// 网约车审批详情
export const checkNetorder = (params) =>
  http.get(`/order/app/checkNetorder/${params.id}`);
// 网约车用车详情
export const applynetcarurl = (params) => {
  let url =
    params.type && params.type == "true"
      ? "/order/app/didiorderdetailurl"
      : "/order/app/applynetcarurl";
  return http.get(`${url}/${params.id}`);
};
// 网约车行程列表
export const applyNetcarlist = (params) =>
  http.get(`/order/app/netcarlist`, params);
//  网约车常用乘车人列表
export const passengerlist = (params) =>
  http.get(`/order/app/passengerlist`, params);
// 网约车添加常用乘车人
export const addpassenger = (data, config = {}) =>
  http.post("/order/app/addpassenger", data, config);
// 网约车添加常用乘车人
export const newapplynetcar = (data, config = {}) =>
  http.post("/order/app/netcarsubmit", data, config);

/**
 *消息管理
 */
// 列表
export const msgList = (params) => http.get(`/sys/messagelist`, params);
// 详情
export const msgInfo = (params) => http.get(`/sys/messageinfo/${params.id}`);
// 删除
export const msgDel = (params) => http.delete(`/sys/message/${params.id}`);
// 全部已读
export const msgAll = (data) => http.put(`/sys/messagesetreadall`, data);
// 消息置顶
export const msgTopr = (data) => http.put(`/sys/messagesettop/${data.id}`);
// 意见反馈
export const msgFeedBack = (data) => http.post(`/sys/feedback`, data);
// 未读消息
export const messagecount = (params) => http.get(`/sys/messagecount`);

// 逆解析地址
export const gdapi = (res) => {
  let url = `https://zqcx.di-digo.com/gdapi/v3/geocode/regeo?output=JSON&location=${res.longitude},${res.latitude}&key=f50ac9b4e67fc88593738c211a6658cc&radius=1000&extensions=all`;
  return new Promise((resolve, reject) => {
    uni.request({
      url: url, //仅为示例，并非真实接口地址。
      method: "get",
      success: (res) => {
        if (res.statusCode == 200 && res.data.status == 1) {
          resolve(res.data.regeocode);
        } else {
          reject(res.data);
        }
      },
      fail(err) {
        reject(res.data);
      },
    });
  });
};

// 高德地图获取代理
export const amapApi = (res) => {
  let url = `https://zqcx.di-digo.com/gdapi/v3/direction/driving?output=JSON&${res}&key=f50ac9b4e67fc88593738c211a6658cc&radius=1000&extensions=all`;
  return new Promise((resolve, reject) => {
    uni.request({
      url: url, //仅为示例，并非真实接口地址。
      method: "get",
      success: (res) => {
        if (res.statusCode == 200 && res.data.status == 1) {
          resolve(res.data.regeocode);
        } else {
          reject(res.data);
        }
      },
      fail(err) {
        reject(res.data);
      },
    });
  });
};
