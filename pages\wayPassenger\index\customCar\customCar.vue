<template>
	<view class="customCar">
		<u-navbar title="网约车" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="entp_content" style="padding-bottom:140rpx;">
			<u-cell-group class="cellgroup " :border='false'>
				<u-cell @click="openbBtn(1)">
					<span slot='title' class="appr_title">{{ textWash(approverType.regulationName)}}</span>
				</u-cell>
				<view class="cell_type">
					<view class="u-flex  mar_bot ">
						<span class="cell_type_title">用车方式:</span>
						<span>{{textWash(approverType.settingPassenge)}}</span>
					</view>
					<view class="u-flex mar_bot">
						<span class="cell_type_title">可用车型:</span>
						<span>{{dataWash(approverType.availableCarTypeList)}}</span>
					</view>
					<view class="u-flex mar_bot">
						<span class="cell_type_title">同城限制:</span>
						<span>{{textWash(approverType.settingAcrossCity==0?'仅限同城可用':approverType.settingAcrossCity==1?'允许跨城':'允许部分城市跨城')}}</span>
					</view>

					<view class="u-flex mar_bot">
						<span class="cell_type_title">用车路线:</span>
						<span style="flex: 1;">{{textWash(vehicleList[approverType.settingAvailableAddress])}}</span>
					</view>
					<view class="u-flex mar_bot">
						<span class="cell_type_title">每单限额:</span>
						<span style="flex: 1;">{{textWash(approverType.limitMoney)}}</span>
					</view>
					<!-- <view class="u-flex mar_bot">
							<span class="cell_type_title">每日限额:</span>
							<span style="flex: 1;">{{textWash(approverType.availableCarTypeList)}}</span>
						</view> -->
					<view class="u-flex mar_bot">
						<span class="cell_type_title">用车时段:</span>
						<view style="flex: 1;">
							<view v-for="item in approverType.availableTime" :key="item.key">
								{{`${item.name} ${item.startTime}-${item.endTime}`}}
							</view>
							<!-- <block v-for="item in approverType.availableTime" :key="item.key">
								</block> -->
						</view>
					</view>
				</view>
			</u-cell-group>

			<u-cell-group class="cellgroup " :border='false'>
				<u-cell title="用车日期" :value="objData.applyTimeName" isLink @click="openbBtn(2,'dateShow')"></u-cell>
				<u-cell title="用车时段" :value="objData.dateName" isLink @click="openbBtn(3,'timeShow')"></u-cell>
				<u-cell title="用车次数" v-if="approverType.settingLimitEmp==1">
					<view class="" slot="value">
						<u-number-box v-model="objData.useCarNumber" integer></u-number-box>
					</view>
				</u-cell>
			</u-cell-group>

			<u-cell-group class="cellgroup " :border='false' v-if="option.isCheck=='true'">

				<u-cell title="审批人" :value="objData.flowName" isLink @click="openbBtn(5)"></u-cell>
			</u-cell-group>
			<u-cell-group class="cellgroup " :border='false'>
				<u-cell title="申请原因 (必填)" :border='false'>
					<u--textarea v-model="objData.description" placeholder="请输入申请原因" slot="label" confirmType='done'>
					</u--textarea>
				</u-cell>
			</u-cell-group>
		</view>


		<view class="footer_box">
			<u-button type="primary" color="#346CF2" text="确认" @click="submit"></u-button>
		</view>

		<u-datetime-picker :show="dateShow" v-model="dateModel" mode='date' @close="falseBtn('dateShow')"
			@cancel="falseBtn('dateShow')" :formatter='formattes' @confirm='confirm'></u-datetime-picker>

		<u-datetime-picker :show="timeShow" :title='dateType==3?"开始时间":"结束时间"' v-model="timeModel" mode='time'
			@close="falseBtn('timeShow')" @cancel="falseBtn('timeShow')" :formatter='formattes'
			@confirm='confirm'></u-datetime-picker>
	</view>
</template>

<script>
	import {
		regulationinfo,
	} from '@/config/api.js';
	import {
		applyNetCar,
		applyNetCarDetai
	} from '@/config/consoler.js';
	import {
		formatter
	} from "@/common/commonType.js"
	const timer = () => {
		const now = new Date();
		const hours = now.getHours();
		const minutes = now.getMinutes();
		return `${hours}:${minutes}`
	}
	export default {
		data() {
			return {
				userInfo: {},
				option: {},
				objData: {
					applyTime: '',
					description: '',
					startDate: '',
					endDate: '',
					flowId: '',
					regulationId: '',
					useCarNumber: 1,
				},
				dateType: null,
				dateModel: Number(new Date()),
				timeModel: timer(),
				dateShow: false,
				timeShow: false,
				approverType: {
					regulationName: ''
				},
				vehicleList: ['不限', '限定出发地', '限定目的地', '知道出发地或者目的地']
			}
		},
		onLoad(option) {
			this.userInfo = this.$common.getItem('userInfo')
			if (option.type) {
				this.option = option
				this.getDetai()
			} else {
				this.option = option
				this.getApprover()
			}

		},
		methods: {
			getDetai() {
				applyNetCarDetai({
					id: this.option.applyId
				}).then(res => {

					console.log(res, 'res');
					this.$set(this.option, 'isCheck', true)
					this.$set(this.option, 'regulationType', 1)
					this.$set(this.option, 'userId', res.userId)
					this.$set(this.option, 'regulationId', res.regulationId)


					let times = new Date(res.applyTime).getTime()
					let dateNamesplis = res.dateName.split('-')
					this.objData.applyTime = uni.$u.timeFormat(times, 'yyyy-mm-dd')
					this.objData.applyTimeName = uni.$u.timeFormat(times, 'mm月dd日')

					this.objData.startDate = dateNamesplis[0].split(' ')[0]
					this.objData.endDate = dateNamesplis[1]

					this.objData.dateName = res.dateName
					this.objData.dateName = res.dateName
					this.objData.description = res.description
					this.objData.flowId = res.flowId
					this.objData.flowName = res.flowName
					this.objData.sort = res.sort
					this.objData.netApplyId = this.option.applyId

					if (this.option.userId && this.option.regulationId) {
						this.getApprover()
					}
				})
			},
			submit() {
				let that = this
				if (!that.objData.applyTimeName) return uni.$u.toast('请选择用车日期')
				if (!that.objData.dateName) return uni.$u.toast('请选择开始时间和结束时间')
				if (!that.objData.flowName && that.option.isCheck == 'true') return uni.$u.toast('请选择审批人')
				if (!that.objData.description) return uni.$u.toast('请输入申请原因')
				if (that.option.type) {
					that.$set(that.objData, 'isUpdate', 1)
				}
				uni.showModal({
					content: '是否提交用车申请？',
					success: (res) => {
						if(!res.confirm)return
						
						let fals = false
						
						let obj = {
							applyTime: `${that.objData.applyTime} ${that.objData.startDate}:00`,
							description: that.objData.description,
							regulationId: that.option.regulationId,
							startDate: `${that.objData.applyTime} ${that.objData.startDate}:00`,
							endDate: `${that.objData.applyTime} ${that.objData.endDate}:00`,
							useCarNumber: that.objData.useCarNumber,
							flowName: that.objData.flowName,
							flowId: that.objData.flowId
						}
						applyNetCar(obj).then(res => {
							fals = true
							uni.$u.toast(res)
							if (that.option.isCheck == 'true') {
								uni.redirectTo({
									url: '/pages/wayPassenger/workBench/apply/apply?typer=' + 3
								});
							} else {
								uni.switchTab({
									url: '/pages/wayPassenger/trip/trip'
								})
							}
						})
						if (that.option.type) {
							that.getDetai()
						}
					}
				})
			},

			confirm(event) {
				if (this.dateType == 2) {
					const times = uni.$u.timeFormat(event.value, 'yyyy-mm-dd')
					const timesName = uni.$u.timeFormat(event.value, 'mm月dd日')
					this.$set(this.objData, 'applyTime', times)
					this.$set(this.objData, 'applyTimeName', timesName)
					this.falseBtn('dateShow')
				} else if (this.dateType == 3) { // 3跟4 是开始和结束
					this.$set(this.objData, 'startDate', event.value)
					this.timeShow = false
					this.dateType = 4
					setTimeout(() => {
						this.timeShow = true
					}, 500)
				} else if (this.dateType == 4) {
					if (this.objData.startDate > event.value) return uni.$u.toast('请选择大于开始时间')
					this.$set(this.objData, 'endDate', event.value)
					this.$set(this.objData, 'dateName', `${this.objData.startDate} - ${this.objData.endDate}`)
					this.dateType = null
					this.falseBtn('timeShow')
				}
			},
			openbBtn(t, name) {
				this.dateType = t
				if (name) {
					this[name] = true
				}
				if (t == 5) {
					uni.$u.route('/pages/wayPassenger/index/approver/approver', {
						regulationId: this.option.regulationId,
						psgUserIdr: this.option.psgUserId,
						type: true,
					});
				}
			},
			async getApprover() {
				regulationinfo({
					params: {
						reguid: this.option.regulationId,
						psgUserId: this.option.userId,
					}
				}).then((data) => {
					console.log(data, '------------');
					if (data.settingPassenger) {
						let sett = data.settingPassenger.split(",")
						data.settingPassenge = `${this.settWash(sett[0])} ${this.settWash(sett[1])}`
					}
					data.availableTime = JSON.parse(data.availableTime)
					this.approverType = data
				})
			},

			approverVal(val) {
				this.$set(this.objData, 'flowId', val.userId)
				this.$set(this.objData, 'flowName', val.fullName)
				this.$set(this.objData, 'sort', val.sort)
			},
			settWash(v) {
				return v == 1 ? '自叫车' : v == 2 ? '代叫车' : ''
			},
			dataWash(arr) {
				let tetx = '-'
				if (arr) {
					tetx = arr.map(v => {
						return v.carTypeFullName
					}).join()
				}
				return tetx
			},
			textWash(e) {
				return e ? e : '-'
			},
			falseBtn(name) {
				this[name] = false
			},
			formattes(type, value) {
				return formatter(type, value)
			}
		}
	}
</script>

<style scoped>
	.cellgroup {
		background-color: #fff;
		margin-top: 20rpx;
	}

	.cell_type {
		padding: 20rpx;
		font-size: 26rpx;
	}

	.cell_type_title {
		color: #6e6e6e;
		margin-right: 20rpx;
	}

	.mar_bot {
		margin-bottom: 10rpx;
	}

	.appr_title {
		font-size: 36rpx;
		font-weight: 600;

	}

	/deep/.u-cell-group__wrapper {
		background-color: #fff;
		/* #ifdef MP-WEIXIN */
		margin-top: 20rpx;
		/* #endif */
	}
</style>