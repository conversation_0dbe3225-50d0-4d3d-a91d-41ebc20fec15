<template>
	<view>
		<u-navbar title="变更行程" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="ctn">
			<block v-for="(item,idx) in tripList" :key="idx">
				<view class="ctn-item u-align-items" :key="item">
					<view class="item-icon" :class="{'visibility':idx==0}" @click="direction(idx,true)">
						<u-icon name="arrow-upward" color="#fff"></u-icon>
					</view>
					<view class="item-conten u-align-items">
						<view class="item-conten-drop" :class="{'blue':idx==0,'orgn':tripList.length==idx+1}"></view>
						<view style="flex: 1;" :class="{azure:!item.siteAddrName}" @click="siteOpen(idx)">
							{{item.siteAddrName?item.siteAddrName:'请添加行程'}}
						</view>
						<view class="" @click="delBtn(idx)">
							<u-icon name="minus-circle-fill"></u-icon>
						</view>
					</view>
					<view class="item-icon" :class="{'visibility':tripList.length==idx+1}"
						@click="direction(idx,false)">
						<u-icon name="arrow-downward" color="#fff"></u-icon>
					</view>
				</view>
			</block>
			<view class="u-align-items u-row-center">
				<u-button type="primary" color="#346CF2" text="添加行程" @click="addSubmit()"
					style="width: 25%;"></u-button>
			</view>

		</view>
		<view class="textarea">
			<view class="textTileb">
				变更说明：<span v-if="datar.isTravelFormationLocationRemark">(必填项)</span><span v-else>(非必填项)</span>
			</view>
			<view>
				<u--textarea v-model="remark" :confirmType="'done'" placeholder="请输入内容" :height='100'></u--textarea>
			</view>
		</view>
		<view class="btm-btn-style">
			<u-button type="primary" color="#346CF2" text="确定" @click="submit()"></u-button>
		</view>
		<getSeat @seatClose='seatClose' v-if="seatShow" :seatIndex='seatIndex'></getSeat>
	</view>
</template>

<script>
	import getSeat from '@/components/getSeat/getSeat.vue'
	import {
		mytravelinfo,
		mytraveUpdate,
	} from '@/config/api.js';
	export default {
		components: {
			getSeat
		},
		data() {
			return {
				tripList: [],
				optionObj: {},
				datar: {},
				seatShow: false,
				seatIndex: null,
				remark: null
			}
		},
		onLoad(option) {
			this.optionObj = option
			this.getDtail()
		},
		methods: {
			siteOpen(idx) {
				this.seatIndex = idx
				this.seatShow = true
			},
			seatClose(item) {
				if (item) {
					let locat = item.location.split(',')
					let obj = {
						siteAddrDetail: item.address,
						siteAddrName: item.name,
						siteAreaCode: item.pcode,
						siteLat: locat[1],
						siteLng: locat[0],
					}
					this.tripList.splice(item.idx, 1, obj)
					this.seatShow = false
				} else {
					this.seatShow = false
				}
			},
			delBtn(idx) {
				this.tripList.splice(idx, 1)
			},

			submit() {
				let listr = this.tripList.filter(v => {
					return !v.siteAddrDetail
				})
				if (listr.length != 0) return uni.$u.toast('请选择行程后在保存')
				let up = {
					throughAddrInfo: JSON.stringify(this.tripList),
					travelLocationRemark: this.remark ? this.remark : '',
					id: this.optionObj.id
				}

				let pages = getCurrentPages(); //获取跳转的所有页面
				let nowPage = pages[pages.length - 1]; //当前页
				let prevPage = pages[pages.length - 2]; //上一页

				mytraveUpdate(up).then(res => {
					uni.$u.toast(res)

					setTimeout(() => {
						try {
							if (prevPage.route == 'pages/map/mapnotit') {
								prevPage.getShow()
							}
						} catch (e) {

						}

						uni.navigateBack({
							delta: 1
						})
					}, 500)
				})
			},
			getDtail() {
				mytravelinfo({
					params: this.optionObj
				}).then(res => {
					this.datar = res
					if (res.throughAddrInfo && res.throughAddrInfo != '{}') {
						this.tripList = JSON.parse(res.throughAddrInfo)
					}
					let obj1 = {
						siteAddrDetail: res.fromAddrDetail,
						siteAddrName: res.fromAddrName,
						siteAreaCode: res.fromAreaCode,
						siteLat: res.fromLat,
						siteLng: res.fromLng,
					}
					let obj2 = {
						siteAddrDetail: res.toAddrDetail,
						siteAddrName: res.toAddrName,
						siteAreaCode: res.toAreaCode,
						siteLat: res.toLat,
						siteLng: res.toLng,
					}
					this.tripList.splice(0, 0, obj1)
					this.tripList.splice(this.tripList.length, 0, obj2)
				})
			},
			addSubmit() {
				if (this.tripList.length > 5) return uni.$u.toast('添加行程上限')
				let objr = {
					siteAddrDetail: null,
					siteAddrName: '',
					siteAreaCode: '',
					siteLat: '',
					siteLng: '',
				}
				this.tripList.splice(this.tripList.length - 1, 0, objr)
			},
			direction(idx, type) {
				let index = type ? idx - 1 : idx + 1
				let temp = this.tripList[idx]
				this.$set(this.tripList, idx, this.tripList[index])
				this.$set(this.tripList, index, temp)
				uni.$u.toast(`${type?'向上':'向下'}`)
			},
		}
	}
</script>

<style scoped>
	.ctn {
		background: #fff;
		padding: 20rpx;
		margin-top: 10rpx;
	}

	.ctn-item {
		margin-bottom: 20rpx;
	}

	.item-conten {
		background: #f7f7f7;
		padding: 20rpx;
		flex: 1;
		border-radius: 10rpx;
	}

	.item-icon {
		background-color: rgb(96, 98, 102);
		border-radius: 50%;
		padding: 8rpx;
		margin: 10rpx;
	}

	.item-conten-drop {
		padding: 6rpx;
		background: rgb(90, 199, 37);
		border-radius: 50%;
		margin-right: 10rpx;
	}

	.textarea {
		padding: 20rpx;
		background-color: #fff;
		margin-top: 20rpx;
	}

	.textTileb {
		margin: 20rpx 0;
	}

	.visibility {
		visibility: hidden;
	}

	.azure {
		color: #ccc;
	}

	.blue {
		background: #239efc;
	}

	.orgn {
		background: #ff7031;
	}
</style>