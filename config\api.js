const http = uni.$u.http

import qs from 'qs'
// 登录
export const loginFun = (params, config = {}) => http.post('sys/app/applogin', params, config)
// 微信授权
export const wechatLoginFun = (params, config = {}) => http.post('sys/app/wechatlogin', params, config)
// 退出登录
export const outLogin = (params, config = {}) => http.post('sys/app/loginout', params, config)
// 获取openid
// export const getcodeSession = (params, config = {}) => http.post('sys/app/getcodesession', params, config)

export const getcodeSession = (data) => http.get('sys/app/getcodesession', data)
// 修改密码
export const resetpassword = (params, config = {}) => http.put('sys/app/resetpassword', params, config)
// 获取用户信息
export const getuserinfo = (data) => http.get('sys/app/getuserinfo', data)
// 缓存登录角色
export const appcacherole = (params, config = {}) => http.post('sys/app/appcacherole', params, config)
// 微信登录存取
export const appgivecod = (params, config = {}) => http.post('sys/app/appgivecode', params, config)
// 获取工作台
export const getmenubyuser = (data) => http.get('sys/app/getmenubyuser', data)
// 高德搜索poi
export const getGdparameters = (params, config = {}) => http.post('sys/gdparameters', params, config)



// **************************************************************************////乘客端
// 首页
//获取轮播图数据
export const getLunBo = (data) => http.get('v1/home/<USER>', data)

//获取图片数字验证码
export const getCaptcha = (data) => http.get('sys/app/captcha', data)

//查询登录用户绑定用车制度列表
export const regulationList = (data) => http.get('merchant/app/regulationlistbyloginuser', data)

//查询登录用户的消息
// export const regulationList = (data) => http.get('merchant/app/regulationlistbyloginuser', data)

//获取公司所有部门（树形结构）
export const treeListorg = (data) => http.get('merchant/app/treeListorg', data)

//查询员工列表
export const employeelistbyorg = (data) => http.get('merchant/app/employeelistbyorg', data)

//根据场景查询用户绑定用车制度列表
export const regulationlistbyuser = (data) => http.get('merchant/app/regulationlistbyuser', data)

//根据用车制度查询成本中心
export const costcenterorgbyregulation = (data) => http.get('merchant/app/costcenterorgbyregulation', data)

//查询用户绑定用车制度详情
export const regulationinfo = (data) => http.get('merchant/app/regulationinfo/' + data.params.reguid, data)
//查询用户绑定用车制度详情
export const regulationinfor = (data) => http.get('merchant/app/regulationinfo/' + data.params.id, data)

//企业公车订单申请
export const applycomporder = (params, config = {}) => http.post('order/app/applycomporder', params, config)
// 私车公用申请
export const applyprivateorder = (params, config = {}) => http.post('order/app/applyprivateorder', params, config)

// 工作台
// 自有车我的申请列表
export const applycomporderlist = (data) => http.get('order/app/applycomporderlist', data)

// 自有车我的申请详情
export const applycomporderinfo = (data) => http.get('order/app/applycomporderinfo/' + data.params.id)

// 我的审批列表
export const checkcomporderlist = (data) => http.get('order/app/checkcomporderlist', data)

// 我的审批详情
export const checkcomporderinfo = (data) => http.get('order/app/checkcomporderinfo/' + data.params.id)

// 我的审批转租赁详情
export const checkleaseorderinfo = (data) => http.get('order/app/checkleaseorderinfo/' + data.params.id)

//审批操作
export const checkcomporder = (params, config = {}) => http.put('order/app/checkcomporder/' + params.id, params
	.checkCompOrderVo, config)

//转租赁审批
export const checktoleaseor = (params, config = {}) => http.put('order/app/checktoleaseorder/' + params.id, params
	.checkCompOrderVo, config)

// 私车公用审批
export const checkpriceteorder = (params, config = {}) => http.put('order/app/checkpriceteorder/' + params.id, params
	.checkCompOrderVo, config)

// 我的调度列表
export const dispcomporderlist = (data) => http.get('order/app/dispcomporderlist', data)

// 我的调度详情
export const dispcomporderinfo = (data) => http.get('order/app/dispcomporderinfo/' + data.params.id)

// 调度选择车辆
export const cardisplist = (data) => http.get('car/app/cardisplist', data)

// 调度选择司机
export const driverdisplist = (data) => http.get('merchant/app/driverdisplist', data)

// 调度选择车队
export const carteamlist = (data) => http.get('car/app/carteamlist', data)

// 调度选择租赁公司
export const suppliercoopelist = (data) => http.get('merchant/app/suppliercoopelist', data)

// 调度选择车型
export const cartypetreelist = (data) => http.get('car/app/cartypetreelist', data)
export const cartypetreebyfeetemplate = (data) => http.get('car/app/cartypetreebyfeetemplate', data)

// 调度选择包车套餐
export const tempvaluationlist = (data) => http.get('car/app/tempvaluationlist', data)

//派车
export const dispatchleasecar = (params, config = {}) => http.put('order/app/dispatchleasecar/' + params.id, params
	.dispatchCarVo, config)

// 改派
export const dispatchleasechangecar = (params, config = {}) => http.put('order/app/dispatchleasechangecar/' + params.id,
	params.dispatchCarVo, config)

//驳回
export const dispatchcompreject = (params, config = {}) => http.put('order/app/dispatchcompreject/' + params.id, params
	.dispatchRejectVo, config)

//转车队
export const dispatchleasechangefleet = (params, config = {}) => http.put('order/app/dispatchleasechangefleet/' + params
	.id, params.dispatchFleetVo, config)

//转租赁
export const dispatchtolease = (params, config = {}) => http.put('order/app/dispatchtolease/' + params.id, params
	.dispatchToLeaseVo, config)

//转租赁审批
export const checktoleaseorder = (params, config = {}) => http.put('order/app/checktoleaseorder/' + params.id, params
	.appCheckCompOrderVo, config)

//撤单
export const dispatchtoleaseback = (params, config = {}) => http.put('order/app/dispatchtoleaseback/' + params.id,
	params, config)

// 结单(司机)
export const tempvaluationinfo = (data) => http.get('car/app/tempvaluationinfo/' + data.params.id)
export const costcalculate = (params, config = {}) => http.post('order/app/costcalculate/' + params.id, params
	.costCalculateVo, config)
export const driveroverorder = (params, config = {}) => http.put('order/app/driveroverorder/' + params.id, params
	.dispatchOverVo, config)

// 结单(乘客)
export const dispatchcompchangeover = (params, config = {}) => http.put('order/app/dispatchcompchangeover/' + params.id,
	params.dispatchOverVo, config)

// 修改费用
export const changefee = (params, config = {}) => http.put('order/app/changefee/' + params.id, params.dispatchOverVo,
	config)

// 行程
// 我的行程订单列表
export const mytravellist = (data) => http.get('order/app/mytravellist', data)
// 取消订单
export const mytravelCancel = (params, config = {}) => http.put('order/app/cancelorder/' + params.id, params)
// 我的行程订单详情
export const mytravelinfo = (data) => http.get('order/app/mytravelinfo', data)
// 修改行程
export const mytraveUpdate = (params, config = {}) => http.post('order/app/updatetravelorder/' + params.id,
	params, config)
// 个人中心
// 我的订单列表
export const comporderlist = (data) => http.get('order/app/comporderlist', data)
// 私车公用列表
export const privateorderlist = (data) => http.get('order/app/privateorderlist', data)

// 私车公用修改地址
export const updateorderplace = (data) => http.put('/order/app/updateorderplace/' + data.id, data)

// 我的订单详情
export const comporderinfo = (data) => http.get('order/app/comporderinfo/' + data.params.id)

// 确认费用
export const comfirmorder = (data) => http.put('order/app/confirmorder/' + data.params.id)

// 费用
export const comCostdetails = (data) => http.put('order/app/costdetails/' + data.params.id)

// 待评价
export const comSaveAluated = (data, config = {}) => http.post('order/app/saveevaluated/' + data.id, data, config)




// **************************************************************************////司机端
// 我的订单列表
export const driverorderlist = (data) => http.get('order/app/driverorderlist', data)

// 我的订单详情
export const driverorderinfo = (data) => http.get('order/app/driverorderinfo/' + data.params.id)
export const driverorderinfocopy = (data) => {
	return http.get('order/app/driverorderinfo/' + data.id)
}

// 司机统计信息
export const driverreport = (data) => http.get('order/app/driverreport', data)

// 查询个人信息
export const driverinformation = (data) => http.get('merchant/app/driverinformation', data)

// 编辑个人信息（乘客、 司机）
export const employeeinformationupdate = (params, config = {}) => http.put('merchant/app/employeeinformationupdate',
	params, config)

// 通用上传文件接口
export const fileupload = (params, config = {}) => http.upload('sys/file/fileupload', params, config)

// 首页订单列表
export const driverorder = (data) => http.get('order/app/driverorder', data)

// 确认接单.取消接单
export const driveracceptorder = (params, config = {}) => http.put('order/app/driveracceptorder/' + params.id, params
	.driverAcceptOrderVo, config)

// 取消订单
export const cancelorder = (params, config = {}) => http.put('order/app/cancelorder/' + params.id, params, config)

// 开始执行
export const startExecution =
	(params, config = {}) => http.put('order/app/driverbeginorder/' + params.id, params,
		config)

// 到达出发地
export const driverarrivefromaddr =
	(params, config = {}) => http.put('order/app/driverarrivefromaddr/' + params.id, params,
		config)

// 到达目的地
export const driverarrivetoaddr =
	(params, config = {}) => http.put('order/app/driverarrivetoaddr/' + params.id, params,
		config)


// 回场
export const drivercomeback =
	(params, config = {}) => http.put('order/app/drivercomeback/' + params.id, params,
		config)