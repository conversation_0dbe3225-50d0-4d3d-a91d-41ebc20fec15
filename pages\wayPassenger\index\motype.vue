<template>
	<view style="padding-bottom:30rpx;width: 100vw; overflow:hidden">
		<swiper :indicator-dots="isShowDot && showDot" :current='current' v-if="listdivInfo.length != 0"
			:style="{ height: (80 * col * 2) + 'rpx' }" @change="swiperChange">
			<swiper-item v-for="(item, index) in listdivInfo" :key="index" :class="{ 'swiper-item': true, }">
				<view v-for="(child, code) in item" :class="{ 'smallItem': true }" :key="code"
					:style="{ width: `calc(${width}% - 40rpx)` }" @click="getOpen(child)">
					<view class="image">
						<u-image :src="imgr" width="64rpx" height="64rpx" radius="32rpx" v-if="child.bindType == 1">
						</u-image>
						<u-image :src="imgb" width="64rpx" height="64rpx" radius="32rpx" v-if="child.bindType == 2">
						</u-image>
					</view>
					<view class="name">{{ child.regulationSname }}</view>
				</view>
			</swiper-item>
		</swiper>
		<u-empty text="暂无功能权限" icon="http://cdn.uviewui.com/uview/empty/order.png" style='margin-top: 60rpx;'
			v-if="listdivInfo.length == 0">
		</u-empty>
	</view>
</template>

<script>
	export default {
		props: {
			list: {
				type: Array,
				default: () => {
					return [];
				},
			},
			//一行排列数
			nums: {
				type: Number,
				default: 4,
			},
			//排列行数
			col: {
				type: Number,
				default: 1,
			},
			tabsidx: {
				type: Number,
				default: 1,
			},

			//是否展示指示灯
			isShowDot: {
				type: Boolean,
				default: true,
			},
			//
			isNumber: {
				type: Number,
			},
			isImage: {
				type: String,
				default: 'https://zqcx.di-digo.com/app/image/index_icon1.png',
			}
		},
		watch: {
			list: {
				handler: function(newVal, oldVal) {
					this.listdiv();
				},
				deep: true,
			},
			tabsidx: {
				handler: function(newVal, oldVal) {
					this.listdiv();
				},
				deep: true,
			},
		},
		mounted() {
			this.listdiv();
		},
		data() {
			return {
				listdivInfo: [],
				width: 25,
				showDot: true,
				imgr: 'https://zqcx.di-digo.com/app/image/geren.png',
				imgb: 'https://zqcx.di-digo.com/app/image/bumen.png',
				current: 0
			};
		},
		methods: {
			swiperChange(e) {
				this.current = e.detail.current
			},
			getOpen(item) {
				this.$emit('setItem', item)
			},
			async listdiv() {
				this.current = 0
				this.width = 100 / this.nums;
				var arr = [];
				let that = this;
				await this.list.forEach((v, index) => {
					var num = Math.floor(index / (that.nums * that.col));
					if (!arr[num]) {
						arr[num] = [];
						arr[num].push(v);
					} else {
						arr[num].push(v);
					}
				});
				// console.log(arr,'arr');
				this.listdivInfo = arr;
				if (this.listdivInfo.length > 1) {
					this.showDot = true;
				} else {
					this.showDot = false;
				}
			},
		},
	};
</script>

<style lang="scss" scoped>
	.swiper {
		// background: #fff;
	}

	.swiper-item {
		display: flex;
		flex-wrap: wrap;
		left: 20rpx;
		right: 20rpx;
		// width: calc(100% - -10rpx) !important;

		.smallItem {
			display: flex;
			align-items: center;
			background: #e3f1ff;
			// height: calc(48% - 20rpx);
			height: 140rpx;
			margin: 10rpx;
			border-radius: 20rpx;

			.image {
				width: 64rpx;
				height: 64rpx;
				margin: 0 5%;
			}

			.name {
				margin-top: 8rpx;
				font-size: 24rpx;
				font-weight: bold;
			}
		}
	}

	.magin {
		margin-bottom: 60rpx !important;
	}

	.u-flex-cengten {
		justify-content: center !important;
	}

	/deep/ .uni-swiper-wrapper {
		overflow: unset;
	}

	/deep/ .uni-swiper-dots-horizontal {
		background-color: #D1DEFF;
		border-radius: 15rpx;
		bottom: -20rpx;
	}

	/deep/ .uni-swiper-dot {
		transition: all .5s;
		background: #ffffff00;
		// height: 10rpx !important;
	}

	/deep/ .uni-swiper-dot-active {
		transition: all .5s;
		width: 40rpx;
		background: #346CF2 !important;
		border-radius: 15rpx !important;
	}
</style>