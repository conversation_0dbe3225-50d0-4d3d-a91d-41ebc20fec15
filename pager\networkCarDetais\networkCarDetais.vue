<template>
	<view>
		<u-navbar :title="titles" :autoBack="true" :placeholder="true"></u-navbar>

		<u-cell-group class="cellgroup " :border='false'>
			<u-cell :value="dataWash(datas.compOrderStateName)">
				<view slot='title' class="u-flex">
					<span
						style="font-weight: bold;font-size: 32rpx; margin-right: 20rpx;">{{dataWash(datas.ordName)}}</span>
					<u-icon name="phone-fill" color="#2979ff" size="24" @click='phoneClick'></u-icon>
				</view>

				<view slot='label'>
					<span>{{dataWash(datas.costCenterName)}}</span>
				</view>
			</u-cell>
		</u-cell-group>

		<u-cell-group class="cellgroup " :border='false'>
			<u-cell>
				<view slot='title'>
					<span>申请单号: </span>
					<span class="title_text"> {{dataWash(datas.netApplyCode)}}</span>
					<span class="text-copy" @click.stop="copyClick(datas)" v-show="datas.netApplyCode">复制</span>
				</view>
			</u-cell>
			<u-cell>
				<view slot='title' @click="networkShow=true">
					<span>用车制度: </span>
					<span class="title_text"> {{dataWash(datas.regulationName)}}</span>
					<span class="regulation" v-show="datas.netApplyType">{{datas.netApplyType==1?'日常':'差旅'}} </span>
					<span class="title_span">(点击查看详情)</span>
				</view>
			</u-cell>
			<u-cell>
				<view slot='title'>
					<span>申请时间: </span>
					<span class="title_text">
						{{new Date(datas.applyTime).getTime() | date('yyyy年mm月dd日 hh:MM:ss')}}
					</span>
				</view>
			</u-cell>
		</u-cell-group>
		<u-cell-group class="cellgroup " :border='false'>
			<u-cell>
				<view slot='title'>
					<span>用车日期: </span>
					<span class="title_text">
						{{new Date(datas.startDate).getTime() | date('yyyy年mm月dd日')}}
					</span>
				</view>
			</u-cell>
			<u-cell>
				<view slot='title'>
					<span>用车时段: </span>
					<span class="title_text"> {{dataWash(datas.dateName)}}</span>
				</view>
			</u-cell>
		</u-cell-group>

		<u-cell-group class="cellgroup " :border='false'>
			<u-cell title="申请原因: ">
				<span slot='label'>{{dataWash(datas.description)}}</span>
			</u-cell>
		</u-cell-group>

		<u-cell-group class="cellgroup " :border='false'>
			<u-cell title="订单跟踪: ">
				<view slot="label">
					<u-steps current="1" dot direction="column">
						<u-steps-item :title="`${item.evtDetContent} - ${item.operatorTimes}`"
							v-for="item in datas.orderEventHis" :key="item.operatorTimes">
							<view slot='desc' style="margin-bottom: 20rpx;">
								{{`${item.operatorName}${item.operatorMobile}`}}
							</view>
						</u-steps-item>
					</u-steps>
				</view>
			</u-cell>
		</u-cell-group>

		<view class="footer_box btn_box u-flex u-row-around" v-if='option.type==1 &&datas.compOrderState==10'>
			<view class="" style="width: 30%;" v-show="datas.approvalButton.isNetCarButton==1">
				<u-button type="primary" color="#346CF2" text="同意" @tap="agreeWith(1)"></u-button>
			</view>
			<view class="" style="width: 30%;" v-show="datas.approvalButton.isNetCarRejectedButton==1">
				<u-button type="primary" color="#cccccc" text="驳回" @click="rejected()"></u-button>
			</view>
		</view>

		<!-- 驳回弹窗组件 -->
		<u-popup class="popup_bg" :round="5" :show="rejectshow" mode="center" @close="rejectclose"
			:customStyle="styleObjr">
			<rejectCom v-if="rejectshow" :rejectObj="datas" rejectName="驳回" @adoptFun="adoptFunrs">
			</rejectCom>
		</u-popup>

		<!-- 审批人下拉框 -->
		<u-picker :show="adoptShow" :columns="nextList" keyName="name" @cancel="adoptShow=false" @confirm="approvalBtn"
			@close="adoptShow=false"></u-picker>

		<u-popup :show="networkShow" mode="center" :round="3" @close="()=>{networkShow=false}" :customStyle="styleObjr">
			<vehicleReminder :data='datas.appApplyRegulationVo' @catShow='carClick' :shows='true'></vehicleReminder>
		</u-popup>
	</view>
</template>

<script>
	import {
		applyNetCarDetai,
		checkNetorder
	} from '@/config/consoler.js';
	import {
		checkcomporder,
	} from '@/config/api.js';
	import rejectCom from '@/pages/wayPassenger/workBench/componentr/rejectCom.vue'
	import vehicleReminder from '@/pages/wayPassenger/workBench/componentr/vehicleReminder.vue'
	export default {
		components: {
			rejectCom,
			vehicleReminder
		},
		data() {
			return {
				titles: '',
				option: {},
				titleList: ['', '网约车申请详情', '网约车审批详情'],
				datas: {
					orderEventHis: [],
					approvalButton: {}
				},
				modalShow: false,
				modalTile: '',
				nextList: [],
				adoptShow: false,
				styleObjr: {
					width: '85%'
				},
				rejectshow: false,
				networkShow:false,
			}
		},
		onLoad(op) {
			this.option = op
			this.titles = this.titleList[op.type]

			this.getDetails()
			// op.type== 1 ? '网约车申请详情' : op.type == 2 ? '网约车审批详情' : ''
		},
		onReady() {

		},
		methods: {
			carClick(val) {
				if (!val) return this.networkShow = false
			},
			rejected() {
				this.datas.useCarType = 2
				this.rejectshow = true
			},
			rejectclose() {
				this.rejectshow = false
				this.selectshow = false
			},
			adoptFunrs(objr) {
				if (!objr) return this.rejectshow = false
				let objs = {
					isAgree: objr.isAgree,
					checkRemark: objr.checkRemark,
					nextCheckUserId: null,
					rejectType: objr.rejectType
				}
				this.adoptFun(objs)
			},
			approvalBtn(e) {
				let objr = {
					isAgree: 1,
					checkRemark: null,
					nextCheckUserId: e.value[0].userId
				}
				this.adoptFun(objr)
			},
			adoptFun(objr) {
				let that = this
				objr.isNetCar = 1
				checkcomporder({
					checkCompOrderVo: objr,
					id: this.option.id,
				}).then((data) => {
					that.getDetails()
					setTimeout(() => {
						that.adoptShow = false
						this.rejectshow = false
						uni.$u.toast('操作成功')
					}, 1000)
				})
			},
			phoneClick() {
				if (!this.datas.mobile) return uni.$u.toast('暂无联系方式')
				uni.makePhoneCall({
					phoneNumber: this.datas.mobile //仅为示例
				});
			},
			confirm() {

			},
			cancel() {
				this.modalTile = ''
				this.modalShow = false
			},
			agreeWith(t) {
				let that = this
				uni.showModal({
					// title: '通过申请',
					content: '是否确定通过用车申请',
					success: function(res) {
						if (res.confirm) {
							if (that.datas.nextCheckUserList && that.datas.nextCheckUserList.length > 0) {
								that.nextList = [that.datas.nextCheckUserList]
								that.adoptShow = true
							} else {
								let objr = {
									isAgree: 1,
									checkRemark: null,
									nextCheckUserId: null
								}
								that.adoptFun(objr)
							}
						}
					}
				})
			},
			getDetails() {
				if (this.option.type == 1) {
					applyNetCarDetai({
						id: this.option.id
					}).then(res => {
						this.datas = res
					})
				} else {
					checkNetorder({
						id: this.option.id
					}).then(res => {
						console.log(res, 'res');
						this.datas = res
					})
				}
			},
			copyClick(item) {
				uni.setClipboardData({
					data: item.netApplyCode ? item.netApplyCode : '',
					success: function() {
						uni.$u.toast('复制成功')
					}
				});
			},
			timeWash(v) {

			},
			dataWash(v) {
				return v ? v : '-'
			}
		}
	}
</script>

<style scoped>
	.cellgroup {
		background-color: #fff;
		margin-top: 20rpx;
	}

	.text-copy {
		margin: 0 20rpx;
		border-radius: 10rpx;
		color: #3894ff;
		padding: 0 10rpx;
		border: 1px solid #3894ff;
		background-color: #e9f3ff;
		font-size: 24rpx;
	}

	.regulation {
		background-color: #7728F5;
		padding: 4rpx 16rpx;
		color: #fff;
		border-radius: 20rpx;
		margin-right: 20rpx;
		font-size: 24rpx;
	}

	.title_text {
		margin: 0 20rpx;
	}
	.title_span{
		font-size: 24rpx;
		color: #ccc;
	}

	/deep/.u-cell-group__wrapper {
		background-color: #fff;
		/* #ifdef MP-WEIXIN */
		margin-top: 20rpx;
		/* #endif */
	}
</style>