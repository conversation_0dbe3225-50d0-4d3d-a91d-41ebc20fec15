<template>
	<view class="workBench">
		<u-navbar title="工作台" :placeholder="true">
			<view slot="left"></view>
		</u-navbar>

		<view class="workBox" v-for="(item,index) in workBoxListr" :key="item.resName">
			<block v-if="item.children">
				<h5 class="workBox_title">{{item.resName}}</h5>
				<view class="workBox_conten">
					<view class="workBox_item" v-for="(i,idx) in item.children" :key="i.resName"
						@click="goPage(item,i)">
						<u-icon size="35" :name="i.menuUrl">
						</u-icon>
						<span class="workBox_text">{{i.resName}}</span>
					</view>
				</view>
			</block>
		</view>

		<u-empty text="暂无功能权限" icon="http://cdn.uviewui.com/uview/empty/order.png" style='margin-top: 60rpx;'
			v-if="workBoxListr.length==0">
		</u-empty>

		<!-- 车务上报弹窗 -->
		<u-popup :show="openShow" mode="bottom" :round="5" @close="openClick(false)" safeAreaInsetBottom>
			<view class="carTitle u-align-items u-row-between">
				<view>请选择要上报的车务</view>
				<i class="iconfont icon-close" @tap="openClick(false)"></i>
			</view>
			<view class="carIcon">
				<view class="carBoxs u-center" hover-class="carBoxsHover" v-for="(item,index) in carType" :key="index"
					@click="goCarUrl(item)">
					<view class="iconr u-center">
						<u-image class="down_icon" :lazyLoad="true" :width="30" :height="30" :src="item.iconr">
						</u-image>
					</view>
					<view class="carText">
						{{item.name}}
					</view>
				</view>
			</view>
		</u-popup>
		<!-- 底部 -->
		<!-- <tabBart :current='2'></tabBart> -->
	</view>
</template>

<script>
	import {
		getmenubyuser,
	} from '@/config/api.js';
	import {
		messagecount
	} from '@/config/consoler.js';
	import tabBart from '@/components/tabBart/tabBart.vue'
	export default {
		component: {
			// tabBart
		},
		data() {
			return {
				openShow: false,
				workBoxListr: [],
				carType: [{
						id: '0',
						iconr: 'https://zqcx.di-digo.com/app/image/jiayou.png',
						name: '加油/充电',
						color: '#199DFF',
						type: 'oilSaveVo'
					},
					{
						id: '2',
						iconr: 'https://zqcx.di-digo.com/app/image/weixiu.png',
						name: '维修',
						color: '#FF19D9',
						type: 'maintainSaveVo'
					},
					{
						id: '3',
						iconr: 'https://zqcx.di-digo.com/app/image/weibao.png',
						name: '保养',
						color: '#D29D36',
						type: 'maintainSaveVo'
					},
					{
						id: '4',
						iconr: 'https://zqcx.di-digo.com/app/image/baoxian.png',
						name: '保险',
						color: '#22C310',
						type: 'insuranceSaveVo'
					},
					{
						id: '5',
						iconr: 'https://zqcx.di-digo.com/app/image/nianjian.png',
						name: '年检',
						color: '#A9C6CE',
						type: 'inspectionSaveVo'
					},
					{
						id: '6',
						iconr: 'https://zqcx.di-digo.com/app/image/weizhangr.png',
						name: '违章',
						color: '#D81E06',
						type: 'violationSaveVo'
					},
					{
						id: '7',
						iconr: 'https://zqcx.di-digo.com/app/image/qitar.png',
						name: '其他',
						color: '#A411D1',
						type: 'otherTrafficSaveVo'
					},
					{
						id: '8',
						iconr: 'https://zqcx.di-digo.com/app/image/qitar.png',
						name: '其1他',
						color: '#A411D1',
						type: 'otherTrafficSaveVo'
					}
				],
			}
		},
		onLoad() {
			this.getMsg()
		},
		onShow() {
			this.getUserList()
		},
		methods: {
			getMsg() {
				messagecount({}).then(res => {
					let gentle = res.countPush + res.countSystem
					let sum = gentle > 99 ? '99+' : `${gentle}`
					uni.setTabBarBadge({
						index: 3,
						text: sum ? sum : '0'
					})
				})
			},
			goPage(item, i) {
				if (i.resUrl == 'true') {
					if (!i.children) return uni.showToast({
						title: '暂无功能权限！'
					})
					let iCd = i.children.map(v => {
						return v.resPath
					})
					let arr = this.carType.filter(v => {
						return iCd.includes(v.id)
					})
					this.carType = arr
					this.openClick(true)
				} else {
					uni.navigateTo({
						url: i.resUrl
					})
				}
			},
			goCarUrl(item) {
				uni.$u.route({
					url: '/pager/vehicleService/vehicleService',
					params: {
						id: item.id,
						name: item.name,
						type: item.type
					},
				})
				this.openClick(false)
			},
			// 打开弹窗
			openClick(type) {
				this.openShow = type
			},
			goUrlr(url, type) {
				uni.navigateTo({
					url: `${url}${type?'?typer='+type:''}`,
				})
			},
			getUserList() {
				getmenubyuser().then((res) => {
					let att = res.filter(v => {
						return v.resUrl == "workBench"
					})
					let arrs = att[0].children ? att[0].children : []
					const arraysEqual = (a, b) => {
						return a.toString() == b.toString()
					}
					if (this.$store.state.consoleMenu) {
						if (arraysEqual(this.$store.state.consoleMenu, arrs)) {
							// console.log(this.workBoxListr,1);
							if (this.workBoxListr.length == 0) {
								this.workBoxListr = this.$store.state.consoleMenu
							}
						} else {
							// console.log(2);
							this.$store.commit('setMenu', arrs ? arrs : [])
							this.workBoxListr = arrs
						}
					} else {
						// console.log(3);
						this.$store.commit('setMenu', arrs ? arrs : [])
						this.workBoxListr = arrs
					}
				})
			}

		}
	}
</script>

<style lang="scss" scoped>
	.workBench {
		margin-bottom: 20rpx;

		.workBox {
			// margin-top: 20rpx;
			margin: 0 40rpx;

			.workBox_title {
				height: 33rpx;
				font-size: 35rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #111111;
				line-height: 33rpx;
				margin: 20rpx 0;
			}


			.workBox_conten {
				width: 100%;
				// height: 192rpx;
				background: #FFFFFF;
				border-radius: 13rpx;
				display: flex;
				// justify-content: space-around;
				align-items: center;
				flex-flow: wrap;
				padding: 20rpx 0;

				.workBox_item {
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 25%;
					margin: 20rpx 0;

					.workBox_text {
						height: 50rpx;
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #404040;
						line-height: 80rpx;
					}
				}

			}
		}
	}

	.carTitle {
		padding: 30rpx;
		font-weight: bold;
		border-bottom: 5rpx solid #f5f5f5;
		color: #3e3e3e;
	}

	.carIcon {
		display: flex;
		flex-wrap: wrap;
		text-align: center;

		.carBoxs {
			flex-direction: column;
			width: 25%;
			height: 180rpx;
		}

		.carBoxsHover {
			background-color: #ececec86;
			border-radius: 10rpx;
		}

		.iconr {
			i {
				font-size: 55rpx;
			}

			margin-bottom: 10rpx;
		}
	}
</style>