<template>
	<view class="contenr">
		<u--form :model="form" :rules="rulesr" ref="uForm" class="is-bottom" :label-width="100" labelAlign="right"
			v-show="!overShow">
			<view class="formBg">
				<u-form-item :required="true" label="选择车辆 :" prop="carNumber" borderBottom @tap="pickerBtn(1)">
					<u--input v-model="form.carNumber" disabled disabledColor="#ffffff" placeholder="请选择车辆"
						border="none"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item label="品牌型号 :" prop="brandModelName" borderBottom>
					<u-input v-model="form.brandModelName" border="none" placeholder="根据选择车辆自动填充品牌型号" disabled
						disabledColor="#ffffff" />
				</u-form-item>
			</view>
			<block v-if="typer==6">
				<view class="formBg">
					<u-form-item :required="true" label="违章时间 :" prop="violationDate" borderBottom @tap="tiemOpen()">
						<u--input v-model="form.violationDate" disabled disabledColor="#ffffff" placeholder="请选择违章日期"
							border="none">
						</u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				<!-- 	<u-form-item :required="true" label="违章时间 :" prop="violationTime" borderBottom
						@tap='remindShow=true'>
						<u--input v-model="form.violationTime" disabled disabledColor="#ffffff" placeholder="请选择违章时间"
							border="none">
						</u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item> -->
					<u-form-item :required="true" label="违章地点 :" prop="violationAddress" borderBottom @tap='leftClick'>
						<u-input v-model="form.violationAddress" border="none" placeholder="请选择违章地点" />
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>
				<view class="formBg">
					<u-form-item label="违章罚款 :" prop="violationCost" borderBottom>
						<u-input v-model="form.violationCost" type='number' border="none" placeholder="请输入违章罚款" />
						<text slot="right">元</text>
					</u-form-item>
					<u-form-item label="违章扣分 :" prop="violationPoints" borderBottom>
						<u-input v-model="form.violationPoints" type='number' border="none" placeholder="请输入违章扣分" />
						<text slot="right">分</text>
					</u-form-item>
				</view>
				<view class="formBg">
					<u-form-item label="违章状态 :" prop="violaName" borderBottom @tap='allBtn(1)'>
						<u--input v-model="form.violaName" disabled disabledColor="#ffffff" placeholder="请选择违章状态"
							border="none">
						</u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>
			</block>
			<block v-if="typer==7">
				<view class="formBg">
					<u-form-item :required="true" label="项目类型 :" prop="otherName" borderBottom @tap='allBtn(2)'>
						<u--input v-model="form.otherName" disabled disabledColor="#ffffff" placeholder="请选择项目类型"
							border="none">
							<!--  -->
						</u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
					<u-form-item :required="true" label="办理时间 :" prop="otherTrafficTime" borderBottom
						@click="datetimeShow=true">
						<u--input v-model="form.otherTrafficTime" disabled disabledColor="#ffffff" placeholder="请选择办理时间"
							border="none">
						</u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>
				<view class="formBg">
					<u-form-item :required="true" label="项目费用 :" prop="otherTrafficCost" borderBottom>
						<u-input v-model="form.otherTrafficCost" type='number' border="none" placeholder="请输入项目费用" />
						<text slot="right">元</text>
					</u-form-item>
				</view>
				<view class="formBg" @click="peopleShow=true">
					<u-form-item label="经办人 :" prop="userName" borderBottom>
						<u-input v-model="form.userName" border="none" disabled placeholder=""
							disabledColor="#ffffff" />
					</u-form-item>
				</view>
			</block>
			<view class="formBg">
				<u-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic" multiple :maxCount="8"
					class="pding"></u-upload>
			</view>

			<view class="formBg pding wx_pd">
				<text>备注说明 :</text>
				<u--textarea v-model="form.remark" placeholder="请输入备注说明" confirmType="done"></u--textarea>
			</view>

		</u--form>

		<view class="formBg pding btn u-align-items u-row-around" v-show="!overShow">
			<view class="" style="width: 70%;">
				<u-button type="primary" color="#346CF2" text="提交确认" @tap="submit()"></u-button>
			</view>
			<view class="" style="width: 25%;">
				<u-button type="primary" color="#346CF2" text="上报列表" @click="goRouter()"></u-button>
			</view>
		</view>

		<!-- 日期选择 -->
		<u-datetime-picker :show="timeShow" v-model="timeMode" mode="datetime" :formatter="formatter"  @cancel="tiemBtn"
			:closeOnClickOverlay='true' @close="tiemBtn" @confirm='confirms'>
		</u-datetime-picker>

		<!-- 时间选择 -->
		<u-datetime-picker :show="remindShow" v-model="remindTiem" mode="time" @cancel="remindShow=false"
			@close="remindShow=false" @confirm='remindConf'></u-datetime-picker>

		<!-- 日期时间选择 -->
		<u-datetime-picker :show="datetimeShow" v-model="timeMode" :formatter="formatter" mode="datetime"
			@cancel="datetimeShow=false" @close="datetimeShow=false" @confirm='datetimeConf'></u-datetime-picker>

		<!-- 车牌选择器 -->
		<u-picker :show="pickerShow" :columns="carList" keyName="carNumber" :closeOnClickOverlay='true'
			@confirm="carConfirm" @cancel="pickerBtn" @close="pickerBtn"></u-picker>


		<!-- 地图获取 -->
		<view v-if="overShow">
			<u-navbar title="选择违章地址" @leftClick="leftClick">
			</u-navbar>
			<web-view src="/hybrid/html/comeon.html" style="top: 88rpx;"></web-view>
		</view>

		<!-- 违章、项目类型选择器 -->
		<u-picker :show="violaShow" :columns="allList" keyName="dicDescribe" :closeOnClickOverlay='true'
			@confirm="violafirm" @cancel="violaShow=false" @close="violaShow=false"></u-picker>

		<!-- 选择经办人 -->
		<popupr :show="peopleShow" title="选择经办人" @close="peopleShow=false">
			<slot>
				<people @peopleBtn='peopleVal'></people>
			</slot>
		</popupr>
	</view>
</template>

<script>
	import {
		uploadImg
	} from '@/config/consoler.js'
	import popupr from '../popupr/popupr.vue'
	import people from '../people/people.vue'
	export default {
		props: ['carList', 'typer'],
		components: {
			popupr,
			people
		},
		data() {
			let tiemrs = () => {
				let myDate = new Date()
				let er = (v) => {
					return v < 10 ? `0${v}` : v
				}
				return `${myDate.getHours()}:${er(myDate.getMinutes())}`
			}
			return {
				form: {
					valShow: true
				},
				rulesr: {
					'carNumber': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					'violationDate': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],
					
					'violationAddress': [{
						required: true,
						message: '必填项',
						trigger: ['blur', 'change']
					}],

				},
				fileList: [],
				timeShow: false,
				timeMode: Number(new Date()),
				datetimeShow: false,
				datetimeMode: Number(new Date()),
				pickerShow: false,
				tiemType: null,
				remindShow: false,
				remindTiem: tiemrs(),
				overShow: false,
				violaShow: false,
				violaList: [],
				otherList: [],
				allList: [],
				allType: null,
				peopleShow: false,
			}
		},
		created() {
			window.addEventListener('message', this.handleMessage);
		},
		beforeDestroy() {
			window.removeEventListener('message', this.handleMessage);
		},
		mounted() {
			let user = this.$common.getItem('userInfo')
			let arrs = this.$common.getItem('dicVoList')
			if (this.typer == 7) {
				this.$set(this.form, 'userName', user.name)
				this.$set(this.form, 'operatorUserId', user.userId)
			}
			arrs.forEach(item => {
				if (item.dicCode == "violation_state") {
					this.violaList = [item.dicValueList]
				}
				if (item.dicCode == "other_traffic_type") {
					this.otherList = [item.dicValueList]
				}
			})
		},
		methods: {
			peopleVal(user) {
				if (user.length > 1) return uni.$u.toast('请选择一个经办人')
				this.$set(this.form, 'userName', user[0].name)
				this.$set(this.form, 'operatorUserId', user[0].userId)
				this.peopleShow = false

			},
			// 选择违章状态
			violafirm(e) {
				let o = e.value[0]
				if (this.allType == 1) {
					this.$set(this.form, 'violationState', o.dicValue)
					this.$set(this.form, 'violaName', o.dicDescribe)
				}
				if (this.allType == 2) {
					this.$set(this.form, 'otherTrafficType', o.dicValue)
					this.$set(this.form, 'otherName', o.dicDescribe)
				}
				this.violaShow = false
			},
			// 接收webview 参数
			handleMessage(evt) {
				let data = evt.data.data
				if (data && data.arg) {
					this.form.violationAddress = data.arg.oilAddress
					this.form.violationLat = data.arg.oilLat
					this.form.violationLng = data.arg.oilLng
					this.leftClick()
				}
			},
			// 选择时间
			remindConf(e) {
				this.$set(this.form, 'violationTime', e.value + ':00')
				this.remindShow = false
			},
			datetimeConf(e) {
				let valr = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
				this.$set(this.form, 'otherTrafficTime', valr)
				this.datetimeShow = false
			},
			// 车牌选择
			carConfirm(e) {
				let o = e.value[0]
				this.$set(this.form, 'carId', o.carId)
				this.$set(this.form, 'brandModelName', o.brandModelName)
				this.$set(this.form, 'carNumber', o.carNumber)
				this.pickerBtn()
			},
			// 选中日期
			confirms(e) {
				let valr = uni.$u.timeFormat(e.value, 'yyyy-mm-dd  hh:MM:ss')
				this.$set(this.form, 'violationDate', valr)
				this.tiemBtn()
			},
			// 开关
			allBtn(t) {
				if (t == 1) {
					this.allList = this.violaList
				}
				if (t == 2) {
					this.allList = this.otherList
				}
				this.allType = t
				this.violaShow = true
			},
			// 日期开关
			tiemOpen(t) {
				this.tiemType = t
				this.tiemBtn()
			},
			goRouter() {
				uni.$u.route({
					url: '/pager/vehicleEscala/vehicleEscala',
					params: {
						id: this.typer,
					},
				})
			},
			// 日期开关
			tiemBtn() {
				this.timeShow = !this.timeShow
			},
			// 车牌开关
			pickerBtn() {
				this.pickerShow = !this.pickerShow
			},
			// 地图开关
			leftClick() {
				this.overShow = !this.overShow
			},
			// 日期格式化
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				if (type === 'hour') {
					return `${value}时`
				}
				if (type === 'minute') {
					return `${value}分`
				}
				return value
			},
			// 保存
			submit() {
				this.$refs.uForm.validate().then(res => {
					this.form.imgUrls = this.fileList.map(res => {
						return res.urlr
					}).join()
					uni.$u.toast('提交成功')
					setTimeout(() => {
						this.$emit('preserva', this.form)
					}, 500)
				}).catch(errors => {
					uni.$u.toast('校验失败')
				})
			},
			// 上传附件
			afterRead(event) {
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList`].length
				lists.map((item) => {
					this[`fileList`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				lists.forEach(r => {
					uploadImg(r, 4).then(res => {
						if (res) {
							let item = this[`fileList`][fileListLen]
							this[`fileList`].splice(fileListLen, 1, Object.assign(item, {
								status: 'success',
								message: '',
								urlr: res
							}))
							fileListLen++
						} else {
							this[`fileList`].splice(fileListLen, 1)
							uni.$u.toast('上传失败')
						}
					})
				})
			},
			// 删除附件
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},

		}
	}
</script>

<style lang="scss" scoped>
	.is-bottom {
		padding-bottom: 120rpx;
	}

	.formBg {
		margin: 20rpx 0;
		background-color: #fff;

		/deep/.u-form-item__body__left__content__label {
			display: flex;
			justify-content: flex-end !important;
			padding-right: 10rpx;
		}

		/deep/.item__body__right__content__icon {
			padding-right: 30rpx;
		}

		/deep/.u-form-item__body {
			padding: 30rpx 0;
		}
	}

	.btn {
		position: fixed;
		bottom: 0;
		width: calc(100% - 60rpx);
		margin: 0 !important;
		border-top: 4rpx solid #f3f3f3;
		z-index: 99;
	}

	.pding {
		padding: 20rpx 30rpx;

	}

	.wx_pd {
		/* #ifdef MP-WEIXIN */
		padding-bottom: 120rpx;
		/* #endif */
	}

	.map_detail_scoll {
		height: 0;
		transition: all .3s linear 0s;
		overflow-y: auto;
	}

	.map_detail_scoll.active {
		height: 324rpx;
		transition: all .3s linear 0s;
	}

	/deep/.u-form-item__body__left__content__label {
		display: flex;
		justify-content: end !important;
		padding-right: 10rpx;
	}

	/deep/.u-collapse-item__content__text {
		padding: 0;
	}

	.is-popup {
		/deep/.item__body__right__content__icon {
			padding-left: 20rpx;
		}

		/deep/.u-form-item__body__left__content__required {
			left: 0;
		}
	}

	.is-bottom {
		/deep/.u-form-item__body {
			padding: 30rpx 0;
		}

		/deep/.item__body__right__content__icon {
			padding-right: 30rpx;
		}

		/deep/.u-form-item__body__left__content__required {
			left: 10rpx;
		}
	}
</style>
