<template>
	<view class="hover_ball_cell"
		:style="{ width: `${diameter}px`, height: `${diameter}px`, top: `${top}px`, left: `${left}px` }"
		@touchmove="touchmove" @touchend="touchend" @touchcancel="touchcancel" @tap="onTap" :animation="ballAnimation">
		<view class="wave">
			icon
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				diameter: 0,
				top: 0,
				left: 0,
				isMove: false,
				ballAnimation: {},
				timeout: null,
				modile: {},
			};
		},
		name: 'shopSuspension',
		created() {
			let _this = this;
			_this.modile = uni.getSystemInfoSync();
			_this.top = _this.modile.safeArea.top + 500;
			_this.left = _this.modile.safeArea.left + (_this.modile.screenHeight / 20);
			_this.diameter = _this.modile.screenHeight / 15;
		},
		methods: {
			onTap() {
				let _this = this;
				let x = '0';
				if (2 * _this.left >= _this.modile.safeArea.width) {
					x = '-100%';
				}
				let create = uni.createAnimation({
					duration: 0
				});
				create.translate(x).step();
				_this.ballAnimation = create.export();
			},
			touchmove(e) {
				let _this = this;
				_this.isMove = true;
				if (_this.timeout != null) {
					clearTimeout(_this.timeout);
					_this.timeout = null;
				}
				var touch = e.touches[0] || e.changedTouches[0];
				_this.left = touch.clientX;
				_this.top = touch.clientY;
			},
			touchend(e) {
				let _this = this;
				if (!_this.isMove) {
					return;
				}
				_this.finish(e);
			},
			touchcancel(e) {
				let _this = this;
				if (!_this.isMove) {
					return;
				}
				_this.finish(e);
			},
			finish(e) {
				let _this = this;
				_this.isMove = false;
				var touch = e.touches[0] || e.changedTouches[0];
				_this.left = touch.clientX;
				_this.top = touch.clientY;
				let x = '0';
				if (2 * _this.left + _this.diameter >= _this.modile.safeArea.width) {
					_this.left = _this.modile.safeArea.width;
					x = '-100%';
				} else {
					_this.left = _this.modile.safeArea.left;
				}
				if (_this.top > _this.modile.safeArea.height + _this.modile.safeAreaInsets.bottom) {
					_this.top = _this.modile.safeArea.height + _this.modile.safeAreaInsets.bottom;
				} else if (_this.top < -_this.diameter / 2) {
					_this.top = -_this.diameter / 2;
				}
				let create = uni.createAnimation({
					duration: 0
				});
				create.translate(x).step();
				_this.ballAnimation = create.export();
			},
		}
	};
</script>

<style lang="scss" scoped>
	.hover_ball_cell {
		position: fixed;
		overflow: hidden;
		border-radius: 50%;
		transform: translate(-50%, 0);
		z-index: 9999;
		bottom: 200rpx;
		right: 50rpx;
		background: #06AFFF;
		border-radius: 50%;
		width: 80rpx;
		height: 80rpx;
		padding: 10rpx;
		color: #fff;
		box-shadow: 2px 2px 5px 2px #06AFFFc2;

		.wave {
			position: relative;
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 50%;
		}
	}
</style>
