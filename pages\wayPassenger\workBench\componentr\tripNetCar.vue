<template>
	<view class="trip_bosx">
		<view class="trip_s_tit u-flex u-row-between">
			<view class="trip_icon">
				<u-icon size="40" name="https://zqcx.di-digo.com/app/image/wang.png">
				</u-icon>
			</view>
			<view class="u-flex">
				<span class="u-text-bold trip_carNumber">{{item.regulationName ?item.regulationName:''}}</span>
			</view>
			<view class="u-flex">
				<view class="trip_s_tag" style="margin-right: 20rpx;">
					<u-tag :text="item.compOrderStateName" plain size="mini"> </u-tag>
				</view>

				<view class="trip_s_uicon" v-if="false">
					<view @click="moreBtn($event,item)">
						<u-icon size="20" name="https://zqcx.di-digo.com/app/image/diandian.png">
						</u-icon>
					</view>
					<view class="trip_s_airClass" v-if="item.airShow">
						<view class="footbtn" @click="goTrip(item)" style="border-bottom: 1px solid #ccc;">
							<u-icon name="list"></u-icon>去下单
						</view>
						<view class="footbtn hui" @click="cancelBtn(item)">
							<u-icon name="close"></u-icon>取消订单
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="trip_s_top">北京 ** 上海 ** 深圳</view> -->
		<view class="trip_s_content u-flex u-row-between">
			<view class="trip_s_c_txt u-text-center">
				<view class="trip_s_c_c">
					{{getHhmm(item.reserveStartTime)}}
				</view>
				<view class="trip_s_c_b">
					{{getYmd(item.reserveStartTime) }}
				</view>
			</view>
			<view class="flex_line_box">
				<view class="flex_line"></view>
				<view class="flex_bg">
					{{second(Number(item.reserveEndTimes) - Number(item.reserveStartTimes))}}
				</view>
			</view>
			<view class="trip_s_c_txt u-text-center">
				<view class="trip_s_c_c">
					{{getHhmm(item.reserveEndTime) }}
				</view>
				<view class="trip_s_c_b">
					{{getYmd(item.reserveEndTime)}}
				</view>
			</view>
		</view>
		<view class="trip_s_footer u-flex u-row-around">
			<!-- <view class="footbtnT huiR" @click="gonav(item,false)"> -->
			<view class="footbtnT huiR" @click="closeClick(item,false)">
				取消订单
			</view>
			<view class="footbtnT focus" @click="openShow(item)">
				去下单
			</view>
		</view>

		<template v-if="item.orderCarTravelList.length !=0">
			<view class="trip" v-for="ite in item.orderCarTravelList" :key="ite.reserveStartTime">
				<view class="t_item">
					<span class="t_i_table bold">行程单号: </span>
					<span class="t_i_text">{{ite.netTravelCode?ite.netTravelCode:''}}</span>
				</view>
				<view class="t_item u-flex ">
					<view class="u-flexs" style="width: 50%;">
						<span>
							<u-icon :name="'https://zqcx.di-digo.com/app/image/neMy.png'"></u-icon>
						</span>
						<span class="t_i_text">{{ite.psgName}} {{maskPhone(ite.psgMobile)}}</span>
					</view>
					<view class="u-flexs" style="width: 50%;">
						<span>
							<u-icon :name="'https://zqcx.di-digo.com/app/image/neTime.png'"></u-icon>
						</span>
						<span class="t_i_text">
							{{$u.timeFormat(ite.reserveStartTime, 'mm月dd日 hh:MM')}}
						</span>
					</view>
				</view>
				<view class="t_item u-align-items">
					<span>
						<u-icon :name="'https://zqcx.di-digo.com/app/image/diang.png'" size='10'></u-icon>
					</span>
					<span class="t_i_text">{{ite.fromAddrName}}</span>
				</view>
				<view class="t_item u-align-items">
					<span>
						<u-icon :name="'https://zqcx.di-digo.com/app/image/diany.png'" size='10'></u-icon>
					</span>
					<span class="t_i_text">{{ite.toAddrName}}</span>
				</view>

				<!-- <view class="useCar" @click="gonav(ite,true,item)">
					去用车
				</view> -->
				<view class="useCar" @click="goCarClick(ite,true,item)">
					去用车
				</view>
			</view>
		</template>

		<u-popup :show="networkShow" mode="center" :round="3" @close="()=>{networkShow=false}" :customStyle="styleObjr">
			<vehicleReminder :data='item.appApplyRegulationVo' @catShow='carClick' :shows='false'></vehicleReminder>
		</u-popup>
	</view>
</template>

<script>
	import {
		mytravellist,
		mytravelinfo,
		mytravelCancel
	} from '@/config/api.js';
	import vehicleReminder from '@/pages/wayPassenger/workBench/componentr/vehicleReminder.vue'
	export default {
		components: {
			vehicleReminder
		},
		props: {
			item: {
				type: Object,
				default: () => {
					return {}
				}
			}
		},
		data() {
			return {
				networkShow: false,
				styleObjr: {
					width: '85%'
				},
				goCarObj:{}
			}
		},
		methods: {
			goCarClick(ite,type,item){
				this.$set(this.goCarObj,'ite',ite)
				this.$set(this.goCarObj,'item',item)
				this.networkShow= true
			},
			
			closeClick(item){
				let that = this
				uni.showModal({
					content: '是否取消当前订单',
					success: (res) => {
						if (res.confirm) {
							mytravelCancel({
								id: item.applyId,
								travelType: item.travelType
							}).then(res => {
								uni.$u.toast("操作成功")
								setTimeout(() => {
									this.$emit('list', item)
								}, 1000)
							})
						}
					}
				})
			},
			carClick(val) {
				this.networkShow = false
				if (val) {
					this.gonav(this.goCarObj.ite, true)
				}
			},
			openShow(item) {
				uni.navigateTo({
					url: '/pages/map/mapNet?id=' + item.applyId
				})

				return
				this.networkShow = true //原先 去用车
			},
			gonav(ite, t, item={}) {
				item.types = t
				item.netTravelId = ite.netTravelId
				this.$emit('tripClick', item)
			},
			getHhmm(date) {
				let artr = date.split(' ')[1].split(':')
				return `${artr[0]}:${artr[1]}`
			},
			getMmdd(date) {
				let artr = date.split(' ')[0].split('-')
				return `${artr[1]}-${artr[2]}`
			},
			getYmd(date) {
				let artr = date.split(' ')[0].split('-')
				return `${artr[0]}-${artr[1]}-${artr[2]}`
			},
			getWeekDay(date) {
				let dater = new Date(date)
				let week;
				if (dater.getDay() == 0) week = "周日"
				if (dater.getDay() == 1) week = "周一"
				if (dater.getDay() == 2) week = "周二"
				if (dater.getDay() == 3) week = "周三"
				if (dater.getDay() == 4) week = "周四"
				if (dater.getDay() == 5) week = "周五"
				if (dater.getDay() == 6) week = "周六"
				return week;
			},
			maskPhone(phoneNumber) {
				return phoneNumber.substr(0, 3) + '****' + phoneNumber.substr(7);
			},
			second(value) {
				// console.log(value, 'value1');
				let theTime = parseInt(value / 1000); // 秒
				let middle = 0; // 分
				let hour = 0; // 小时

				if (theTime >= 60) {
					middle = parseInt(theTime / 60);
					theTime = parseInt(theTime % 60);
					if (middle >= 60) {
						hour = parseInt(middle / 60);
						middle = parseInt(middle % 60);
					}
				}
				let result = "";
				if (theTime > 0) {
					result = "" + parseInt(theTime) + "秒"
				}
				if (middle > 0) {
					result = "" + parseInt(middle) + "分" + result;
				}
				if (hour > 0) {
					result = "" + parseInt(hour) + "小时" + result;
				}
				return result ? result : 0 + "秒";
			}
		}
	}
</script>

<style lang="scss" scoped>
	.trip_bosx {

		.trip_b_time {
			margin: 20rpx 13rpx;
			font-size: 28rpx;
		}

		.trip_b_time.before {
			color: #999;
		}



		.trip_s_tit {
			border-bottom: 1px solid #D6DAE9;
			padding: 18rpx 0 14rpx 0;
			// margin-left: 13rpx;
			margin-right: 13rpx;
			position: relative;

			.trip_icon {
				position: absolute;
				top: 0;
				left: 0;
			}


			.trip_carNumber {
				margin-left: 80rpx;
			}

			.trip_s_tag {
				margin: 0 28rpx;
			}

			.trip_s_uicon {
				position: relative;

				.trip_s_airClass {
					position: absolute;
					background-color: #fff;
					width: 240rpx;
					right: 20rpx;
					top: 60rpx;
					box-shadow: 0 0 3px 0px #6666665e;
					// 1px 1px 5px 0px #bcbcbc5e
					z-index: 999;
					border-radius: 20rpx;
					transition: background 0.3s ease-in-out;

					.footbtn {
						height: 80rpx;
						line-height: 80rpx;
						font-size: 29rpx;
						display: flex;
						justify-content: space-evenly;
						align-items: center;
					}

					.border {
						border-top: 1px solid #ccc;
						border-bottom: 1px solid #ccc;
					}


				}
			}


		}

		.trip_s_content {
			margin-top: 24rpx;
			padding-bottom: 20rpx;
			position: relative;

			.trip_s_c_txt {
				margin: 0 48rpx;

				.trip_s_c_t {
					font-size: 24rpx;
					color: #404040;
				}

				.trip_s_c_c {
					font-size: 36rpx;
					color: #404040;
					margin-top: 10rpx;
				}

				.trip_s_c_b {
					font-size: 24rpx;
					color: #999999;
					margin-top: 12rpx;
				}
			}

			.flex_line_box {
				position: absolute;
				left: 50%;
				margin-left: -106rpx;

				.flex_line {
					width: 212rpx;
					height: 4rpx;
					border-radius: 2rpx;
					background-color: #F1F4FD;
				}

				.flex_bg {
					width: 180rpx;
					height: 46rpx;
					border-radius: 24rpx;
					background-color: #F1F4FD;
					text-align: center;
					font-size: 24rpx;
					color: #404040;
					line-height: 46rpx;
					position: absolute;
					left: 50%;
					margin-left: -90rpx;
					top: -22rpx;
				}
			}
		}

		.trip_s_footer {
			border-top: 1px solid #eaecf3;
			border-bottom: 1px solid #eaecf3;
			// .footbtn {
			// 	text-align: center;
			// 	width: 33.3%;
			// 	height: 80rpx;
			// 	line-height: 80rpx;
			// 	font-size: 28rpx;
			// }

			.footbtnCenter {
				border-left: 1px solid #F9FAFE;
				border-right: 1px solid #F9FAFE;
			}

			.hui {
				color: #999999;
			}

			.huiR {
				color: #999999;
				border-right: 1px solid #eaecf3;
			}

			.footbtnT {
				text-align: center;
				width: 50%;
				height: 60rpx;
				line-height: 60rpx;
				font-size: 28rpx;
			}

			.focus {
				color: #346CF2;
			}
		}
	}

	.trip_s_top {
		padding: 20rpx 20rpx 0 20rpx;
		font-size: 24rpx;
		color: #000;
		font-weight: bold;
		text-align: center;
	}

	.trip {
		font-size: 24rpx;
		margin: 20rpx;
		padding: 20rpx;
		background: #E8DFF5;
		border-radius: 16rpx;
		position: relative;
	}

	.t_item {
		margin-bottom: 6rpx;
	}

	.t_i_table {}

	.t_i_text {
		margin-left: 6rpx;
	}

	.useCar {
		position: absolute;
		right: 10rpx;
		bottom: 10rpx;
		color: #6483F9;
		border: 1px solid #6483F9;
		padding: 1rpx 10rpx;
		border-radius: 6rpx;
	}
</style>