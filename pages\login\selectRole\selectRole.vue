<template>
	<view class="selectRole">
		<view class="logo u-flex u-row-center">
			<u--image :showLoading="true" src="https://zqcx.di-digo.com/app/image/blogo.png" width="357rpx"
				height="146rpx"></u--image>
		</view>
		<view class="selectBtn">
			<view class="btn u-flex" @click="passenger(1)">
				<u-icon name="https://zqcx.di-digo.com/app/image/head_1.png" size="34"></u-icon>

				<view class="text">
					我是乘客/调度员/审核员
				</view>
			</view>

			<view class="btn u-flex" @click="passenger(2)">
				<u-icon name="https://zqcx.di-digo.com/app/image/head_2.png" size="34"></u-icon>

				<view class="text">
					我是司机
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	import {
		appcacherole
	} from '@/config/api.js';
	export default {
		data() {
			return {
				userInfo: {}
			}
		},
		mounted() {
			this.userInfo = this.$common.getItem('userInfo')
		},
		methods: {
			passenger(type) {
				appcacherole({
					cacheRoleType: type
				}).then(data => {
					this.userInfo.cacheRoleType = type;
					this.$common.setItem('userInfo', this.userInfo)
					if (type == 1) {
						uni.switchTab({
							url: '/pages/wayPassenger/index/index'
						})
					} else {
						uni.navigateTo({
							url: '/pageDriver/driveIndex/driveIndex',
						})
					}
				})
			},

		}
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #fff;

		.selectRole {
			overflow: hidden;

			.logo {
				margin-top: 124rpx;
			}

			.selectBtn {
				position: absolute;
				bottom: 143rpx;
				font-size: 28rpx;
				padding: 0 33rpx;
				width: calc(100vw - 66rpx);

				.btn {
					padding: 30rpx 20rpx;
					border: 1px solid #CCCCCC;
					margin-bottom: 20rpx;

					.text {
						margin-left: 20rpx;
					}
				}
			}

		}
	}
</style>
