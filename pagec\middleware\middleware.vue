<template>
	<view>
		<getSeat v-if="option.type==3||option.type==4" @seatClose='seatClose'></getSeat>


		<u-datetime-picker :show="option.type==2" v-model="dateModel" :title='option.date' mode='time'
			@close="closeBack" @cancel="closeBack" :formatter='formattes'
			@confirm='confirm'></u-datetime-picker>

	</view>
</template>

<script>
	const timer = () => {
		const now = new Date();
		const hours = now.getHours();
		const minutes = now.getMinutes();
		return `${hours}:${minutes}`
	}
	import {
		formatter
	} from "@/common/commonType.js"
	import getSeat from '@/components/getSeat/getSeat.vue'
	export default {
		components: {
			getSeat
		},
		data() {
			return {
				dateModel: timer(),
				option: {}
			}
		},
		onLoad(option) {
			this.option = option
		},
		methods: {
			closeBack(){
				uni.navigateBack({
					delta: 1
				});
			},
			confirm(e) {
				let obj = {
					value: `${this.option.date} ${e.value}`,
					valueName: `${this.option.date} ${e.value}:00`
				}
				this.naviBack(obj)
			},
			seatClose(e) {
				if (e) {
					let lo = e.location.split(',')
					e.longitude = lo[0]
					e.latitude = lo[1]
					e.value=e.name
					this.naviBack(e)
				} else {
					uni.navigateBack({
						delta: 1
					});
				}
			},
			naviBack(obj) {
				let pages = getCurrentPages(); //获取跳转的所有页面
				let nowPage = pages[pages.length - 1]; //当前页
				let prevPage = pages[pages.length - 2]; //上一页
				try {
					prevPage.$vm.setData(obj, this.option.type)
				} catch (e) {
					uni.$u.toast('选择数据有误,请返回重新选择')
				}

				uni.navigateBack({
					delta: 1
				});
			},
			formattes(type, value) {
				return formatter(type, value)
			},
		},

	}
</script>

<style>

</style>