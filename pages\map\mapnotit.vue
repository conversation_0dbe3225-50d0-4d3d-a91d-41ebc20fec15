<template>
	<view>
		<web-view :src="src" ></web-view>
		<u-skeleton rows='8' :titleWidth='"100%"' loading :rowsWidth='"100%"' :rowsHeight='60'
			v-if='skeleShow'></u-skeleton>
	</view>
</template>

<script>
	import qs from 'qs'
	export default {
		data() {
			return {
				src: '',
				optionr: {},
				show: true,
				skeleShow: false
			};
		},
		onShow() {
			// uni.showLoading({
			// 	title: '加载中',
			// 	mask: true
			// })
			// this.getVal()
		},
		onLoad(option) {
			this.skeleShow = true
			uni.showLoading({
				title: '正在加载中..',
				mask: true
			})
			this.optionr = option
			this.getVal()
		},
		methods: {
			getVal() {
				let that = this
				uni.getLocation({
					type: 'gcj02',
					isHighAccuracy: true,
					highAccuracyExpireTime: 3500,
					geocode: true, //设置该参数为true可直接获取经纬度及城市信息
					success: function(res) {
						if (that.optionr.longitude && that.optionr.latitude) {
							if (that.optionr.longitude == res.longitude && that.optionr.latitude == res
								.latitude) {
								// alert(`${res.longitude},${res.latitude}-1`)
								that.openUrlr(that.optionr)
								return
							} else {
								// alert(`${res.longitude},${res.latitude}-2`)
								that.optionr.longitude = res.longitude
								that.optionr.latitude = res.latitude
								that.openUrlr(that.optionr)
								return
							}
						} else {
							// alert(`${res.longitude},${res.latitude}-3`)
							that.optionr.longitude = res.longitude
							that.optionr.latitude = res.latitude
							that.openUrlr(that.optionr)
							return
						}
					},
					fail: function() {
						that.optionr.longitude = ''
						that.optionr.latitude = ''
						that.openUrlr(that.optionr)
						uni.showToast({
							title: '获取地址失败，将导致部分功能不可用',
							icon: 'none'
						});
					}
				});
			},
			getShow() {
				// uni.showLoading({
				// 	title: '加载中',
				// 	mask: true
				// })
				this.show = false
				setTimeout(() => {
					this.show = true
				}, 5)
			},
			openUrlr(option) {

				setTimeout(() => {
					this.skeleShow = false
					// let urlr = 'https://zqcxdev.di-digo.com/app-h5/'
					let urlr = 'https://zqcx.di-digo.com/app-h5/'
					// console.log(this.$urlrs, '---------')
					// let urlr = this.$urlrs
					// this.src = option.url + '?' + qs.stringify(option)
					// #ifdef H5
					option.type = "H5"
					option.accessToken = JSON.parse(uni.getStorageSync("userInfo")).accessToken
					this.src = option.url + '?' + qs.stringify(option)
					// #endif
					// #ifdef MP-WEIXIN
					option.type = "WEIXIN"
					option.accessToken = JSON.parse(uni.getStorageSync("userInfo")).accessToken
					this.src = `${urlr}${option.url}?${qs.stringify(option)}`
					// #endif
					uni.hideLoading()
				}, 600)
			}
		},
	}
</script>

<style lang="scss">
	/deep/.animate {
		width: 100% !important;
	}
</style>