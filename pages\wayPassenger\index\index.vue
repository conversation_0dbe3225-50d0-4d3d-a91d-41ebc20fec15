<template>
	<view class="indexr">
		<u-navbar :title="scrollTop>100?'':'正其出行'" :placeholder="true" :bgColor="scrollTop>100?'#346CF200':'#346CF2'"
			:titleStyle="{color:'#fff'}">
			<view slot="left"></view>
		</u-navbar>

		<view class="index_top" ref='indexTop'>
			<view class="top_togreet" :class="{'top_togreet_mag':scrollTop>100}">
				{{toGreetr()}}
			</view>
			<view class="top_tiemout">
				{{tiemOutr()}}
			</view>
			<view class="to_tips u-flex " v-show="tipsList&&tipsList.length!=0">
				<u-notice-bar fontSize='15' direction='column' :duration='2500' :text="tipsList" bgColor='#fff5'
					color='#000' @click='openTion'></u-notice-bar>
				<!-- <u-icon name="volume"></u-icon>
				<span>您有1个进行中的网约车订单,请前往查看</span> -->
			</view>
		</view>

		<view class="idx_contenr">

			<u-tabs :list="newListType" @change="tabsChange" :current="tabsidx"
				:activeStyle="{ color: '#303133',fontWeight: 'bold',transform: 'scale(1.05)',}"
				:inactiveStyle="{color: '#303133',transform: 'scale(1)'}"
				itemStyle="width:25%; height: 40px; padding:0">
			</u-tabs>
			<view>
				<motype :list="newList" :nums="2" :col="2" @setItem="goEntp" :tabsidx="tabsidx"></motype>
			</view>
		</view>

		<view class="banner">
			<u-swiper :list="bannerList" :height="160" :radius="6" indicator indicatorMode="line" circular>
			</u-swiper>
		</view>

		<view class="idx_find">
			<view class="find_text">
				发现
			</view>
			<view class="find_boxr">
				<view class="find_boxr_textone">了解正其出行</view>
				<view class="find_boxr_texttwo">多种员工出行使用场景，让企业用车更便捷</view>
				<view class="find_type u-align-items u-row-around">
					<view class="u-flex" v-for="item in findList" :key="item">
						<u-icon name="checkmark-circle" color="#325BFF" size="15"></u-icon>
						<span style="margin-left: 10rpx;">{{item}}</span>
					</view>
				</view>
			</view>
		</view>

		<view class="idx_bottom">
			正其出行，让企业出行管理更简单
		</view>

	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from 'vuex';
	import {
		regulationList,
		appcacherole,
		getuserinfo
	} from '@/config/api.js';
	import {
		messagecount
	} from '@/config/consoler.js';
	import motype from './motype.vue'

	import tabBart from '@/components/tabBart/tabBart.vue'
	export default {
		components: {
			tabBart,
			motype,
		},
		data() {
			return {
				tipsList: [],
				newList: [],
				officialCarsRegulation: [],
				privateCarsRegulation: [],
				netCarsRegulation: [],
				tabsidx: 0,
				tabsidxr: 1,
				newListType: [],
				listType: [{
						name: '企业用车',
						id: 1,
					},
					{
						name: '私车公用',
						id: 2,
					}, {
						name: '网约车',
						id: 3,
					}, {
						name: '自费用车',
						id: 4,
					},
				],
				findList: ['多种场景', '用车合规', '降本增效'],
				bannerList: [
					'https://zqcx.di-digo.com/app/image/lunbo_1.png',
				],
				scrollTop: 0
			}
		},
		computed: mapState(['hasLogin', 'uerInfo']),
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		onLoad() {
			this.$nextTick(() => {
				// console.log(this.listTyper);
			})
		},
		onShow() {
			this.userInfo = this.$common.getItem('userInfo')
			this.bindLogin()
			this.getMsg()
		},
		methods: {
			openTion(e) {
				console.log(e, 'e');
			},
			getMsg() {
				messagecount({}).then(res => {
					let sum = 0
					let gentle = 0
					gentle = res.countPush + res.countSystem
					sum = gentle > 99 ? '99+' : `${gentle}`
					uni.setTabBarBadge({
						index: 3,
						text: sum ? sum : '0'
					})
				})
			},
			goEntp(item, t) {
				item.typer = this.tabsidx
				item.types = this.tabsidxr
				if (this.tabsidxr == 1 || this.tabsidxr == 2) {
					uni.$u.route({
						url: '/pages/wayPassenger/index/entp/entp',
						params: item
					})
				} else if (this.tabsidxr == 3) {
					let obj = {
						userId: item.userId,
						regulationId: item.regulationId,
						isCheck: item.isCheck,
						regulationType: item.regulationType,
						id: item.regulationId,
					}

					uni.$u.route({
						url: '/pages/wayPassenger/index/customCar/customCar',
						params: obj
					})
				}
			},
			tabsChange(e) {
				this.tabsidxr = e.id
				this.tabsidx = e.index
				this.setDatar()
			},
			...mapMutations(['logout']),
			bindLogin() {
				if (this.$common.getItem('userInfo')) {
					// 两种角色的按缓存角色跳转
					if (this.$common.getItem('userInfo').appTerminal == '1,2') {
						if (this.$common.getItem('userInfo').cacheRoleType == '2') {
							uni.$u.route('/pages/driver/index/index');
						} else if (this.$common.getItem('userInfo').cacheRoleType == '1') {
							this.initBadge()
						} else {
							uni.$u.route('/pages/login/selectRole/selectRole');
						}
					} else {
						// 一种角色的按角色跳转
						if (this.$common.getItem('userInfo').appTerminal == '2') {
							uni.$u.route('/pages/driver/index/index');
						} else {
							this.initBadge()
						}
					}

				} else {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}
			},
			initBadge() {
				if (true) {
					uni.showTabBar()
				} else {
					uni.removeTabBarBadge({
						index: 3
					})
					uni.hideTabBarRedDot({
						index: 4
					})
				}
				regulationList({}).then(data => {
					this.officialCarsRegulation = data.officialCarsRegulation
					this.privateCarsRegulation = data.privateCarsRegulation
					this.netCarsRegulation = data.netCarsRegulation
					this.setNewLisr()
				})
			},
			setNewLisr() {
				this.newListType = this.listType.filter(v => {
					if (v.id == 1 && this.officialCarsRegulation.length != 0) return v
					if (v.id == 2 && this.privateCarsRegulation.length != 0) return v
					if (v.id == 3 && this.netCarsRegulation.length != 0) return v
				})
				// console.log(this.newListType, 'this.newListType ');
				// if(this.officialCarsRegulation.length != 0 )
				this.tabsidx = 0
				if (this.newListType.length != 0) {
					this.tabsidxr = this.newListType[0].id
				}
				this.setDatar()
			},
			setDatar() {
				if (this.tabsidxr == 1) {
					this.newList = this.officialCarsRegulation
				} else if (this.tabsidxr == 2) {
					this.newList = this.privateCarsRegulation
				} else if (this.tabsidxr == 3) {
					this.newList = this.netCarsRegulation
				}
			},
			tiemOutr() {
				let tiemr = new Date().getTime()
				let a = new Array("日", "一", "二", "三", "四", "五", "六");
				let week = new Date().getDay();
				let str = " 星期" + a[week];
				return uni.$u.timeFormat(tiemr, 'mm月dd日') + str
			},
			toGreetr() {
				let tiemr = new Date().getHours()
				return tiemr < 6 ? '凌晨了。' : tiemr < 9 ? '早上好！' : tiemr < 12 ? '上午好！' : tiemr < 18 ?
					'下午好！' : tiemr < 21 ? '傍晚好！' : tiemr < 24 ? '晚上好！' : ''
				// return tiemr < 6 ? '凌晨了,请注意休息。' : tiemr < 9 ? '早上好！崭新的一天。' : tiemr < 12 ? '上午好！开启忙碌的一天。' : tiemr < 18 ?
				// 	'下午好！记得放松。' : tiemr < 21 ? '傍晚好！记得按时吃饭。' : tiemr < 24 ? '晚上好！早点休息。' : ''
			}
		}
	}
</script>

<style lang="scss">
	.indexr {
		height: calc(100vh - 52px);

		.index_top {
			background: url(https://zqcx.di-digo.com/app/image/indexBg.png) no-repeat 50%;
			height: 580rpx;
			background-color: #346CF2;
			background-position: 0 80rpx;
			overflow: hidden;
			color: #FFF;
			background-size: cover;
			position: relative;
		}

		.top_title {
			text-align: center;
			font-weight: bold;
		}

		.top_togreet {
			font-size: 42rpx;
			font-weight: bold;
			margin-top: 280rpx;
			margin-left: 54rpx;
			transition: all 1s;
		}

		.top_togreet_mag {
			margin-top: 360rpx;
		}

		.top_tiemout {
			margin-left: 54rpx;
			line-height: 80rpx;
		}

		.to_tips {
			position: absolute;
			bottom: 40rpx;
			// background: #fff5;
			// border: 2rpx solid #346CF2;
			// border-radius: 10rpx;
			margin: 0 20rpx;
			width: calc(100% - 40rpx);
			// padding: 10rpx;
			// color: #000;
		}

		.idx_contenr {
			background: #fff;
			border-top-right-radius: 30rpx;
			border-top-left-radius: 30rpx;
			// margin-top: -20px;

			position: relative;
			top: -30rpx;
		}

		.idx_find {}

		.find_text {
			line-height: 80rpx;
			margin-left: 20rpx;
		}

		.find_boxr {
			padding: 40rpx;
			margin: 0 20rpx;
			background: #EBF1FF;
			border-radius: 10rpx;
		}

		.find_boxr_textone {
			font-size: 34rpx;
			color: #011C88;
			font-weight: bold;
		}

		.find_boxr_texttwo {
			font-size: 26rpx;
			color: #011C88;
			font-weight: bold;
			line-height: 60rpx;
		}

		.find_type {
			background: #F9FAFE;
			padding: 20rpx;
			font-size: 24rpx;
			color: #011C88;
			margin-top: 20rpx;
		}

		.idx_bottom {
			text-align: center;
			color: #BDCFFE;
			font-size: 28rpx;
			line-height: 80rpx;
			/* #ifdef H5 */
			padding-bottom: 100rpx;
			/* #endif */
		}

		.banner {
			width: 94vw;
			margin: 26rpx auto 0 auto;

		}


	}

	/deep/ .u-notice-bar {
		border-radius: 10rpx;
		padding: 6rpx 24rpx !important;
		border: 1px solid #fff;
		box-shadow: inset 0px 0px 5px 1px #fff;

		&.u-icon__icon {
			color: #346CF2;
		}
	}
</style>