<template>
	<view class="getArea">
		<u-navbar title="选择地区" :autoBack="false" @leftClick="closeBtn" id="unavbar"></u-navbar>
		<view class="header w100 flexw" :style="{height:customBar}">
			<view class="placeholder" :style="{height:statusBar}"></view>
			<view class="flex w100">
				<input class="input" :focus="true" @confirm="doneInput" type="text" confirm-type="search"
					:adjust-position="false" @input="onInput" placeholder="搜索城市 中文/首字母" v-model="searchValue" />
			</view>
		</view>

		<scroll-view class="w100" scroll-y="true" :scroll-into-view="scrollIntoId" :scroll-top='scrollTop'
			:style="{height:scrollHeight}" @touchmove.stop.prevent>
			<view class="dingweiPosi city" v-if="disdingwei" @click="back_city({'city_name':cityr})">
				当前定位城市: {{cityr?cityr:'定位失败'}}
			</view>

			<!-- 城市列表 -->
			<view v-if="searchValue == ''" v-for="(item, index) in list" :id="item.idx" :key="item.idx">
				<view class="letter-header bold">{{ item.idx }}</view>
				<view class="contents">
					<view class="city-div" v-for="(city, i) in item['cities']" :key="i" @tap="back_city(city)">
						<text class="city">{{ city.city_name }}</text>
					</view>
				</view>

			</view>
			<!-- 搜索结果 -->
			<view class="city-div" v-for="(item, index) in searchList" :key="index" @tap="back_city(item)">
				<text class="city">{{ item.city_name }}</text>
			</view>

			<u-empty v-if="searchList.length == 0 && searchValue" text="列表为空"
				icon="http://cdn.uviewui.com/uview/empty/order.png">
			</u-empty>
			<view class="placeholder footer"></view>
		</scroll-view>

		<!-- 右侧字母 -->
		<view class="letters" id="list" v-if="searchValue == ''" @touchstart="touchStart"
			@touchmove.stop.prevent="touchMove" @touchend="touchEnd">
			<!-- <view class="fmin" @click="scrollTo('hot')">最近</view> -->
			<view class="fmin" v-for="item in letter" :key="item.idx">{{ item.idx }}</view>
		</view>

		<!-- 选中之后字母 -->
		<view class="mask" v-if="touchmove">
			<view class="mask-r bold">{{scrollIntoId}}</view>
		</view>
	</view>
</template>

<script>
	import Citys from '../../common/cityCode.js';
	export default {
		props: ['cityr'],
		data() {
			return {
				statusBar: '0px',
				customBar: '45px',

				winHeight: 0,
				itemHeight: 0,
				winOffsetY: 0,
				touchmove: false,


				scrollHeight: this.statusBarHeight,
				ImgUrl: this.ImgUrl,
				letter: [],

				searchValue: '',
				scrollIntoId: '',
				list: [],
				tId: null,
				searchList: [],
				showMask: false,
				disdingwei: true,
				cacheLocation: [{
					'city_name': '北京'
				}], //最近访问
				position: '', //定位获取的位置

				po_tips: '重新定位',
				hotCity: [{
					'city_name': '济南'
				}],

				scrollTop: null,

				optionObj: {},
			}
		},

		watch: {
			list() {
				setTimeout(() => {
					this.setList()
				}, 100)
			}
		},
		mounted() {

			console.log(this.cityr);
			//获取存储的最近访问
			var that = this
			// #ifdef H5
			this.scrollHeight = uni.getSystemInfoSync().windowHeight - parseInt(this.customBar) - 44 + 'px'
			// #endif
			// #ifdef MP-WEIXIN
			this.scrollHeight = uni.getSystemInfoSync().windowHeight - parseInt(this.customBar) - 88 + 'px'
			// #endif
			console.log(Citys, 'Citys');
			this.letter = Citys.index;
			this.list = Citys.list;
			// this.hotCity = Citys.data.hot_city; 
		},
		methods: {
			closeBtn() {
				this.$emit("AreaBtn")
			},
			setList() {
				uni.createSelectorQuery()
					.in(this)
					.select('#list')
					.boundingClientRect()
					.exec(ret => {
						this.winOffsetY = ret[0].top
						this.winHeight = ret[0].height
						this.itemHeight = this.winHeight / this.list.length
					})

			},
			touchStart(e) {
				this.touchmove = true
				let pageY = e.touches[0].pageY
				let index = Math.floor((pageY - this.winOffsetY) / this.itemHeight)
				if (this.list[index]) {
					this.scrollIntoId = this.list[index].idx
				}


			},
			touchMove(e) {
				let pageY = e.touches[0].pageY
				let index = Math.floor((pageY - this.winOffsetY) / this.itemHeight)
				if (this.list[index] && this.list[index].idx === this.scrollIntoId) {
					return false
				}
				if (this.list[index]) {
					this.scrollIntoId = this.list[index].idx
				}
			},
			touchEnd() {
				this.touchmove = false
				this.touchmoveIndex = -1
			},

			doneInput() {
				uni.hideKeyboard()
			},
			move(e) {
				console.log(111, e);
			},
			getId(index) {
				return this.letter[index];
			},
			query(source, text) {
				let res = [];
				var self = this;
				let len = source.length
				var text = text.toLowerCase()
				for (let i = 0; i < len; i++) {
					//单字母搜索
					if (text.length == 1 && /^[a-zA-Z]$/.test(text)) {
						let arr = []
						let idx = text.toUpperCase()
						if (idx == source[i].idx) {
							console.log(222, source[i]);
							return source[i].cities
						}
					}
					//其它搜索
					if (source[i].cities) {
						let _len = source[i].cities.length
						for (var n = 0; n < _len; n++) {
							let _item = source[i].cities[n]
							if (new RegExp('^' + text).test(_item.city_name)) {
								res.push(_item);
								continue;
							}
							if (new RegExp('^' + text).test(_item.city_pinyin)) {
								res.push(_item);
								continue;
							}
							if (new RegExp('^' + text).test(_item.py)) {
								res.push(_item);
								continue;
							}

						}
					}
				}

				return res;
			},

			onInput(e) {
				const value = e.target.value;
				if (value !== '' && this.list) {
					const queryData = this.query(this.list, String(value).trim());
					this.searchList = queryData;
					this.disdingwei = false
				} else {
					this.searchList = [];
					this.disdingwei = true
				}
			},

			back_city(item, hasLocation = false) {
				this.$emit("AreaBtn", item)
			},

			// 重新定位
			getWarpweft() {
				uni.getLocation({
					type: 'wgs84',
					geocode: true,
					success: function(res) {
						console.log('当前位置：' + res.address.city);
						this.city = res.address.city || '郑州'
					}
				});
			}
		}
	};
</script>

<style scoped>
	.getArea {
		transition-duration: 350ms;
		transition-timing-function: ease-out;
		position: fixed;
		inset: 0px;
		z-index: 1002;
		background-color: #F9FAFE;
	}

	.fsmall {
		font-size: 26rpx;
	}

	.fmiddle {
		font-size: 28rpx;
	}

	.blue {
		color: #007AFF;
	}

	.bold {
		color: #ccc;
		/* margin: 20rpx 20rpx 20rpx 0; */
		/* line-height: 60rpx; */
	}

	.flex {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.mask {
		position: fixed;
		z-index: 3;
		top: 40%;
		left: 40%;
	}

	.mask-r {
		height: 120rpx;
		width: 120rpx;
		border-radius: 60rpx;
		display: flex;
		background: rgba(0, 0, 0, 0.5);
		backdrop-filter: blur(5px);
		justify-content: center;
		align-items: center;
		font-size: 40rpx;
		color: #FFFFFF
	}

	.content {
		height: 100%;
		width: 100%;
		background-color: #ffffff;
	}

	.header {
		width: 100%;
		z-index: 8;
		background-color: #FFFFFF;
		/* #ifdef H5 */
		margin-top: 88rpx;
		/* #endif */
		/* #ifdef MP-WEIXIN */
		margin-top: 180rpx;
		/* #endif */
	}


	.back_div {
		width: 100rpx;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.back_img {
		width: 35rpx;
		height: 35rpx;
	}

	.dingweiPosi {
		margin-top: 20rpx;
		padding: 20rpx 30rpx;
		background: #fff;
	}

	.input {
		font-size: 26rpx;
		width: calc(100% - 60rpx);
		height: 60rpx;
		max-height: 60rpx;
		border-radius: 10rpx;
		background-color: #F5F5F5;
		padding-left: 20rpx;
		padding-right: 20rpx;
		box-sizing: border-box;
	}

	.title {
		font-size: 30rpx;
		color: white;
	}

	.show {
		left: 0;
		width: 100%;
		transition: left 0.3s ease;
	}

	.hide {
		left: 100%;
		width: 100%;
		transition: left 0.3s ease;
	}


	.title {
		font-size: 30rpx;
		color: white;
	}

	.letters {
		position: absolute;
		right: 0;
		width: 50rpx;
		color: #cccccc;
		top: 25%;
		text-align: center;
		font-size: 24rpx;
		font-weight: 510;
	}

	.letters .fmin {}


	.letter-header {
		font-size: 28rpx;
		padding: 20rpx 40rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;

	}

	.city-div {
		display: flex;
		padding: 20rpx 0;
		border-bottom-width: 0.5rpx;
		border-bottom-color: #ebedef;
		border-bottom-style: solid;
		align-items: center;
	}

	.city {
		font-size: 28rpx;
		color: #000000;
		padding-left: 30rpx;
	}

	.dingwei {
		/* 	width: 90%;
		margin: auto; */
		padding-top: 25rpx;
		box-sizing: border-box;
		margin-bottom: 26rpx;
	}

	.dingwei .grey {
		margin-bottom: 25rpx;
		margin-left: 30rpx;
	}

	.dingwei_city {
		width: 100%;
		box-sizing: border-box;
		display: flex;
		justify-content: space-between;
		background: #fff;
	}

	.dingwei_city_one {
		height: 60rpx;
		background-color: #F5F5F5;
		border-radius: 30rpx;
		font-size: 26rpx;
		padding: 0 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 20rpx 20rpx 20rpx 0;
	}


	.dingweis {
		width: 32rpx;
		height: 32rpx;
	}

	.dingwei_city_zuijin {
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
	}

	.contents {
		display: flex;
		flex-direction: column;
		background-color: #fff;
	}
</style>