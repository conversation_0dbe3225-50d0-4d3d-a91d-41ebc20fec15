import App from './App'
import Vue from 'vue'
import Common from "./common/common.js"
import uView from "uview-ui"
import store from './store'
import DateUtil from '@/common/DataUtil.js'

Vue.prototype.$urlrs = 'https://zqcxdev.di-digo.com/app-h5/' //测试
Vue.prototype.$store = store
Vue.prototype.$common = Common
Vue.prototype.$DateUtil = DateUtil

Vue.config.productionTip = false

Vue.use(uView);
App.mpType = 'app'


// if (process.env.NODE_ENV === 'development') {
// 	Vue.prototype.$urlrs = 'https://zqcxdev.di-digo.com/app-h5/' //测试
// 	console.log('开发环境');
// } else {
// 	Vue.prototype.$urlrs = 'https://zqcx.di-digo.com/app-h5/' //线上
// 	console.log('生产环境');
// }

// 挂载
const app = new Vue({
	store,
	...App
})

require('@/config/request.js')(app)

app.$mount()
