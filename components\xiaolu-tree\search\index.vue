<template>
	<view class="tmp-box">
		<view class='filterBox'>
			<u-search placeholder="搜索" :showAction="false" v-model="inputVal" @search='handleFllter'></u-search>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				inputVal: "",

			};
		},
		methods: {
			handleFllter(data) {
				this.$emit("confirm", data)
			},
			clears(){
				console.log(this.inputVal)
				this.inputVal=""
				console.log('清除了内容')
			}
		},
	}
</script>

<style lang="scss" scoped>
	.filterBox {
		padding: 27rpx 32rpx;
		.filter-input {
			height: 80rpx;
			background-color: #eeeff0;
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			
			padding-left: 40rpx;
			.filterImg {
				width: 44rpx;
				height: 44rpx;
				margin-right: 20rpx;
			}
			.filterImgs {
				width: 32rpx;
				height: 32rpx;
				
			}
			.text {
				width: 84%;
				background-color: #eeeff0;
				font-size: 32rpx;
				color: #000;
			}
		}
	}
	@import url("../css/icon.css");
</style>
