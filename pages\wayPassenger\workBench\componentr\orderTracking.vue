<template>
	<view class="track">
		<u-steps current="1" dot direction="column">
			<u-steps-item :title="item.titles" v-for="(item,idx) in list" :key="idx">
				<view slot='desc'>
					<slot name='conten' :row='item'></slot>
				</view>
			</u-steps-item>
		</u-steps>
	</view>
</template>

<script>
	export default {
		props: {
			list: {
				type: Array,
				default: () => {
					return []
				}
			}
		},
		data() {
			return {}
		},
		methods: {

		}
	}
</script>

<style scoped>
	.track {
		width: 100%;
		max-height: 50vh;
		overflow: auto;
		padding: 40rpx;
	}
</style>