<template>
	<view>
		<u-navbar title="乘车人" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="u-flex u-row-between ma pa" style="background: #fff;">
			<span class="top_l">常用乘车人</span>
			<span class="top_r" @click="goBtn">+ 添加乘车人</span>
		</view>

		<view class="list u-flex-direction ma pa cont_list" v-for="(item,index) in list" @click="selectClick(item)">
			<span class="top_l">
				{{item.name}}
				<span v-if="item.self" class="self">本人</span>
			</span>
			<span class="font">手机: {{item.mobile}}</span>
			<span class="font">部门: {{item.orgName}}</span>
		</view>

		<view style="padding-bottom:20rpx ;">

		</view>

	</view>
</template>

<script>
	import {
		addpassenger,
		passengerlist
	} from '@/config/consoler.js'
	export default {
		data() {
			return {
				list: [],
			}
		},
		onLoad(o) {
			this.getList()
		},
		methods: {
			goBtn() {
				uni.$u.route('/pages/wayPassenger/index/choiceRide/choiceRide', {
					checkListData: JSON.stringify([])
				});
			},
			selectClick(item) {
				let pages = getCurrentPages(); //获取跳转的所有页面
				let nowPage = pages[pages.length - 1]; //当前页
				let prevPage = pages[pages.length - 2]; //上一页

				uni.showModal({
					content: `您当前选择的是 "${item.name}"`,
					success: (res) => {
						prevPage.$vm.setData(item, 1)
						uni.navigateBack({
							delta: 1
						});
					}
				})
			},
			getList() {
				passengerlist().then(res => {
					this.list = res
				})
			},
			setUser(data) {
				addpassenger({
					userId: data[0].id
				}).then(res => {
					uni.$u.toast(res)
					this.getList()
				})
			},
		}
	}
</script>

<style scoped>
	.list {
		display: flex;
	}

	.top_l {
		font-weight: bold;
	}

	.top_r {
		font-size: 13px;
		border: 1px solid rgb(52, 108, 242);
		padding: 5px;
		color: rgb(52, 108, 242);
		border-radius: 5px;
	}

	.cont_list {
		background: #fff;
		border-radius: 10rpx;
		padding: 20rpx 40rpx !important;
		border: 1px solid #ccc;
	}

	.ma {
		margin: 20rpx;
	}

	.pa {
		padding: 20rpx;
	}

	.font {
		font-size: 27rpx;
		color: #666;
		margin-top: 10rpx;
	}

	.self {
		color: green;
		border: 1px solid green;
		border-radius: 5rpx;
		font-size: 24rpx;
		padding: 0 10rpx;
		margin: 0 20rpx;
	}
</style>