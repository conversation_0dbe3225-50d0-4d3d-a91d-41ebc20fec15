<template>
	<view class="addMyCar">
		<u-navbar :title="carTitle" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="add_conten">
			<view class="add_tip" v-if="optionr.type != 'details'">请确保四角整齐，图片清晰</view>

			<u-cell-group :customStyle='customStyler'>
				<u-cell arrow-direction="down">
					<text class="car_box" slot="title">行驶证(正本)照片</text>
					<view slot="label">
						<u-upload :fileList="cardJust" @afterRead="(event)=>afterRead(event,true)"
							@delete="(event)=>deletePic(event,true)" name="6" multiple :maxCount="1" width="200"
							height="100" style='justify-content: center;'>
							<image src="https://zqcx.di-digo.com/app/image/down.png" mode="widthFix"
								style="width: 200px;height: 100px;"></image>
						</u-upload>
					</view>
				</u-cell>
				<u-cell arrow-direction="down">
					<text class="car_box" slot="title">行驶证(副本)照片</text>
					<view slot="label">
						<u-upload :fileList="cardVice" @afterRead="(event)=>afterRead(event,false)"
							@delete="(event)=>deletePic(event,false)" name="6" multiple :maxCount="1" width="200"
							height="100" style='justify-content: center;'>
							<image src="https://zqcx.di-digo.com/app/image/down.png" mode="widthFix"
								style="width: 200px;height: 100px;"></image>
						</u-upload>
					</view>
				</u-cell>
			</u-cell-group>

			<u-cell-group :customStyle='customStyler'>
				<u-cell :border='false'>
					<text class="car_box" slot="title">车辆信息</text>
				</u-cell>
				<u-cell title="车辆所有人:">
					<u-input v-model="form.carOwner" :readonly="optionr.type == 'details'" border="none"
						placeholder="请填写车辆所有人" slot="value" />
					<u-icon name="close-circle" slot="right-icon" @click='setValue("carOwner")'></u-icon>
				</u-cell>
				<u-cell title="车牌号:" :isLink="true" @click='keyShow=true'>
					<u-input v-model="form.carNumber" :readonly="optionr.type == 'details'" border="none"
						placeholder="请填写车牌号" slot="value" />
					<u-icon name="close-circle" slot="right-icon" @click='setValue("carNumber")'></u-icon>
				</u-cell>
				<u-cell title="车牌颜色:" :isLink="true" @click="carCode(1)">
					<u--input v-model="form.numberColor" disabled disabledColor="#ffffff" placeholder="请选择车牌颜色"
						border="none" slot="value"></u--input>
				</u-cell>
				<u-cell title="车辆类型" :isLink="true" @click="carCode(2)">
					<u--input v-model="form.carCategoryName" disabled disabledColor="#ffffff" placeholder="请选择车辆类型"
						border="none" slot="value"></u--input>
				</u-cell>
				<u-cell title="车辆品牌:" :isLink="true" @click="carCode(4)">
					<u--input v-model="form.carBrandName" disabled disabledColor="#ffffff" placeholder="请选择车辆品牌"
						border="none" slot="value"></u--input>
				</u-cell>
				<u-cell title="车辆品牌型号:" :isLink="true" @click="carCode(5)">
					<u--input v-model="form.brandModelName" disabled disabledColor="#ffffff" placeholder="请选择车辆品牌型号"
						border="none" slot="value"></u--input>
				</u-cell>

				<u-cell title="车身颜色:" :isLink="true" @click="carCode(3)">
					<u--input v-model="form.carColor" disabled disabledColor="#ffffff" placeholder="请选择车身颜色"
						border="none" slot="value"></u--input>
				</u-cell>
			</u-cell-group>

			<view class="footer_box" v-if="optionr.type != 'details'">
				<u-button type="primary" text="确定" @click="submit"></u-button>
			</view>
		</view>
		<!-- 车辆颜色、车牌 -->
		<u-picker :show="carShow" :columns="carColumns" keyName="dicDescribe" @close='carClose' @confirm='carConfirm'
			@cancel='carClose'></u-picker>

		<!-- 车辆品牌 -->
		<u-picker :show="brandShow" :columns="carColumns" keyName="brandModelName" @close='brandClose'
			@confirm='brandConfirm' @cancel='brandClose'></u-picker>
	</view>
</template>

<script>
	import {
		uploadImg,
		carApplyForcar,
		carGetcarbrandlist,
		carGetcarbrandmodellist,
		carGetcarinfo,
		carPostEditorcar
	} from '@/config/consoler.js';
	export default {
		data() {
			return {
				customStyler: {
					"background": "#fff",
					"margin-top": "20rpx",
				},

				cardJust: [],
				cardVice: [],
				form: {

				},

				keyShow: false,
				keyValue: '',

				numberColorList: [],
				carColorList: [],
				carCategoryList: [],
				carType: null,
				carShow: false,
				carColumns: [],

				brandShow: false,
				brandCarList: [],
				brandList: [],
				optionr: {},
				carTitle: ""
			};
		},
		onLoad(op) {
			this.optionr = op
			if (op.type == 'edit') {
				this.getDetail(op)
				this.carTitle = "编辑车辆"
			} else if (op.type == 'details') {
				this.carTitle = "查看车辆详情"
				this.getDetail(op)
			} else {
				this.carTitle = "添加车辆"
			}
			this.initr()

		},
		methods: {
			setValue(name) {
				if (this.optionr.type == 'details') return uni.$u.toast('无法编辑')
				this.$set(this.form, name, '')
			},
			// 打开
			carCode(t) {
				if (this.optionr.type == 'details') return uni.$u.toast('无法编辑')
				this.carType = t
				if (t == 1) {
					this.carColumns = [this.numberColorList]
					this.carShow = true
				}
				if (t == 2) {
					this.carColumns = [this.carCategoryList]
					this.carShow = true
				}
				if (t == 3) {
					this.carColumns = [this.carColorList]
					this.carShow = true
				}
				// 4、5 汽车品牌
				if (t == 4) {
					this.carColumns = [this.brandCarList]
					this.brandShow = true
				}
				if (t == 5) {
					if (!this.form.carTypeId) return uni.$u.toast('请先选择车辆品牌')
					this.carColumns = [this.brandList]
					this.brandShow = true
				}


			},
			// 颜色、类别、车牌
			carConfirm(e) {
				let t = this.carType
				if (t == 1) {
					this.form.numberColor = e.value[0].dicDescribe
				}
				if (t == 2) {
					this.form.carCategory = e.value[0].dicValue
					this.form.carCategoryName = e.value[0].dicDescribe
				}
				if (t == 3) {
					this.form.carColor = e.value[0].dicValue
				}
				this.carClose()
			},
			carClose() {
				this.carShow = false
				this.carColumns = []
			},
			// 车辆品牌系列
			brandConfirm(e) {
				let t = this.carType
				if (t == 4) {
					this.form.carTypeId = e.value[0].carBrandId
					this.form.carBrandName = e.value[0].carBrandName
					this.form.brandModelId = ''
					this.form.brandModelName = ''
					this.getSeries()
				}
				if (t == 5) {
					this.form.brandModelId = e.value[0].brandModelId
					this.form.brandModelName = e.value[0].brandModelName
				}
				this.brandClose()
			},
			brandClose() {
				this.brandShow = false
				this.carColumns = []
			},
			// 保存新增
			submit() {
				if (this.cardJust.length != 0) {
					this.form.drvPertImgUrl1 = this.cardJust[0].urlr
					this.form.drvPertImgUrl1Str = this.cardJust[0].urlr
				}
				if (this.cardVice.length != 0) {
					this.form.drvPertImgUrl2 = this.cardVice[0].urlr
					this.form.drvPertImgUrl2Str = this.cardVice[0].urlr
				}

				if (this.optionr.type == 'add') {
					carApplyForcar(this.form).then(v => {
						uni.$u.toast(v)
						setTimeout(() => {
							uni.navigateBack({
								delta: 1, //返回层数，2则上上页
							})
						}, 500)
					})
				} else {
					carPostEditorcar(this.form).then(v => {
						uni.$u.toast(v)
						setTimeout(() => {
							let pages = getCurrentPages(); // 当前页面
							let beforePage = pages[pages.length - 2]; // 上一页
							uni.navigateBack({
								success: function() {
									beforePage.getList(); // 执行上一页的onLoad方法
								}
							});

						}, 500)
					})

				}

			},
			// 上传图片
			afterRead(flie, type) {
				if (this.optionr.type == 'details') return uni.$u.toast('无法编辑')
				let listr = [].concat(flie.file)
				let flieLen = this[`${type?'cardJust':'cardVice'}`].length
				listr.map((item) => {
					this[`${type?'cardJust':'cardVice'}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				uploadImg(flie.file[0], 4).then(v => {
					if (v) {
						let item = this[`${type?'cardJust':'cardVice'}`][flieLen]
						this[`${type?'cardJust':'cardVice'}`].splice(flieLen, 1, Object.assign(item, {
							status: 'success',
							message: '',
							urlr: v,
							url: flie.file[0].url
						}))
						flieLen++
						uni.$u.toast('上传成功')
					} else {
						this[`${type?'cardJust':'cardVice'}`].splice(flieLen, 1)
						uni.$u.toast('上传失败')
					}
				})
			},
			// 删除附件
			deletePic(flie, type) {
				if (this.optionr.type == 'details') return uni.$u.toast('无法删除')
				this[`${type?'cardJust':'cardVice'}`].splice(flie.index, 1)
				uni.$u.toast('删除成功')
			},
			// 获取初始值
			initr() {
				let arrs = this.$common.getItem('dicVoList')
				console.log(arrs, 'arrs');
				arrs.forEach(item => {
					if (item.dicCode == "car_number_color") {
						this.numberColorList = item.dicValueList
					}
					if (item.dicCode == "car_color") {
						this.carColorList = item.dicValueList
					}
					if (item.dicCode == "car_category") {
						this.carCategoryList = item.dicValueList
					}
				})
				let obj = {
					pageNum: 1,
					pageSize: 500
				}
				carGetcarbrandlist({
					params: obj
				}).then(v => {
					v.pageList.forEach(res => {
						res.brandModelName = res.carBrandName
					})
					this.brandCarList = v.pageList
				})
			},
			// 获取详情
			getDetail(row) {
				carGetcarinfo({
					id: row.id
				}).then(res => {
					this.cardJust = [{
						url: res.drvPertImgUrl1,
						urlr: res.drvPertImgUrl1Str
					}]
					this.cardVice = [{
						url: res.drvPertImgUrl2,
						urlr: res.drvPertImgUrl2Str
					}]

					res.carCategoryName = this.carCategoryList.filter(v => {
						return v.dicValue == res.carCategory
					})[0].dicDescribe
					console.log(res.carCategoryName);
					this.form = res
				})
			},
			// 获取车辆系列
			getSeries() {
				let obj = {
					pageNum: 1,
					pageSize: 500,
					carBrandId: this.form.carTypeId
				}
				carGetcarbrandmodellist({
					params: obj
				}).then(v => {
					this.brandList = v.pageList
					this.$nextTick(() => {
						this.carCode(5)
					})
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.addMyCar {
		.add_conten {
			padding-bottom: 150rpx;
		}

		.add_tip {
			padding: 10rpx 20rpx;
			background: rgba(23, 120, 252, 0.16);
		}

		.car_box {
			font-size: 34rpx;
			margin: 10rpx;
		}

		.is-bottom {
			background: #fff;
			// padding: 0 20rpx;
			margin-top: 20rpx;
		}

		.add_btm {
			position: fixed;
			bottom: 0;
			width: calc(100% - 66rpx);
			padding: 20rpx 32rpx;
			background-color: #fff;
		}

		/deep/ .u-upload__wrap {
			justify-content: center;
		}

		/deep/ .u-input--square {
			flex: 3 !important;
		}

		/deep/ .u-cell--clickable {
			background: #fff;
		}

	}
</style>