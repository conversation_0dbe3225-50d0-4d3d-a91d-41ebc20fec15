body {
	font-size: 12px;
	font-family: "Microsoft YaHei", <PERSON><PERSON>;
	overflow-x: hidden;
	overflow-y: auto;
	margin: 0;
	background-color: #fff;
}

/* 公共样式 */
.u-flex {
	display: flex;
	flex-direction: row;
	align-items: center;
}

/* 换行 */
.u-flex-wrap {
	flex-wrap: wrap;
}

/* 不换行 */
.u-flex-nowrap {
	flex-wrap: nowrap;
}

/* 垂直居中 */
.u-col-center {
	align-items: center;
}

/* 顶部对齐 */
.u-col-top {
	align-items: flex-start;
}

/* 底部对齐 */
.u-col-bottom {
	align-items: flex-end;
}

/* 左边对齐 */
.u-row-left {
	justify-content: flex-start;
}

/* 水平居中 */
.u-row-center {
	justify-content: center;
}

/* 右边对齐 */
.u-row-right {
	justify-content: flex-end;
}

/* 水平两端对齐，项目之间的间隔都相等 */
.u-row-between {
	justify-content: space-between;
}

/* 水平每个项目两侧的间隔相等，所以项目之间的间隔比项目与父元素两边的间隔大一倍 */
.u-row-around {
	justify-content: space-around;
}

.u-flex-1 {
	flex: 1;
}

.u-flex-2 {
	flex: 2;
}

.u-flex-3 {
	flex: 3;
}

.u-flex-4 {
	flex: 4;
}

.u-flex-5 {
	flex: 5;
}

.u-flex-6 {
	flex: 6;
}

.u-flex-7 {
	flex: 7;
}

.u-flex-8 {
	flex: 8;
}

.u-flex-9 {
	flex: 9;
}

.u-flex-10 {
	flex: 10;
}

.u-flex-11 {
	flex: 11;
}

.u-flex-12 {
	flex: 12;
}

/* 文字左对齐 */
.u-text-left {
	text-align: left;
}

/* 文字居中对齐 */
.u-text-center {
	text-align: center;
}

/* 文字右对齐 */
.u-text-right {
	text-align: right;
}

/* 文字加粗 */
.u-text-bold {
	font-weight: bold;
}


.text-right {
	text-align: right;
}

.text-center {
	text-align: center;
}

#container {
	width: 100vw;
	height: calc(100vh - 100px);
	z-index: 80;
	transition: all .5s linear 0s;
}

#containeFu {
	height: 80px;
	position: relative;
	z-index: 90;
	width: 100vw;
	bottom: 60px;
	background: linear-gradient(to bottom, rgba(255, 255, 255, 0), #F9FAFE 60%)
}


/* 出发地 目的地 */
.ui-searchbar-wrap {
	height: 60px;
	background: #F9FAFE
}

.ui-searchbar {
	height: 32px;
	line-height: 32px;
	margin: 0 16px;
	color: inherit;
}

.ui-border-radius {
	border-radius: 36px;
}

.line-s {
	width: 1px;
	height: 16px;
	display: inline-block;
	background-color: #B5BCD6;
	position: absolute;
	top: 50%;
	margin-top: -8px;
	left: 90px;
}

.ui-searchbar input {
	padding-left: 20px;
}

.selcet_lon {
	width: 60px;
	padding: 0;
	text-align: center;
}

.posBox {}

.posBox li {
	margin-top: 10px;
	margin-bottom: 10px;
}

.posBox p {
	font-size: 12px;
}

.posBox .posIcon {
	width: 30px;
	height: 30px;
	margin: 12px 12px 8px 6px;
}



/* 订单详情 */
.mapTip {
	background-color: #fff;
	font-size: 12px;
	padding: 4px 10px;
	border-radius: 3px;
}

.mapTip span {
	color: #346CF2;
}

.mapText {
	width: 182px;
	white-space: normal;
	word-break: break-all;
}


.backbtn {
	position: fixed;
	background-color: #fff;
	top: 20px;
	left: 16px;
	width: 40px;
	height: 40px;
	z-index: 98;
	text-align: center;
	line-height: 40px;
	font-size: 28px;
	color: #666666;
	border-radius: 4px;
}

.backbtnr {
	position: fixed;
	background-color: #fff;
	top: 0;
	left: 0;
	width: 100vw;
	height: 40px;
	z-index: 999;
	text-align: center;
	line-height: 40px;
	font-size: 28px;
	color: #666666;
	border-radius: 4px;
}

.backbtnr-top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 20px;
}

.top_tips {
	position: absolute;
	top: 50px;
	width: 100%;
	z-index: 998;
	display: flex;
	justify-content: center;
}

.top_tips span {
	color: #346CF2;
}

.top_tips_item {
	background: #ffffffbd !important;
	padding: 5px 10px !important;
	border-radius: 20px !important;
	box-shadow: 0 0 4px 1px #6666665e !important;
}

/* .mapdown {
	width:calc(100vw - 14px);
	position: fixed;
	bottom:20px;
	z-index: 99;
	margin: 4px 7px;
	transition: all .3s linear 0s;
} */
.mapdown {
	width: 100vw;
	position: fixed;
	/* height: 85%; */
	background: #fff;
	bottom: 0;
	z-index: 99;
	transition: all .5s linear 0s;
	border-radius: 10px 10px 0px 0px;
	/* box-shadow: 0px 1px 4px 2px rgba(136, 136, 136, 0.4); */
}

.fullScreen {
	/* height: 85%; */
	background: #fff !important;
	margin: 0 !important;
	width: 100vw !important;
	bottom: 0 !important;
}

.mapdown-down-scroll {
	height: calc(100% - 48px) !important;
	overflow-y: auto !important;
	z-index: 1000;
}

.mapdown-top {
	line-height: 40px;
	font-size: 16px;
	padding: 0 10px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.mapdown-topr {
	background-color: #fff;
	height: 25px;
	line-height: 25px;
	box-shadow: 0px 1px 2px 0px rgba(136, 136, 136, 0.3);
	border-radius: 7px 7px 0px 0px;
	font-size: 12px;
	padding: 0 9px;
}

.mapdown-slio {
	height: 3px;
	background: #dbdbdb;
	width: 15%;
	margin-top: 4px;
	border-radius: 20px;
}

.ui-img-icon {
	width: 30px;
	height: 30px;
	background-size: 30px 30px;
	position: relative;
}

.upload-input {
	width: 80px;
	height: 80px;
	position: relative;
	text-align: center;
	overflow: hidden;
	background-color: #fbfdff;
	border: 1px dashed #c0ccda;
	border-radius: 6px;
}

.upload-ipt {
	position: absolute;
	left: 0;
	width: 80px;
	height: 80px;
	opacity: 0;
	z-index: 999;
}

.dorg {
	background: #fff0 !important;
	box-shadow: none !important;
	width: 100vw !important;
	padding: 0;
}

.dorg_icon {
	font-size: 30px;
	color: #fff;
}

.trip_s_airClass {
	position: absolute;
	background-color: #fff;
	width: 120px;
	right: 20px;
	top: 45px;
	box-shadow: 0 0 8px 3px #6666665e;
	z-index: 9999;
	border-radius: 10px;
}

.footbtn {
	height: 40px;
	line-height: 40px;
	font-size: 14px;
	display: flex;
	justify-content: space-evenly;
	align-items: center;
}

.fa-icont {
	margin-right: 10px;
	font-size: 14px;
	color: #346cf2;
}

.border {
	border-top: 1px solid #ccc;
	border-bottom: 1px solid #ccc;
}

.money {
	/* background-color: #F7F7F7; */
	/* padding: 10px; */
	margin: 5px 10px;
	/* border-radius: 5px; */
}

.money-top {
	border-radius: 5px 5px 0 0;
	padding: 0 15px;
	background-color: #F7F7F7;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.money-btm {
	border-radius: 0 0 5px 5px;
	padding: 0 15px;
	background-color: #F7F7F7;
	transition: all .5s linear 0s;
	display: block;
}

.stepList {
	display: flex;
	flex-direction: column;
	margin-bottom: 5px;
}

.app_t_line {
	padding: 6px;
}

.mapdownboxr {
	display: flex !important;
	flex-direction: column;
	align-items: center;
	margin-right: 20px;
}

.mapdownbox p {
	font-size: 13px;
}

.mapdownbox .posIcon {
	width: 30px;
	height: 30px;
	margin: 12px 12px 8px 6px;
}

.cssFont {
	font-size: 13px;
}

.mapdown-down {
	border-radius: 7px 7px 0px 0px;
	overflow: hidden;
	/* margin-top: 7px; */
	background: rgba(255, 255, 255, .85);
}

.mapdown-imgbox {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 10px;
}

.d_txt {
	line-height: 40px;
	height: 40px;
	color: #666666;
	border-bottom: 1px solid #E9ECF7;
}

.d_txt span {
	color: #346CF2;
}

.d_b_b {
	border-bottom: 1px solid #E9ECF7;
}


.text-c {
	border: 1px solid #9659f7;
	background: #eee5fe;
	padding: 0 2px;
	color: #9659f7;
	border-radius: 3px;
	/* font-size: 8px; */
}


.mapbtnbox {
	height: 26px;
	/* padding-top: 14px; */
	padding: 7px 0;
	line-height: 26px;
	font-size: 14px;
	color: #262626;
}

.map_detail {
	overflow: hidden;
	margin-top: 7px;
	background: rgba(255, 255, 255, .85);
}

.map_detail_ul {
	border-top: 1px solid #E9ECF7;
}

.map_detail_ul span {
	font-size: 12px;
	color: #999;
}

.map_detail_ul li {
	padding: 4px 12px;
	border-bottom: 1px solid #E9ECF7;
}

.map_detail_ul li p {
	margin: 4px 0;
}

.detail_val {
	color: #666666;
	font-size: 13px;
}

.detail_val span {
	color: #FF3131;
	font-size: 13px;
}

.detail_tit {
	color: #262626;
	font-size: 13px;
}

.detail_label {
	color: #999;
	font-size: 12px;
}

.detail_valT {
	color: #262626;
	font-size: 14px;
}

.map_detail_num {
	padding: 6px 12px;
	font-size: 12px;
}

.map_detail_num_t span {
	font-size: 18px;
	font-weight: bold;
}

.map_detail_num_r {
	color: #999999;
}

.map_detail_num_r i {
	font-size: 20px;
	margin-left: 4px;
	width: 14px;
	text-align: center;
}

.map_detail_scoll {
	height: 0;
	transition: all .3s linear 0s;
	overflow-y: auto;
}

.map_detail_scoll.active {
	height: 232px;
	transition: all .3s linear 0s;
}

.mu_btn {
	background-color: rgb(52, 108, 242);
	font-size: 14px;
	height: 50px;
	line-height: 50px;
	color: #fff;
}

.commonbtn {
	font-size: 14px;
	color: #666666;
}

.down_box {
	margin: 30px 10px 25px 10px;
}

.down_box_s {
	width: 170px;
	margin: 30px auto;
}

.star_box {
	background: rgba(255, 255, 255, .85);
	margin-top: 7px;
	border-radius: 7px;
}

.star_tit {
	padding: 0 12px;
	font-size: 14px;
	line-height: 40px;
	border-bottom: 1px solid #E9ECF7;
}

.star_list {
	font-size: 36px;
	text-align: center;
	padding: 14px 0;
}

.star_list i {
	margin: 0 13px;
}

.star_list .bright {
	color: #FDA54A;
}

.star_list .dark {
	color: #A4ACD9;
}

/* 我的申请 */
.aplbox {
	background-color: #F1F1F1;
}

.aplavat {
	position: absolute;
	right: 18px;
	top: 1px;
	text-align: center;
	font-size: 12px;
	color: #999999;
}

.mapdownbox .aplavatimg {
	width: 30px;
	height: 30px;
	display: inline-block;
	margin: 0;
}

.mapdown-down-apl {
	border-radius: 0px 0px 7px 7px;
	overflow: hidden;
	margin-top: 7px;
	background-color: #fff;
}

.aplbtn_box {
	margin-top: 13px;
}

.pribtn {
	height: 50px;
	font-size: 18px;
	background-color: #346CF2;
}

.cancelbtn {
	height: 50px;
	font-size: 18px;
	background-color: #FE7925;
}

.u-m-r-8 {
	margin-right: 8px;
}

.ui-dialog-cnt-m {
	border-radius: 6px;
	width: 320px;
	-webkit-background-clip: padding-box;
	pointer-events: auto;
	background-color: #fff;
	position: relative;
	-webkit-box-sizing: border-box;
	color: #000;
}

.popup_tit {
	font-size: 18px;
	text-align: center;
	padding: 20px 0 30px 0;
}

.popup_btn_yes {
	width: 120px;
	height: 40px;
	font-size: 18px;
	background-color: #346CF2;
	color: #fff;
	border-radius: 4px;
}

.popup_btn_no {
	width: 120px;
	height: 40px;
	font-size: 18px;
	background-color: #E9ECF7;
	color: #666666;
	border-radius: 4px;
}

.popup_btm_notr {
	width: 80px;
	height: 30px;
	font-size: 14px;
	background-color: #E9ECF7;
	color: #666666;
	border-radius: 4px;
}

.popup_close {
	width: 120px;
	height: 40px;
	font-size: 18px;
	background-color: #E9ECF7;
	color: #666666;
	border-radius: 4px;
}

.popup_btn_box {
	padding-bottom: 14px;
	width: 264px;
	margin: 0 auto;
}

.popup_btn_boxr {
	padding-bottom: 14px;
}

.popup_btn_nor {
	width: 90px;
	height: 30px;
	font-size: 13px;
	background-color: #E9ECF7;
	color: #666666;
	border-radius: 4px;
}

.popup_btn_color {
	color: #fff;
	background-color: #346CF2;
}


.mileage_tit {
	margin: 0 10px;
	height: 50px;
	line-height: 50px;
	text-align: center;
	font-size: 18px;
	border-bottom: 1px solid #E9ECF7;
}

.mileage_tips {
	color: #FF3131;
	font-size: 12px;
	line-height: 40px;
	text-align: center;
}

.ui-list-single h5 {
	-webkit-box-flex: 1;
}

.mileage_input {
	border: none;
	text-align: right;
	color: #346CF2;
}

.mileage_tit_s {
	font-size: 14px;
	color: #999999;
	margin-left: 12px;
	line-height: 40px;
}

.u-m-b-10 {
	margin-bottom: 10px;
}

.u-m-r-15 {
	margin-right: 15px;
}

/* 司机端 订单详情 */
.drmp_txt {
	/* line-height: 20px; */
	color: #262626;
	margin-top: 10px;
}

.drmp_txt_box {
	border-top: 1px solid #E9ECF7;
	border-bottom: 1px solid #E9ECF7;
	padding: 6px 0 12px 0;
}

.drmp_btn_bg {
	padding: 10px;
	background: rgba(255, 255, 255, );
	margin-top: 10px;
}

.drmp-down {
	border-radius: 7px;
}

.drmp_btn_color {
	background-color: #FF7031;
}

.drmp_dia_txt {
	text-align: center;
	font-size: 14px;
	margin-bottom: 30px;
}

.dialog_flex {
	padding: 0 15px;
	display: flex;
	justify-content: space-between;
	font-size: 14px;
	line-height: 32px;
	margin-bottom: 20px;
}

.dialog_input {
	height: 30px;
	border: 1px solid #C3C3C3;
	border-radius: 2px;
	padding: 0 6px;
	width: 150px;
}

/* element ui */
.uploadhover {
	opacity: 1 !important;
}

.uploadhover span {
	display: inline-block !important;
}

.drmp_file_box {}

.drmp_file_box .el-upload--picture-card {
	width: 80px;
	height: 80px;
	line-height: 90px;
}

.drmp_file_box .el-upload-list--picture-card .el-upload-list__item {
	width: 80px;
	height: 80px;
	margin: 0;
}

.drmp_file_box.haveImg .el-upload {
	display: none;
}

.drmp_file_box.haveImgOne .el-upload {
	display: none;
}

.font_bold {
	font-weight: bold;
}

.startImg {
	width: 80px;
	height: 80px;
	background-color: #ccc;
	border-radius: 6px;
	margin-bottom: 4px;
}

.startGen {
	position: absolute;
	top: 30px;
	left: 10px;
	display: flex;
}

.startGen i {
	margin-left: 20px;
	font-size: 20px;
	color: #fff;

}

.startEndBox {
	width: 74%;
	margin: 0 auto;
	padding-bottom: 10px;
}

.startBox {
	padding: 0 15px;
	margin-bottom: 30px;
}

.toum {
	background-color: transparent;
}

.dhBtn {
	padding: 4px 10px;
	border: 1px solid #199DFF;
	color: #199DFF;
	border-radius: 3px;
	margin-top: 14px;
}

/* 导航弹窗 */
.drawer_tit {
	font-size: 14px;
	font-weight: bold;
	color: #000;
	z-index: 999;
}

.drawerUl {
	list-style: none;
	margin: 0;
	margin-top: 14px;
	font-size: 14px;
}

.drawerUl li {
	background-color: #EFEFEF;
	padding: 8px 16px;
	border-radius: 6px;
	line-height: 20px;
	margin-bottom: 12px;
}

.drawerUl li .gobtn {
	background-color: #fff;
	border-radius: 25px;
	padding: 4px 14px;
}

.fyli {
	padding: 11px 12px 11px 12px;
	width: calc(100% - 24px);
}

.el-dialog__wrapper {
	z-index: 99999 !important;
}

.el-upload-list--picture-card .el-upload-list__item-actions:hover {
	opacity: 1;
}

.el-upload-list--picture-card .el-upload-list__item-actions {
	opacity: 1;
}

.el-upload-list--picture-card .el-upload-list__item-actions span {
	display: inline-block;
}

.el-upload-list--picture-card .el-upload-list__item-actions span {
	display: inline-block;
}

.el-upload-list__item {
	transition: none;
}

.overHeight {
	max-height: 66vh;
	overflow-y: auto;
}

.loder {
	display: flex;
}

.loder div {
	width: 16px;
	height: 16px;
	margin: 0 5px;
	background: #fff;
	border-radius: 50%;
	animation: loder 0.6s infinite alternate;
}

.loder div:nth-child(2) {
	animation-delay: 0.2s
}

.loder div:nth-child(3) {
	animation-delay: 0.4s
}

@keyframes loder {
	to {
		opacity: 0.1;
		transform: translate3d(0, 16px, 0);
	}
}

/* 弹窗 */
#msg_c {
	position: fixed;
	top: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

#successMsg {
	background: #585858e3;
	box-shadow: 0px 0px 16px 0px rgba(148, 148, 148, 0.36);
	border-radius: 4px;
	padding: 10px;
	font-size: 12px;
	color: #FFFFFF;
	z-index: 99;
	display: flex;
	justify-content: center;
	align-items: center;
	max-width: 50%;
	word-break: break-all;
	flex-direction: column;
	min-width: 35%;
}

.tiprInner {
	width: 100%;
	text-align: left;
}

.tiprData {
	width: 100%;
	text-align: center;
	font-size:14px ;
	margin-top: 5px;
}

.rules{
	padding: 3px 20px;
	border:1px solid #000;
	border-radius: 5px;
	margin: 5px 0;
}