<template>
	<view class="apply">
		<u-navbar :title="useCarTypeName" :autoBack="true" :placeholder="true" @leftClick="leftClick">
			<!-- <view slot="left"></view> -->
		</u-navbar>
		<!-- 		<view class="nav_box">
			<u-tabs :list="navList" :scrollable="false" lineWidth="22" lineHeight="2" lineColor="#346CF2"
				:activeStyle="{color: '#262626'}" :inactiveStyle="{color: '#999999'}" @change="changeTab"
				itemStyle="width:33%; height: 40px; padding:0">
			</u-tabs>
		</view> -->
		<view class="tab_top u-flex u-row-between">
			<view :class="{ 'tab_top_btn': true, 'active': tabtype == 1 }" @click="changetype(1)">
				待审批
			</view>
			<view :class="{ 'tab_top_btn': true, 'active': tabtype == 2 }" @click="changetype(2)">
				已通过
			</view>
			<view :class="{ 'tab_top_btn': true, 'active': tabtype == 3 }" @click="changetype(3)">
				已驳回
			</view>
		</view>
		<tab-page ref="tabPage" :tabtype="tabtype" :useCarType="useCarType" @sendBtn="changetype(1)"></tab-page>
	</view>
</template>

<script>
import tabPage from "./tabpage/tabpage"
export default {
	components: {
		tabPage,
	},
	data() {
		return {
			tabtype: 1,
			useCarType: 1,
			navList: [{
				name: '自有车',
				useCarType: 1
				// badge: {
				// 	isDot: true
				// }
			},
			// {
			// 	name: '网约车',
			// 	useCarType:2
			// },
			{
				name: '转租赁',
				useCarType: 3
			},
			{
				name: '私车公用',
				useCarType: 4
			}
			],
			useCarTypeName: ''
		};
	},
	onHide() {
		this.$nextTick(() => {
			this.$refs.tabPage.getOperate()
		})
	},
	onLoad(option) {
		this.useCarType = option.typer
		this.useCarTypeName = option.typer == 1 ? '自有车审批' : option.typer == 2 ? '网约车审批' :option.typer == 3 ? '转租赁审批' : option.typer == 4 ? '私车公用审批' :
			'我的审批'
		console.log(option, 'option');
	},
	methods: {
		leftClick() {
			// #ifdef H5
			const pages = getCurrentPages()
			let page = pages[pages.length - 1].$page.fullPath; //完整路由地址
			let typer = page.split('type=')[1] //携带的type参数

			if (typer) {
				uni.reLaunch({
					url: '/pages/wayPassenger/index/index'
				})
			}
			// #endif
		},
		changetype(val) {
			this.tabtype = val
		},
		changeTab(item) {
			this.useCarType = item.useCarType
		},
		loadPage() {
			this.$refs.tabPage.queryList()
		},
	}
}
</script>

<style lang="scss" scoped>
.apply {

	// padding-bottom: 200rpx;
	.nav_box {
		background-color: #fff;
	}

	.tab_top {
		width: 520rpx;
		margin: 23rpx auto;

		.tab_top_btn {
			width: 133rpx;
			height: 53rpx;
			line-height: 53rpx;
			text-align: center;
			border-radius: 30rpx;
			font-size: 28rpx;
		}

		.tab_top_btn.active {
			background-color: #346CF2;
			color: #fff;
		}
	}
}
</style>
