<template>
	<view class="tabpage">
		<view class="filterBox">
			<u-search class="search" placeholder="搜索订单" :showAction="false" v-model="keyword" @search="searchFun">
			</u-search>
		</view>
		<scroll-view class="scroll-content" scroll-y="true" refresher-enabled="true" :refresher-triggered="triggered"
			:refresher-threshold="100" refresher-background="#F9FAFE" @scrolltolower="onReflowe"
			@refresherrefresh="onRefresh">
			<view class="app_list" v-for="(item,index) in dataList" :key="index" @click="goDetail(item.applyId)"
				style="position: relative;">
				<block v-if='applyType==1||applyType==2'>
					<view class="jiaoimg">
						<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/qi.png"
							v-if="item.travelType==1 && applyType!=2">
						</u-image>
						<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/zu.png"
							v-if="item.travelType==2 && applyType!=2">
						</u-image>
						<u-image :width="30" :height="30" src="https://zqcx.di-digo.com/app/image/si.png"
							v-if="item.travelType==1 && applyType==2">
						</u-image>
					</view>
					

					<view class="u-flex u-row-between">
						<view class="u-flex">
							<!-- <u-image class="tab_icon" :width="16" :height="14"  src="https://zqcx.di-digo.com/app/image/wdsq_qcl.png"></u-image> -->
							<span class="text-dh">申请单号：{{item.applyCode}}</span>
						</view>
						<view class="text-rt">
							{{tabtype==1?"待审批":tabtype==2?"已通过":tabtype==3&&item.compOrderState==220?"审批驳回":tabtype==3&&item.compOrderState==230?"调度驳回":"已取消"}}
						</view>
					</view>

					<view class="app_t_line u-flex">
						<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon.png">
						</u-image>

						<!-- 	<span>{{item.reserveStartTime | date('mm月dd日 hh:MM')}} -
							{{item.reserveEndTime | date('mm月dd日 hh:MM')}}</span> -->

						<span class="tab_icon">{{item.startTimer}} - {{item.endTimer}}</span>
					</view>

					<view class="app_t_line u-flex">
						<view class="spot"></view>
						<span>{{item.fromAddrName}}</span>
					</view>

					<view class="app_t_line u-flex" v-if="item.throughAddrInfo && item.throughAddrInfo.length > 0">
						<view class="spot" style="background-color: chartreuse;"></view>
						<span style="flex: 1;">{{item.throughAddrInfoName}}</span>
					</view>

					<view class="app_t_line u-flex">
						<view class="spot" style="background: #ff9800;"></view>
						<span>{{item.toAddrName}}</span>
					</view>

					<view class="app_t_line u-flex">
						<u-image :width="14" :height="14"
							src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png">
						</u-image>
						<span class="tab_icon">乘车人数：{{item.psgNums}}</span>
					</view>


					<view class="app_t_line u-flex">
						<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png">
						</u-image>
						<span class="tab_icon">{{item.wantCarTypeFullName}}</span>
					</view>

					<view v-if="item.compOrderState==200&&applyType==2" class="applistbtn"
						@click.stop="openpopup(item)">
						去用车
					</view>

					<!-- 取消订单 -->
					<view v-if="item.compOrderState==10" class="applistbtn" @click.stop="cancelOrder(item)">
						取消订单
					</view>

				</block>

				<block v-if="applyType==3">
					<networkCar :item='item' :index="index" :tipsList='tipsList' @showType="showType"
						@tipsSelect='tipsSelect' @goClick='goClick' :tabtype='tabtype'></networkCar>
				</block>
			</view>
			<view style="text-align: center;" v-if="total==dataList.length &&dataList.length!=0">———— 到底了 ————</view>
			<u-empty v-if="dataList.length==0" mode="order" text='暂无申请'
				icon="http://cdn.uviewui.com/uview/empty/order.png">
			</u-empty>
		</scroll-view>

		<u-popup :show="tiraShow" mode="center" :round="10" @close="()=>{tiraShow=false}" :customStyle="styleObjr">
			<orderTracking :list="tiraList" v-if="tiraShow">
				<template #conten="{row}">
					{{`${row.operatorName}${row.operatorMobile}`}}
				</template>
			</orderTracking>
		</u-popup>
		<u-popup :show="networkShow" mode="center" :round="3" @close="()=>{networkShow=false}" :customStyle="styleObjr">
			<vehicleReminder :data='networkData.appApplyRegulationVo' @catShow='carClick' :shows='false'>
			</vehicleReminder>
		</u-popup>
	</view>
</template>

<script>
	import {
		applycomporderlist,
		cancelorder
	} from '@/config/api.js';
	import {
		cancelnetorder
	} from '@/config/consoler.js';
	import networkCar from '../../componentr/networkCar.vue'
	import orderTracking from '../../componentr/orderTracking.vue'
	import vehicleReminder from '../../componentr/vehicleReminder.vue'
	export default {
		components: {
			networkCar,
			orderTracking,
			vehicleReminder
		},
		data() {
			return {
				triggered: false,
				keyword: '',
				dataList: [],
				status: 'loadmore',
				pageNum: 1,
				pageSize: 20,
				hasNext: true,
				show: false,
				cancelShow: false,
				orderId: '',
				total: 0,
				tipsList: [{
						text: '撤回重编',
						icon: 'reload'
					},
					{
						text: '取消行程',
						icon: 'close'
					},
					{
						text: '联系审批',
						icon: 'server-fill'
					},
					{
						text: '订单跟踪',
						icon: 'list-dot'
					},
				],
				tiraShow: false,
				tiraList: [],
				styleObjr: {
					width: '85%'
				},
				networkShow: false,
				networkData: {}
			}
		},
		props: ["tabtype", 'applyType'],
		watch: {
			tabtype(newVal, oldVal) {
				this.dataList = []
				this.queryList()
			},
		},
		mounted() {
			this.queryList()
		},
		methods: {
			carClick(val) {
				this.networkShow = false
				if (val) {
					uni.$u.route('/pager/networkCar/networkCar', {
						id: this.networkData.applyId,
					})
				}

			},
			goClick(item) {
				if (item.type) {
					this.networkShow = true
					this.networkData = item
					// uni.$u.route('/pager/networkCar/networkCar', {
					// 	id: item.applyId,
					// })
				} else {
					uni.$u.route('/pager/networkCarDetais/networkCarDetais', {
						id: item.applyId,
						type: 1,
					})
				}
			},
			tipsSelect(item, val) {
				if (item.text == '撤回重编') {
					if (!val.isUpdate) return uni.$u.toast('当前状态无法撤回重编')
					this.putWithdraw(val)
				} else if (item.text == '取消行程') {
					this.cancelCompany(val)
				} else if (item.text == '联系审批') {
					if (!val.mobile) return uni.$u.toast('暂无联系方式')
					uni.makePhoneCall({
						phoneNumber: val.mobile //仅为示例
					});
				} else if (item.text == '订单跟踪') {
					if (!val.orderEventHis) return uni.$u.toast('暂无订单跟踪')
					this.tiraShow = true

					val.orderEventHis.forEach(v => {
						v.titles = `${v.evtDetContent} - ${v.operatorTimes}`
					})
					this.tiraList = val.orderEventHis
				}
			},
			putWithdraw(item) {
				let obj = {
					type: true,
					applyId: item.applyId
				}
				uni.$u.route({
					url: '/pages/wayPassenger/index/customCar/customCar',
					params: obj
				})
			},
			cancelCompany(item) {
				cancelnetorder({
					id: item.applyId
				}).then(res => {
					uni.$u.toast(res)
					setTimeout(() => {
						this.onRefresh()
					}, 400)
				})
			},
			showType(item) {
				this.dataList.forEach(v => {
					this.$set(v, 'show', false)
					if (v.applyCode == item.applyCode) {
						this.$set(v, 'show', true)
						setTimeout(() => {
							this.$set(v, 'show', false)
						}, 2500)
					}
				})
			},
			onRefresh(e) {
				this.pageNum = 1
				this.triggered = true
				this.dataList = []
				this.queryList()
			},
			onReflowe(e) {
				if (this.total == this.dataList.length) return
				this.pageNum++
				this.queryList()
			},
			searchFun(value) {
				this.dataList = []
				this.queryList()
			},
			queryList() {
				applycomporderlist({
					params: {
						orderStateType: this.tabtype,
						pageNum: this.pageNum,
						pageSize: this.pageSize,
						content: this.keyword,
						applyType: this.applyType
					}
				}).then((data) => {
					data.pageList.forEach(v => {
						if (v.throughAddrInfo) {
							v.throughAddrInfo = JSON.parse(v.throughAddrInfo)
							v.throughAddrInfoName = v.throughAddrInfo.map(v => v.siteAddrName)
								.join(
									' → ')
						}
						v.startTimer = uni.$u.timeFormat(new Date(v.reserveStartTime).getTime(),
							'mm月dd日 hh:MM')
						v.endTimer = uni.$u.timeFormat(new Date(v.reserveEndTime).getTime(),
							'mm月dd日 hh:MM')
						v.show = false
					})
					this.total = data.total
					if (this.pageNum == 1) {
						this.dataList = data.pageList
					} else {
						this.dataList.push(...data.pageList)
					}
					this.hasNext = data.hasNext
					this.triggered = false
				})
			},
			close() {
				this.show = false
			},
			cancelclose() {
				this.cancelShow = false
			},
			openpopup(item) {
				// this.show = true
				// this.orderId = item.id //订单id
				if (this.applyType == 2) {
					uni.$u.route('/pages/map/mapSi', {
						url: "/hybrid/html/privateCar.html",
						orderType: this.applyType,
						id: item.applyId,
					});
				}
			},
			useCar(item) {
				this.show = false
				uni.$u.route('/pages/map/map', {
					url: "/hybrid/html/application.html",
					title: '开始用车',
					id: this.orderId
				});
			},
			// 跳转详情
			goDetail(id) {
				if (this.applyType == 1 || this.applyType == 2) {
					uni.$u.route('/pages/wayPassenger/workBench/apply/detail/detail', {
						id: id
					});
				}
			},
			// 取消订单弹出
			cancelOrder(item) {
				let that = this
				uni.showModal({
					title: '提示',
					content: '是否取消订单？',
					success: function(res) {
						if (res.confirm) {
							cancelorder({
								id: item.applyId
							}).then((data) => {
								uni.$u.toast('操作成功')
								setTimeout(() => {
									that.queryList()
								}, 1000)
							})
						} else if (res.cancel) {}
					}
				})

			},
		}
	}
</script>

<style lang="scss" scoped>
	.filterBox {
		padding: 0 32rpx;
	}

	.search {
		/deep/.u-search__content {
			background-color: #E9ECF7 !important;
		}

		/deep/.u-search__content__input {
			background-color: #E9ECF7 !important;
		}
	}



	.scroll-content {
		margin-top: 10rpx;
		height: calc(100vh - 260rpx);
	}

	.app_list {
		background-color: #fff;
		margin: 14rpx 11rpx;
		border-radius: 11rpx;
		padding: 30rpx;
		font-size: 28rpx;
		position: relative;
	}

	.tab_icon {
		margin-left: 16rpx;
	}

	.jiaoimg {
		position: absolute;
		left: 0;
		top: 0;
	}

	.regulation {
		background-color: #7728F5;
		padding: 16rpx;
		color: #fff;
		border-top-left-radius: 10rpx;
		border-bottom-right-radius: 10rpx;
	}

	.applistbtn {
		background-color: #346CF2;
		padding: 4rpx 18rpx;
		font-size: 28rpx;
		position: absolute;
		color: #fff;
		border-radius: 8rpx;
		right: 27rpx;
		bottom: 27rpx;
	}

	.icon-right {
		position: absolute;
		top: 50%;
		right: 27rpx;
		margin-top: -16rpx;
	}



	.text-dh {
		font-size: 24rpx;
		color: #999999;
	}

	.text-dh span {
		color: #000;
		margin-left: 10rpx;
	}

	.text-rt {
		color: #346CF2;
	}

	.app_t_line {
		margin-top: 14rpx;
	}

	.spot {
		width: 16rpx;
		height: 16rpx;
		background-color: #239EFC;
		border-radius: 50%;
		margin: 0 24rpx 0 8rpx;
	}

	.divider {
		margin: 16rpx 0;
		height: 1rpx;
		background: #cccc;
	}
</style>