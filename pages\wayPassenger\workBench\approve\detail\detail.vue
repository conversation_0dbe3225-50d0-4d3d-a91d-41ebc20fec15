<template>
	<view class="detail">
		<u-navbar title="详情" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="app_list">
			<view class="u-flex u-row-between detail_tit">
				<view class="u-flex">
					<u-image class="tab_icon" :width="16" :height="14"
						src="https://zqcx.di-digo.com/app/image/wdsq_qcl.png"></u-image>
					<span class="text-dh">申请单号：{{detail.applyCode}}</span>
				</view>
			</view>

			<view class="app_t_line u-flex">
				<u-image class="tab_icon" :width="14" :height="14"
					src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png"></u-image>
				<span>{{detail.psgName}}</span>
			</view>

			<view class="app_t_line u-flex">
				<u-image class="tab_icon" :width="14" :height="14"
					src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png"></u-image>
				<span>{{detail.regulationName}}</span>
			</view>

			<view class="app_t_line u-flex">
				<u-image class="tab_icon" :width="14" :height="14"
					src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png"></u-image>
				<span>{{detail.costCenterName}}</span>
			</view>

			<u-line color="#EEEEEE" margin="20rpx 0"></u-line>

			<view class="app_t_line u-flex">
				<u-image class="tab_icon" :width="14" :height="14"
					src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon.png"></u-image>
				<span>{{new Date(detail.reserveStartTime).getTime()| date('mm月dd日 hh:MM')}} -
					{{new Date(detail.reserveEndTime).getTime()| date('mm月dd日 hh:MM')}}</span>
			</view>

			<view class="app_t_line u-flex">
				<view class="spot"></view>
				<span>{{detail.fromAddrName}}</span>
			</view>

			<view class="app_t_line u-flex" v-for="(item,index) in detail.throughAddrInfo" :key="index">
				<view class="spot" style="background-color:#5ac725"></view>
				<span>{{item.siteAddrName}}</span>
			</view>

			<view class="app_t_line u-flex">
				<view class="spot" style="background-color:#404040"></view>
				<span>{{detail.toAddrName}}</span>
			</view>

			<u-line color="#EEEEEE" margin="20rpx 0"></u-line>

			<view class="app_t_line u-flex" v-if="detail.processName">
				<u-image class="tab_icon" :width="14" :height="14"
					src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png"></u-image>
				<span>审批人：{{detail.processName}}</span>
			</view>

			<view class="app_t_line u-flex" v-if="detail.description">
				<u-image class="tab_icon" :width="14" :height="14"
					src="https://zqcx.di-digo.com/app/image/wdsq_icon4.png"></u-image>
				<span>用车备注：{{detail.description}}</span>
			</view>

			<view class="app_t_line u-flex">
				<u-image class="tab_icon" :width="14" :height="14"
					src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png"></u-image>
				<span>乘车人数：{{detail.psgNums}}</span>
			</view>

			<!-- <view class="app_t_line u-flex">
				<u-image class="tab_icon" :width="14" :height="14"  src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png"></u-image>
				<span>是否匹配司机：{{detail.isAllotDriver==1?'是':'否'}}</span>
			</view> -->

			<view class="app_t_line u-flex">
				<u-image class="tab_icon" :width="14" :height="14"
					src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png"></u-image>
				<span>所选车型：{{detail.wantCarTypeFullName}}</span>
			</view>
		</view>


		<view class="app_list" v-if="detail.orderEventHis&&detail.orderEventHis.length>0">
			<view class="res_jd_tit">
				订单跟踪：
			</view>
			<view class="res_jd">
				<u-steps :current="detail.orderEventHis.length-1" direction="column">
					<u-steps-item v-for="(item,index) in detail.orderEventHis" :key="index"
						:title="item.operatorName+' '+item.evtDetContent"
						:desc=" new Date(item.operatorTime).getTime()| date('yyyy年mm月dd日 hh:MM:ss')">
						<view slot="desc">
							<view class="fontr">{{new Date(item.operatorTime).getTime()| date('yyyy年mm月dd日 hh:MM:ss')}}
							</view>
							<view class="fontr" style="margin-top: 10rpx;">
								{{item.evtDetCode==25?'驳回原因：':""}}{{item.evtDescription ?item.evtDescription:''}}
							</view>
						</view>
					</u-steps-item>
				</u-steps>
			</view>
		</view>

		<view class="footer_box btn_box u-flex u-row-around">
			<block v-if="optionObjr.type==1">
				<view class="btnmini">
					<u-button type="success" text="通过" @click="adopt()"
						v-if="detail.approvalButton.isApprovalButton==1">
					</u-button>
				</view>
				<view class="btnmini">
					<u-button color="#ccc" text="驳回" @click="reject()"
						v-if="detail.approvalButton.isApprovalRejectedButton==1 ">
					</u-button>
				</view>
			</block>
			<block v-if="optionObjr.type==3">
				<view class="btnmini">
					<u-button type="success" text="通过" @click="adopt()"
						v-if="detail.approvalButton.isLeaseApprovalButton==1">
					</u-button>
				</view>
				<view class="btnmini">
					<u-button color="#ccc" text="驳回" @click="reject()"
						v-if="detail.approvalButton.isLeaseApprovalRejectedButton==1 ">
					</u-button>
				</view>
			</block>
			<block v-if="optionObjr.type==4">
				<view class="btnmini">
					<u-button type="success" text="通过" @click="adopt()"
						v-if="detail.approvalButton.isAppPrivateButton==1">
					</u-button>
				</view>
				<view class="btnmini">
					<u-button color="#ccc" text="驳回" @click="reject()"
						v-if="detail.approvalButton.isAppPrivateRejectedButton==1">
					</u-button>
				</view>
			</block>
			<!-- 	<u-button class="btnmini" type="warning" text="转租赁审批" @click="zzlsp(detail)"
				v-if="detail.approvalButton.isLeaseApprovalButton==1"></u-button> -->
		</view>

		<!-- 组件 -->
		<u-popup class="popup_bg" :round="5" :show="show" mode="center" @close="close" :customStyle="styleObjr">
			<view class="popup_tit">
				确定通过申请
			</view>
			<view class="popup_txt">
				是否确定通过用车申请
			</view>
			<view class="u-flex u-row-around popup_btn_box">
				<view class="popup_btn">
					<u-button color="#346CF2" type="primary" text="确定" @click="newAdoptFun()"></u-button>
				</view>
				<view class="popup_btn two">
					<u-button color="#E9ECF7" type="primary" text="取消" @click="close()"></u-button>
				</view>
			</view>
		</u-popup>

		<!-- 驳回弹窗组件 -->
		<u-popup class="popup_bg" :round="5" :show="rejectshow" mode="center" @close="rejectclose"
			:customStyle="styleObjr">
			<rejectCom v-if="rejectshow" :rejectObj="detail" :rejectName="rejectName" @adoptFun="adoptFunrs">
			</rejectCom>
		</u-popup>

		<!-- 下拉组件 -->
		<u-picker :show="selectshow" :columns="modearray" keyName="name" @cancel="selectclose" @confirm="qdselect">
		</u-picker>

		<!-- 000000000000000000000000 -->
		<!-- 转租赁审批弹窗 -->
		<u-popup class="popup_bg" :round="5" :show="zzlspshow" mode="center" @close="zzlspclose">
			<view class="popup_tit">
				转租赁审批
			</view>
			<view class="popup_center">
				<view class="popup_item u-flex">
					<view class="popup_item_tit">
						是否同意:
					</view>
					<u-radio-group @change="agreeChange" v-model="appCheckCompOrderVo.isAgree" placement="row">
						<u-radio :customStyle="{marginRight: '16px'}" label="是" name="1"></u-radio>
						<u-radio label="否" name="0"></u-radio>
					</u-radio-group>
				</view>

				<view class="popup_item u-flex" v-show="agreeyShow">
					<view class="popup_item_tit">
						下级审批人:
					</view>
					<view @click="selectCheck()">
						{{checkUser}}
					</view>
				</view>

				<view class="popup_item u-flex" v-show="!agreeyShow">
					<view class="popup_item_tit">
						驳回方式:
					</view>
					<u-radio-group v-model="appCheckCompOrderVo.rejectType" placement="row">
						<u-radio :customStyle="{marginRight: '16px'}" label="逐级驳回" name="1"></u-radio>
						<u-radio label="申请人" name="2"></u-radio>
					</u-radio-group>
				</view>

				<view class="popup_item">
					<view class="popup_item_tit popup_item">
						审批意见:
					</view>
					<u--textarea placeholder="请输入审批意见" v-model="appCheckCompOrderVo.checkRemark" height="107">
					</u--textarea>
				</view>

			</view>
			<view class="u-flex u-row-between popup_btn_box">
				<u-button class="popup_btn" color="#346CF2" type="primary" text="确定" @click="zzlspFun()"></u-button>
				<u-button class="popup_btn two" color="#E9ECF7" type="primary" text="取消" @click="zzlspclose()">
				</u-button>
			</view>
		</u-popup>

		<!-- 下级审批人列表 -->
		<u-picker :show="checkUsershow" :columns="checkUserList" keyName="name" @cancel="checkUsercancel"
			@confirm="checkUserconfirm"></u-picker>

		<!-- 审批人下拉框 -->
		<u-picker :show="approvalShow" :columns="approvalList" keyName="name" @cancel="approvalShow=false"
			@confirm="approvalBtn"></u-picker>
	</view>
</template>

<script>
	import {
		checkcomporderinfo,
		checkcomporder,
		checktoleaseor,
		checkleaseorderinfo,
		checkpriceteorder
	} from '@/config/api.js';
	import rejectCom from '../../componentr/rejectCom.vue'
	export default {
		data() {
			return {
				id: '',
				detail: {},
				rejectshow: false,
				rejectName: '',
				show: false,
				modearray: [
					[{
							name: '逐级驳回',
							val: 1
						},
						{
							name: '申请人',
							val: 2
						}
					],
				],
				checkCompOrderVo: {
					isAgree: '',
					checkRemark: '',
					nextCheckUserId: '',
					rejectType: ''
				},
				checkvalue: '请选择',
				selectshow: false,
				// 转租赁审批相关参数000000000000000000000000
				choiceOrder: {},
				zzlspshow: false,
				agreeyShow: true,
				appCheckCompOrderVo: {
					isAgree: '1',
					checkRemark: '',
					nextCheckUserId: '',
					rejectType: '1'
				},
				checkUser: '请选择',
				checkUsershow: false,
				checkUserList: [],
				checkUserId: '',
				approvalList: [],
				approvalShow: false,
				styleObjr: {
					width: '85%'
				},
				optionObjr: {}
			};
		},
		components: {
			rejectCom,
		},
		onLoad(option) {
			this.optionObjr = option
			if (option.travelId) {
				this.id = option.travelId
				this.getDetailr()
			} else {
				this.id = option.applyId
				this.getDetail()
			}
		},
		methods: {
			adoptFunrs(objr) {
				if (!objr) return this.rejectshow = false
				this.checkCompOrderVo = {
					isAgree: objr.isAgree,
					checkRemark: objr.checkRemark,
					nextCheckUserId: null,
					rejectType: objr.rejectType
				}
				this.adoptFun()
			},
			getDetail() {
				checkcomporderinfo({
					params: {
						id: this.id,
					}
				}).then((data) => {
					data.approvalButton = data.approvalButton ? data.approvalButton : {}
					if (data.throughAddrInfo) {
						data.throughAddrInfo = JSON.parse(data.throughAddrInfo)
					}
					this.$set(data, 'useCarType', this.optionObjr.type)
					this.detail = data
				})
			},

			getDetailr() {
				checkleaseorderinfo({
					params: {
						id: this.id,
					}
				}).then((data) => {
					data.approvalButton = data.approvalButton ? data.approvalButton : {}
					if (data.throughAddrInfo) {
						data.throughAddrInfo = JSON.parse(data.throughAddrInfo)
					}
					this.$set(data, 'useCarType', this.optionObjr.type)
					this.detail = data
				})
			},
			close() {
				this.show = false
			},
			rejectclose() {
				this.rejectshow = false
				this.selectshow = false
			},
			selectclose() {
				this.selectshow = false
			},
			adopt() {
				this.checkCompOrderVo.isAgree = 1
				this.show = true
			},
			reject() {
				this.checkCompOrderVo.checkRemark = null
				this.checkCompOrderVo.isAgree = 0
				this.rejectName = '驳回'
				this.rejectshow = true
			},
			qdselect(e) {
				this.checkvalue = e.value[0].name;
				this.checkCompOrderVo.rejectType = e.value[0].val;
				this.selectshow = false
			},
			approvalBtn(e) {
				this.checkCompOrderVo.nextCheckUserId = e.value[0].userId
				this.adoptFun()
				this.$nextTick(() => {
					this.approvalShow = false
				})
			},
			newAdoptFun() {
				if (this.detail.nextCheckUserList && this.detail.nextCheckUserList.length > 0) {
					this.approvalList = [this.detail.nextCheckUserList]
					this.show = false
					this.approvalShow = true
				} else {
					this.adoptFun()
				}
			},
			adoptFun() {
				// console.log(, 'this.optionObjr.type');
				// return
				if (this.optionObjr.type == 1) {
					checkcomporder({
						checkCompOrderVo: this.checkCompOrderVo,
						id: this.detail.applyId,
					}).then((data) => {
						this.show = false
						this.rejectshow = false
						this.checkCompOrderVo = {
							isAgree: '',
							checkRemark: '',
							nextCheckUserId: '',
							rejectType: ''
						}
						uni.$u.toast("操作成功")
						// uni.navigateBack({
						// 	delta: 1
						// })
						uni.redirectTo({
							url: '/pages/wayPassenger/workBench/approve/approve?typer=' + this.optionObjr
								.type
						})
					})
				} else if (this.optionObjr.type == 3) {
					checktoleaseor({
						checkCompOrderVo: this.checkCompOrderVo,
						id: this.detail.travelId,
					}).then((data) => {
						this.show = false
						this.rejectshow = false
						this.checkCompOrderVo = {
							isAgree: '',
							checkRemark: '',
							nextCheckUserId: '',
							rejectType: ''
						}
						uni.$u.toast("操作成功")
						uni.navigateBack({
							delta: 1
						})
						// uni.redirectTo({
						// 	url: '/pages/wayPassenger/workBench/approve/approve'
						// })
					})
				} else if (this.optionObjr.type == 4) {
					checkpriceteorder({
						checkCompOrderVo: this.checkCompOrderVo,
						id: this.detail.applyId,
					}).then((data) => {
						this.show = false
						this.rejectshow = false
						this.checkCompOrderVo = {
							isAgree: '',
							checkRemark: '',
							nextCheckUserId: '',
							rejectType: ''
						}
						uni.$u.toast("操作成功")
						uni.navigateTo({
							url: '/pages/wayPassenger/workBench/approve/approve'
						})
					})
				}
				return


			},
			// 转租赁审批000000000000000000000000
			zzlsp(item) {
				this.choiceOrder = item;
				this.checkUser = '请选择'
				this.appCheckCompOrderVo.nextCheckUserId = ''
				this.checkUserList = item.nextCheckUserList
				this.zzlspshow = true
			},
			zzlspclose() {
				this.zzlspshow = false
			},
			agreeChange(name) {
				if (name == '1') {
					this.agreeyShow = true
				} else {
					this.agreeyShow = false
				}
			},
			zzlspFun() {
				checktoleaseorder({
					appCheckCompOrderVo: this.appCheckCompOrderVo,
					id: this.choiceOrder.travelId,
				}).then((data) => {
					uni.$u.toast('操作成功')
					this.agreeyShow = false
					setTimeout(() => {
						this.queryList()
					}, 1000)
				})
			},
			// 下级审批人
			selectCheck() {
				this.checkUsershow = true
			},
			checkUsercancel() {
				this.checkUsershow = false
			},
			checkUserconfirm(e) {
				this.checkUsershow = false
				this.checkUser = e.name
				this.appCheckCompOrderVo.nextCheckUserId = e.userId
			}
		}
	}
</script>

<style lang="scss" scoped>
	.detail {
		padding-bottom: 100rpx;

		.detail_tit {
			margin-bottom: 34rpx;
		}

		.filterBox {
			padding: 0 32rpx;
			margin-top: 14rpx;

			.search {
				/deep/.u-search__content {
					background-color: #E9ECF7 !important;
				}

				/deep/.u-search__content__input {
					background-color: #E9ECF7 !important;
				}
			}
		}

		.app_list {
			background-color: #fff;
			margin: 14rpx 11rpx;
			border-radius: 11rpx;
			padding: 30rpx;
			font-size: 28rpx;
			position: relative;

			.icon-right {
				position: absolute;
				top: 50%;
				right: 27rpx;
				margin-top: -16rpx;
			}

			.tab_icon {
				margin-right: 16rpx;
			}

			.text-dh {
				font-size: 24rpx;
				color: #999999;
			}

			.text-rt {
				color: #346CF2;

				.text-rt-tag {
					margin-right: 4rpx;

					/deep/.u-tag--mini {
						height: 33rpx;
						line-height: 33rpx;
						padding: 0 4rpx;
					}
				}
			}

			.app_t_line {
				margin-top: 20rpx;
			}

			.dw_box {
				font-size: 24rpx;
			}

			.spot {
				width: 16rpx;
				height: 16rpx;
				background-color: #239EFC;
				border-radius: 50%;
				margin: 0 24rpx 0 8rpx;
			}
		}

		.btn_box {
			background-color: #fff;
			height: 80rpx;
			box-shadow: 0px -1px 43px 0px rgba(131, 131, 131, 0.15);

			.btnmini {
				width: 220rpx;
				height: 66rpx;
			}

			.hui {
				background-color: #999999;
				border-color: #999999;
			}
		}

		.line-dotted {
			border-top: 1px dotted #999999;
			margin: 20rpx 0;
		}

		.res_jd {
			padding: 20rpx 0rpx;

			.u-steps-item--column {
				padding-bottom: 20px;
			}
		}

		.popup_bg {}

		.popupcbox {
			padding: 0 53rpx;
		}

		.select_input {
			height: 60rpx;
			border: 1px solid #CCCCCC;
			border-radius: 8rpx;
			padding-left: 20rpx;
			line-height: 60rpx;
			color: #999;
			margin: 20rpx 0;
		}

		.selectbox {
			padding: 0 53rpx;
			font-size: 28rpx;
		}

		.popup_btn_box {
			padding-bottom: 27rpx;
			margin-top: 30rpx;
		}

		.popup_tit {
			font-size: 36rpx;
			text-align: center;
			padding: 40rpx 0 30rpx 0;
		}

		.popup_txt {
			font-size: 28rpx;
			text-align: center;
			margin-bottom: 40rpx;
		}

		// /deep/.u-popup__content {
		// 	width: 84%;
		// }

		.popup_btn {
			width: 230rpx;
			height: 80rpx;

			/deep/ .u-button__text {
				font-size: 36rpx !important;
			}
		}

		.two {
			color: #666666 !important;
		}

		.popupbox {
			.popupbox_top {
				font-size: 28rpx;
				height: 102rpx;
				padding: 0 32rpx;
				border-bottom: 1px solid #E9ECF7;
				justify-content: space-between;

				.define {
					color: #346CF2;
				}
			}

			.radio_type {
				padding: 24rpx 0;

				.u-radio-group {
					justify-content: space-around;
				}

				.u-radio {
					padding: 18rpx 28rpx;
					border: 1px solid #999999;
					border-radius: 7rpx;
				}

				.u-radio.active {
					border: 1px solid #346CF2;
				}
			}

			.list_one {
				margin: 0 32rpx;
			}

			.list_two {
				margin: 0 32rpx;
			}

			.inputBox {
				padding: 23rpx 0;
				border-top: 1px solid #E9ECF7;

				.s_input_box {
					width: 200rpx;
					margin-left: 10rpx;
					margin-right: 50rpx;
				}

				.s_input {
					padding: 0 9px !important;
				}

				.s_btn_box {
					.del {
						background-color: #999999;
						border-color: #999999;
						// height: 100rpx;
					}

					.yes {
						// height: 100rpx;
					}
				}
			}

			.inputBoxTwo {
				padding: 13rpx 0;
				border-top: 1px solid #E9ECF7;

				.s_input_box {
					width: 200rpx;
					margin-left: 10rpx;
					margin-right: 50rpx;
				}

				.s_input {
					padding: 0 9px !important;
				}

				.s_btn_box {
					.del {
						background-color: #999999;
						border-color: #999999;
						// height: 100rpx;
					}

					.yes {
						// height: 100rpx;
					}
				}
			}
		}

		// 000000000000000000000000
		.popup_center {
			padding: 0 52rpx;
		}

		.popup_item {
			margin-bottom: 20rpx;
		}

		.popup_item_tit {
			width: 200rpx;
			margin-right: 20rpx;
		}

		.xgfytxt {
			color: #DE868F;
			font-size: 28rpx;
		}
	}

	.fontr {
		color: #a4a4a4;
		font-size: 24rpx;
	}
</style>