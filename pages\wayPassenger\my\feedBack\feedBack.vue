<template>
	<view>
		<u-navbar title="意见反馈" :autoBack="true" :placeholder="true">
		</u-navbar>
		<view class="conten">
			<view style="margin-bottom: 20rpx;">
				<u--textarea v-model="textArear" placeholder="请输入您的意见或建议!" :height="200" confirmType='done'>
				</u--textarea>
			</view>
			<u-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic" multiple :maxCount="3">
			</u-upload>
			<u-button type="primary" color="#346CF2" text="完成" customStyle="margin-top: 20px" @click="saveBtn">
			</u-button>
		</view>
	</view>
</template>

<script>
	import {
		uploadImg,
		msgFeedBack
	} from '@/config/consoler.js'
	export default {
		data() {
			return {
				textArear: "",
				fileList: [],
				uploadShow: true
			}
		},
		methods: {
			saveBtn() {
				if (!this.uploadShow) return uni.$u.toast('请等待图片上传完成后再点击。')
				if (!this.textArear) return uni.$u.toast('请输入反馈内容。')
				let urlr = this.fileList.map(v => {
					return v.urlr
				}).join()
				msgFeedBack({
					feedbackContent: this.textArear,
					feedbackUrls: urlr
				}).then(res => {
					uni.$u.toast('感谢您的反馈！')
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 1000)
				})
			},
			// 上传附件
			afterRead(event) {
				if (this.fileList.length == 3) return uni.$u.toast('只能上传三张图片。')
				this.uploadShow = false
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList`].length
				lists.map((item) => {
					this[`fileList`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				lists.forEach(r => {
					uploadImg(r, 4).then(res => {
						this.uploadShow = true
						if (res) {
							let item = this[`fileList`][fileListLen]
							this[`fileList`].splice(fileListLen, 1, Object.assign(item, {
								status: 'success',
								message: '',
								urlr: res
							}))
							fileListLen++
						} else {
							this[`fileList`].splice(fileListLen, 1)
							uni.$u.toast('上传失败')
						}
					})
				})
			},
			// 删除附件
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},
		}
	}
</script>

<style scoped>
	.conten {
		margin: 20rpx 40rpx;
	}
</style>
