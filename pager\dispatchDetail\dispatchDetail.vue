<template>
	<view class="detail">
		<u-navbar title="详情" :autoBack="true" :placeholder="true">
			<view class="u-nav-slot" slot="right" v-if="detail.compOrderState==100">
				<span class="xgfytxt" @click="endOner(detail,true)">修改费用</span>
			</view>
		</u-navbar>
		<view class="app_list">
			<!-- 基本信息 -->
			<view>
				<!-- <u-image class="tab_icon" :width="16" :height="14"  src="https://zqcx.di-digo.com/app/image/wdsq_qcl.png"></u-image> -->
				<view class="u-flex u-row-between">
					<div class="text-dh">申请单号：{{detail.applyCode}}</div>
					<div class="text-dh u-text-right" style="color: #007AFF;">
						{{detail.compOrderState==10?"待审批":detail.compOrderState==95?"行后待审批":detail.compOrderState==20?"待派车":detail.compOrderState==24?"租赁待审批":detail.compOrderState==25?"租赁待派车":detail.compOrderState==30?"待接单":detail.compOrderState==40?"待执行":detail.compOrderState==50?"执行中":detail.compOrderState==60?"到达出发地":detail.compOrderState==70?"进行中":detail.compOrderState==80?"到达目的地":detail.compOrderState==120?"结算审核中":detail.compOrderState==200?"已完成":detail.compOrderState==210?"已取消":detail.compOrderState==220?"审批驳回":detail.compOrderState==230?"调度驳回":detail.compOrderState==100?"待确认":detail.compOrderState==90?"已回场":detail.compOrderState==110?"待结算":detail.compOrderState==205?"待评价":"订单异常"}}
					</div>
				</view>
			</view>

			<!-- 企业单显示 -->
			<view v-if="detail.travelType==1">
				<view class="app_t_line u-flex u-row-between">
					<view class="u-flex">
						<view class="tab_icon">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png"></u-image>
						</view>
						<span>{{detail.psgName}}</span>
					</view>

					<view @click="phoneClick(detail.psgMobile)">
						<a :href="'tel:'+detail.psgMobile">
							<u-image class="tab_icon" :width="22" :height="22"
								src="https://zqcx.di-digo.com/app/image/phone.svg"></u-image>
						</a>
					</view>

				</view>

				<view class="app_t_line u-flex">
					<view class="tab_icon">
						<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png">
						</u-image>
					</view>
					<span>{{detail.regulationName}}</span>
				</view>

				<view class="app_t_line u-flex">
					<view class="tab_icon">
						<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png">
						</u-image>
					</view>
					<span>{{detail.costCenterName}}</span>
				</view>
			</view>

			<!-- 租赁单显示 -->
			<view v-else>
				<!-- 转入单 -->
				<view v-if="detail.intoOrOut==2">
					<view class="app_t_line u-flex">
						<view class="tab_icon">
							<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/gongsi.png">
							</u-image>
						</view>
						<span>{{detail.applyUnitName}}</span>
					</view>

					<view class="app_t_line u-flex u-row-between">
						<view class="u-flex">
							<view class="tab_icon">
								<u-image :width="14" :height="14"
									src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png"></u-image>
							</view>
							<span>{{detail.psgName}}</span>
						</view>

						<view @click="phoneClick(detail.psgMobile)">
							<a :href="'tel:'+detail.psgMobile">

								<u-image class="tab_icon" :width="22" :height="22"
									src="https://zqcx.di-digo.com/app/image/phone.svg">
								</u-image>
							</a>
						</view>

					</view>
				</view>
				<!-- 转出单 -->
				<view v-else>
					<view class="app_t_line u-flex">
						<view class="tab_icon">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/icon-10.png"></u-image>
						</view>
						<span>{{detail.applyUnitName}}</span>
					</view>

					<view class="app_t_line u-flex u-row-between">
						<view class="u-flex">
							<view class="tab_icon">
								<u-image :width="14" :height="14"
									src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png"></u-image>
							</view>
							<span>{{detail.psgName}}</span>
						</view>

						<view @click="phoneClick(detail.psgMobile)">
							<a :href="'tel:'+detail.psgMobile">

								<u-image class="tab_icon" :width="22" :height="22"
									src="https://zqcx.di-digo.com/app/image/phone.svg">
								</u-image>
							</a>
						</view>

					</view>

					<!-- <view class="app_t_line u-flex">
						<view class="tab_icon">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png"></u-image>
						</view>
						<span>{{detail.costCenterName}}</span>
					</view>

					<view class="app_t_line u-flex">
						<view class="tab_icon">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png"></u-image>
						</view>
						<span>{{detail.regulationName}}</span>
					</view> -->

				</view>
			</view>

			<view class="app_t_line u-flex">
				<view class="tab_icon">
					<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_sj_icon.png">
					</u-image>
				</view>
				<span class="u-flex">{{detail.reserveStartTimeSeconds | date('mm月dd日 hh:MM')}} <u-icon
						name="arrow-rightward" color='#ffc107'></u-icon>
					{{detail.reserveEndTimeSeconds | date('mm月dd日 hh:MM')}}</span>
			</view>
			<view class="app_addrs app_t_line">
				<view class="u-flex ma">
					<view class="spot"></view>
					<span style="flex: 1;">{{detail.fromAddrName}}</span>
				</view>
				<view class="u-flex ma" v-for="(item,i) in detail.throughAddrInfo" :key="i">
					<view class="spot" style="background-color:#5ac725"></view>
					<span style="flex: 1;">{{item.siteAddrName}}</span>
				</view>
				<view class="u-flex ma">
					<view class="spot endspot"></view>
					<span style="flex: 1;">{{detail.toAddrName}}</span>
				</view>
			</view>

			<!-- 租赁转入单不显示  v-if="detail.intoOrOut!=2"-->
			<view>
				<view class="app_t_line u-flex">
					<view class="tab_icon">
						<u-image :width="14" :height="14"
							src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png"></u-image>
					</view>
					<!-- 乘车人数： -->
					<span>{{detail.ordPsgNums || 0}}人</span>
					<!-- <span class='tab_text' @click="bybusShow=true">查看</span> -->
				</view>
				<view class="app_t_line u-flex">
					<view class="tab_icon">
						<u-image :width="14" :height="14"
							src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png"></u-image>
					</view>
					<span>租车方式：{{detail.rentType==1?'带驾租车':detail.rentType==2?'不带驾租车':'-'}}</span>
				</view>
				
				<view class="app_t_line u-flex">
					<view class="tab_icon">
						<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png">
						</u-image>
					</view>
					<span>{{detail.compOrderState>=30&&detail.compOrderState<210?detail.carTypeFullName:detail.wantCarTypeFullName}}</span>
				</view>
				<view class="app_t_line u-flex">
					<view class="tab_icon">
						<u-image :width="14" :height="14"
							src="https://zqcx.di-digo.com/app/image/entp_icon2.png"></u-image>
					</view>
					<!-- 计费套餐： -->
					<span>{{detail.wantValuationName?detail.wantValuationName:''}}</span>
				</view>
				<view class="app_t_line " style="display: flex;">
					<view class="tab_icon" style="margin-top: 6rpx;">
						<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_icon4.png">
						</u-image>
					</view>
					<span>{{detail.description?detail.description:''}}</span>
				</view>
			</view>

		</view>


		<!-- 调度信息（企业）企业单租赁待审批（24） -->
		<block v-if="detail.carNumber || detail.driverName">
			<view class="app_list"
				v-if="(detail.compOrderState==24 || detail.compOrderState>=30)&&detail.compOrderState!=210&&detail.compOrderState!=220&&detail.compOrderState!=230&&detail.travelType==1">
				<view class="res_jd_tit weight">
					<span class="dian"></span>调度信息：
				</view>

				<view class="app_t_line u-flex" v-if='detail.carNumber'>
					<view class="tab_icon">
						<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_lx_icon.png">
						</u-image>
					</view>
					<span>{{detail.carNumber?detail.carNumber:''}}<span
							v-if="detail.numberColor">({{detail.numberColor?detail.numberColor:''}})</span></span>
				</view>

				<view class="app_t_line u-flex u-row-between" v-if='detail.driverName'>
					<view class="u-flex">
						<view class="tab_icon">
							<u-image :width="14" :height="14"
								src="https://zqcx.di-digo.com/app/image/wdsp_icon1.png">
							</u-image>
						</view>
						<span>{{detail.driverName?detail.driverName:''}}</span>
					</view>

					<view @click="phoneClick(detail.driverMobile)">
						<a :href="'tel:'+detail.driverMobile">
							<u-image class="tab_icon" :width="22" :height="22"
								src="https://zqcx.di-digo.com/app/image/phone.svg"></u-image>
						</a>
					</view>
				</view>

			</view>
		</block>
		<!-- 调度信息（租赁） -->
		<view class="app_list" v-if="detail.travelType==2">
			<view class="res_jd_tit weight">
				<span class="dian"></span>租赁调度信息：
			</view>
			<!-- 租赁转出单 -->
			<view class="app_t_line u-flex" v-if="detail.intoOrOut==1">
				<view class="tab_icon">
					<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/gongsi.png"></u-image>
				</view>
				<span>{{detail.leasingCompName?detail.leasingCompName:''}}</span>
			</view>

			<!-- 租赁转入单 -->
			<view class="app_t_line u-flex u-row-between" v-if="detail.intoOrOut==1 ||detail.intoOrOut==2">
				<view class="u-flex">
					<span><span class='app_span'>行程单号:</span>{{detail.travelCode?detail.travelCode:''}}</span>
					<u-tag class="text-rt-tag" v-if="detail.dispatchMode==1" text="拆" color="#8f4ef7"
						borderColor="#8f4ef7" bgColor="#eee5fe" size="mini" plain plainFill></u-tag>
				</view>
			</view>
			<view class="u-flex app_t_line">
				<span class='app_span '>车型:</span>
				<span style="flex: 1;">{{detail.carTypeFullName?detail.carTypeFullName:''}} </span>
			</view>
			<view class="u-flex app_t_line">
				<span class='app_span '>套餐:</span>
				<span style="flex: 1;">{{detail.valuationFullName?detail.valuationFullName:''}}
				</span>
			</view>
			<!--原先以下判断 v-if="detail.compOrderState>=30" -->
			<view>
				<view class="u-flex app_t_line">
					<span class='app_span'>车牌:</span>
					<span>{{detail.carNumber?detail.carNumber:''}}<span
							v-show="detail.numberColor">({{detail.numberColor}})</span>
					</span>
					<span>{{detail.brandModelName?detail.brandModelName:''}}</span>
				</view>
				<view class="app_t_line u-flex u-row-between">
					<view class="u-flex">
						<span class='app_span'>司机:</span>
						<span>{{detail.driverName?detail.driverName:''}}</span>
					</view>

					<view v-if="detail.driverMobile" @click="phoneClick(detail.driverMobile)">
						<a :href="'tel:'+detail.driverMobile">
							<u-image class="tab_icon" :width="22" :height="22"
								src="https://zqcx.di-digo.com/app/image/phone.svg"></u-image>
						</a>
					</view>
				</view>
			</view>
		</view>

		<!-- 费用信息 -->
		<view class="app_list" v-if="detail.compOrderState>=100&&detail.compOrderState<=205">
			<view class="res_jd_tit weight">
				<span class="dian"></span>费用信息：
			</view>

			<u-cell-group class="detail_box" :border="false">

				<u-cell>
					<view class="detail_val" slot="title">服务里程:
						<span class="detail_span">{{detail.actualUseCarMileage}}公里</span>
					</view>
					<view class="detail_val" slot="value">服务时长:
						<span class="detail_span">{{detail.actualUseCarTime}}</span>
					</view>
				</u-cell>
				<div v-for="(item,index) in detail.feeDetail" :key="index" class="feeDetai_item">
					<!-- 101 起步价 -->
					<template v-if="item.feeDetailCode==101">
						<u-cell :border="false">
							<view class="detail_val" slot="title">{{item.key}}</view>
							<view class="detail_val black" slot="value">{{item.value}}元</view>
							<view class="detail_label" slot="label" v-if='item.detail'>({{item.detail}})</view>
						</u-cell>
					</template>
					<template v-else>
						<u-cell v-if="Number(item.value)>0" :border="false">
							<view class="detail_val" slot="title">{{item.key}}</view>
							<view class="detail_val black" slot="value">{{item.value}}元</view>
							<view class="detail_label" slot="label" v-if='item.detail'>({{item.detail}})</view>
						</u-cell>
					</template>
				</div>
				<u-line></u-line>
				<div v-for="(item,index) in detail.feeDetail" :key="index" class="feeDetai_item">
					<template v-if="item.feeDetailCode==200">
						<template v-if="item.unit&&item.unit.length>0">
							<div v-for="(ite,i) in item.unit" :key="i">
								<u-cell v-if="Number(ite.value)>0" :border="false">
									<view class="detail_val" slot="title">{{ite.key}}</view>
									<view class="detail_val black" slot="value">{{ite.value}} 元</view>
									<view class="detail_label" slot="label" v-if='ite.detail'>({{ite.detail}})
									</view>
								</u-cell>
							</div>
						</template>
					</template>
				</div>
				<u-line></u-line>
				<u-cell :border="false" class="feeDetai_item">
					<view class="detail_val u-text-bold" slot="title">订单金额</view>
					<view class="detail_val_b" slot="value">{{detail.totalFee.toFixed(2)}} 元</view>
				</u-cell>
				<u-line></u-line>
				<!-- 企业单没有优惠费用跟折扣金额 企业单：1 租赁单：2-->
				<u-cell v-if="detail.discountReduceFee" :border="false" class="feeDetai_item">
					<view class="detail_val u-text-bold" slot="title"><span>企业折扣</span></view>
					<view class="detail_val " slot="value"><span>-{{detail.discountReduceFee.toFixed(2)}} 元</span>
					</view>
				</u-cell>

				<u-cell v-if="detail.reduceFee" :border="false" class="feeDetai_item">
					<view class="detail_val u-text-bold" slot="title"><span>优惠金额</span></view>
					<view class="detail_val" slot="value"><span>-{{detail.reduceFee.toFixed(2)}} 元</span></view>
				</u-cell>
				<u-line></u-line>
				<u-cell :border="false" class="feeDetai_item">
					<view class="detail_val u-text-bold black" slot="title">
						{{detail.travelType==1?'订单应付金额':'订单应收金额'}}
					</view>
					<view class="detail_val_b black" slot="value">{{(detail.actualFee).toFixed(2)}} 元</view>
				</u-cell>
				<u-cell :border="false" class="feeDetai_item">
					<view class="detail_val u-text-bold black" slot="title">平台服务费</view>
					<view class="detail_val_b black" slot="value">
						{{detail.providerServeFee?(detail.providerServeFee).toFixed(2):(0).toFixed(2)}} 元
					</view>
				</u-cell>
				<u-cell :border="false" class="feeDetai_item">
					<view class="detail_val u-text-bold black" slot="title">
						{{detail.travelType==1?'订单支付金额':'订单收入金额'}}
					</view>
					<view class="detail_val_b black" slot="value">
						<span v-if="detail.travelType==1">
							{{(detail.actualFee+(detail.providerServeFee?detail.providerServeFee:0)).toFixed(2)}} 元
						</span>
						<span v-else>
							{{(detail.actualFee-(detail.providerServeFee?detail.providerServeFee:0)).toFixed(2)}} 元
						</span>
					</view>
				</u-cell>
			</u-cell-group>

		</view>

		<!-- 订单跟踪 -->
		<view class="app_list" v-if="detail.orderEventHis&&detail.orderEventHis.length>0">
			<view class="res_jd_tit weight">
				<span class="dian"></span>订单跟踪：
			</view>
			<view class="res_jd">
				<u-steps :current="detail.orderEventHis.length-1" direction="column">
					<u-steps-item v-for="(item,index) in detail.orderEventHis" :key="index"
						:title="`${item.operatorName} ${item.evtDetContent} ${item.evtDescription?'原因:'+item.evtDescription:''}`"
						:desc="new Date(item.operatorTime).getTime() | date('yy年mm月dd日 hh:MM:ss')">
						<!-- 	<view slot="title">
							{{}} <span
								v-if="item.evtDescription">{{`退单原因: ${item.evtDescription}`}}</span>
						</view> -->
					</u-steps-item>
				</u-steps>
			</view>
		</view>


		<view class="footer_box btn_box u-flex u-row-around">
			<view class="btnmini" v-if="detail.travelType==1&&detail.dispatchButton.isShowButtonDisp==1">
				<u-button type="success" text="派车" @click="paiche(detail,1)"></u-button>
			</view>
			<view class="btnmini" v-if="detail.travelType==2&&detail.dispatchButton.isShowButtonDisp==1">
				<u-button type="success" text="租赁派车" @click="zlpaiche(detail,2)"></u-button>
			</view>
			<view class="btnmini" v-if="detail.dispatchButton.isShowButtonReDisp==1">
				<u-button type="success" text="改派" @click="opengaip(detail,3)"></u-button>
				<!-- <u-button class="btnmini" v-if="detail.dispatchButton.isShowButtonReDispCheck==1" type="success" text="改派审核" @click="opengpsh(detail)"></u-button> -->
			</view>
			<view class="btnmini" v-if="detail.dispatchButton.isShowButtonDispReject==1">
				<u-button class="hui" color="#ccc" type="success" text="驳回" @click="operation(detail,1)"></u-button>
			</view>
			<view class="btnmini" v-if="detail.dispatchButton.isShowButtonLeaseReject==1">
				<u-button class="hui" color="#ccc" type="success" text="退单" @click="operation(detail,1,true)">
				</u-button>
			</view>
			<view class="btnmini" v-if="detail.dispatchButton.isShowButtonChangeTeam==1">
				<u-button type="primary" text="转车队" @click="operation(detail,2)"></u-button>
			</view>
			<view class="btnmini" v-if="detail.dispatchButton.isShowButtonToLease==1">
				<u-button type="error" text="转租赁" @click="operation(detail,3)"></u-button>
			</view>
			<view class="btnmini" v-if="detail.dispatchButton.isShowButtonBack==1">
				<u-button type="warning" text="撤单" @click="operation(detail,4)"></u-button>
			</view>
			<view class="btnmini" v-if="detail.dispatchButton.isShowButtonFinish==1">
				<u-button type="primary" text="结单" @click="endOner(detail)"></u-button>
			</view>
			<!-- #ifdef MP-WEIXIN -->
			<view class="btnmini" v-if="detail.compOrderState==100">
				<u-button type="primary" color="#00557f" text="修改费用" @click="endOner(detail,true)"></u-button>
			</view>
			<!-- #endif -->
		</view>

		<!-- 驳回弹窗组件 -->
		<u-popup :round="5" :show="rejectshow" mode="center" @close="rejectshow=false" :customStyle="styleObjr">
			<rejectCom v-if="rejectshow" :rejectObj="choiceOrder" :rejectName="formName" @adoptFun="adoptFun">
			</rejectCom>
		</u-popup>

		<!-- 撤单组件 -->
		<u-popup class="popup_bg" :round="5" :show="cancelOrdershow" mode="center" @close="cancelOrderclose">
			<view class="popup_tit">
				撤单
			</view>
			<view class="popup_txt">
				是否确定撤回转外协租赁车队派车订单？
			</view>
			<view class="u-flex u-row-between popup_btn_box">
				<u-button class="popup_btn" color="#346CF2" type="primary" text="确定" @click="cancelOrder()">
				</u-button>
				<u-button class="popup_btn two" color="#E9ECF7" type="primary" text="取消" @click="cancelOrderclose()">
				</u-button>
			</view>
		</u-popup>
		<!--乘车人数-->
		<u-popup class="popup_bg" :round="5" :show="bybusShow" mode="center" @close="bybusShow=false">
			<view class="u-flex popup_by" v-for="(item,i) in detail.psgUserInfos" :key="i">
				<span class="u-flex">
					<view class="tab_icon">
						<u-image :width="14" :height="14" src="https://zqcx.di-digo.com/app/image/wdsq_ccrs_icon.png"
							style="margin-right: 20rpx;">
						</u-image>
					</view>
					{{item.name}}-{{item.mobile}}
				</span>
			</view>
		</u-popup>

		<!-- 车型弹窗 -->
		<u-picker :show="carTypeWin" ref="uPicker" keyName="carTypeName" :columns="carTypecolumns"
			@cancel="carTypeclose" @confirm="carTypeconfirm" @change="carTypechange"></u-picker>
		<!-- 包车套餐弹窗 -->
		<u-picker :show="packageWin" keyName="valuationFullName" :columns="packagecolumns" @cancel="packageclose"
			@confirm="packageconfirm"></u-picker>

		<!-- 开始时间 -->
		<u-datetime-picker :show="startTimeShow" v-model="choiceTime" @cancel="startTimeShow=false"
			@confirm="confirmstartEnd" mode="datetime"></u-datetime-picker>

		<!-- 结束时间 -->
		<u-datetime-picker :show="endTimeShow" v-model="choiceTime" @cancel="endTimeShow=false" @confirm="confirmendEnd"
			mode="datetime">
		</u-datetime-picker>

		<!-- 结单弹窗组件 1-->
		<u-popup :round="5" :show="statemenShow" mode="center" @close="statemenShow=false" :customStyle="styleObjr">
			<statementOne v-if="statemenShow" :isChange="stateTyper" :stateObj="endParam" @stateOne="receiveOne">
			</statementOne>
		</u-popup>
		<!-- 结单弹窗组件 2-->
		<u-popup :round="5" :show="newShow" mode="center" @close="newShow=false" :customStyle="styleObjr">
			<statementTwo v-if="newShow" :stateObj="newParam" :stateType="stateTyper" :oneStateObj="endParam"
				@stateTwo="receiveTwo" @stateTwoChange="receiveTwoChanege">
			</statementTwo>
		</u-popup>

	</view>
</template>

<script>
	import {
		dispcomporderlist,
		dispcomporderinfo,
		dispatchleasecar,
		dispatchcompreject,
		dispatchleasechangefleet,
		dispatchtolease,
		dispatchtoleaseback,
		cartypetreelist,
		tempvaluationlist,
		dispatchleasechangecar,
		tempvaluationinfo,
		costcalculate,
		dispatchcompchangeover,
		checktoleaseorder,
		changefee
	} from '@/config/api.js';
	import rejectCom from '@/pages/wayPassenger/workBench/componentr/rejectCom.vue'
	import statementOne from '@/pages/wayPassenger/workBench/componentr/statementOne.vue'
	import statementTwo from '@/pages/wayPassenger/workBench/componentr/statementTwo.vue'
	export default {
		components: {
			statementOne,
			statementTwo,
			rejectCom
		},
		data() {
			return {
				id: '',
				detail: {
					dispatchButton: {
						isShowButtonReDisp: null
					}
				},
				popupshow: false,
				rejectshow: false,
				cancelOrdershow: false,
				radioVal: '1',
				choiceOrder: {},
				//驳回原因
				reason: '',
				//派车参数
				oneCarDriverArr: [{
					carId: '',
					carName: '',
					driverUserName: '',
					driverUserId: '',
					carTypeId: '',
					valuationId: '',
				}],
				//拆单派车参数
				twoCarDriverArr: [{
					carId: '',
					carName: '',
					driverUserName: '',
					driverUserId: '',
					carTypeId: '',
					valuationId: '',
				}],
				leaseshow: false,
				leasePopshow: false,
				carleasePopshow: false,
				leaseradioVal: '3',

				// 转租赁操作参数
				leaseIndex: '',
				leaseSubmitArr: {
					carTypeId: "",
					coopeMerId: "",
					psgNums: "",
					remark: "",
					valuationId: ""
				},
				// 车型弹窗相关参数
				carTypeWin: false,
				carTypecolumns: [],
				cartypetree: [],
				// 包车套餐弹窗相关参数
				packageWin: false,
				packagecolumns: [],
				// 选择车型是否是拆单操作
				ischaid: false,
				// 改派弹窗相关参数
				GPshow: false,
				GPactions: [{
						name: '派车',
					},
					// {
					// 	name: '驳回',
					// },
					// {
					// 	name: '转车队',
					// },
				],
				// 改派审核弹窗相关参数
				gpshshow: false,
				//结束订单

				costDetail: {},
				startTimeShow: false,
				endTimeShow: false,
				choiceTime: '',
				// 上传相关
				fileList: [],
				sheetType: false,
				sheetObj: {},
				bybusShow: false,
				styleObjr: {
					width: '85%'
				},
				formName: '',
				// 结单
				statemenShow: false,
				endParam: {},
				stateTyper: false,
				newParam: {},
				newShow: false
			};
		},
		filters: {
			transDay(time) {
				let d = parseInt(time / 60 / 60 / 24)
				let h = parseInt(time / 60 / 60 % 24)
				h = h < 10 ? '0' + h : h
				let m = parseInt(time / 60 % 60)
				m = m < 10 ? '0' + m : m
				let s = parseInt(time % 60)
				s = s < 10 ? '0' + s : s
				// 作为返回值返回
				return d + "天" + h + "小时" + m + "分钟"
			}
		},
		onLoad(option) {
			this.id = option.id
			this.choiceTime = Number(new Date());
			this.getDetail()
			this.getTree()
		},
		methods: {
			phoneClick(phone) {
				uni.makePhoneCall({
					phoneNumber: phone //仅为示例
				});
			},
			receiveTwo(typer, obj, datar) {
				console.log(obj, typer);
				//  4 修改费用 3 结单
				if (typer == 1) {
					this.endParam = datar
					this.newShow = false
					this.statemenShow = true
				}
				if (typer == 2) {
					this.newShow = false
				}
				if (typer == 3) {
					this.endtFtion(obj)
				}
				if (typer == 4) {
					this.editFtion(obj)
				}
			},
			endtFtion(obj) {
				dispatchcompchangeover({
					dispatchOverVo: obj,
					id: obj.travelId,
				}).then((data) => {
					uni.$u.toast('操作成功')
					this.getDetail()
					this.newShow = false
				})
			},
			editFtion(obj) {
				changefee({
					dispatchOverVo: obj,
					id: obj.travelId,
				}).then((data) => {
					uni.$u.toast('操作成功')
					this.getDetail()
					this.newShow = false
				})
			},
			receiveOne(objr, datar) {
				this.endParam = datar
				if (!objr) return this.statemenShow = false
				this.newParam = objr
				this.newShow = true
				this.statemenShow = false
			},
			// 结单、修改费用
			endOner(item, typer=false) {
				this.stateTyper = typer
				let objr = {
					actualEndMileage: typer ? item.actualEndMileage : '',
					actualEndTime: typer ? item.actualEndTime : '',
					startMileageImgUrl: typer ? item.startMileageImgUrl : '',
					endMileageImgUrl: typer ? item.endMileageImgUrl : '',
					// actualStartMileage: typer ? item.actualStartMileage : '',
					actualStartMileage: item.actualStartMileage?item.actualStartMileage:'',
					actualStartTime: typer ? item.actualStartTime : '',
					otherFee: {},
					reduceFee: typer ? item.reduceFee : '',
					travelType: item.travelType,
					carTypeFullName: item.carTypeFullName,
					valuationFullName: item.valuationFullName,
					travelId: item.travelId,
					valuationTemplate: JSON.parse(JSON.parse(item.valuationTemplate)?.otherFee),
					overtakeTimeType:item.overtakeTimeType,
					dayEndMileage:JSON.parse(item.dayEndMileage),
					nights:item.nights
				}
				this.endParam = {
					...objr
				}
				// typer true 修改费用 false结算
				if (typer) {
					let attr = []
					if (item.feeDetail) {
						item.feeDetail.forEach(v => {
							if (v.feeDetailCode == 200) {
								v.unit.forEach(r => {
									r.feedatarCode = 200
									attr.push(r)
								})
							}
						})
					}
					if (attr.length == 0) {
						objr.valuationTemplate.forEach(v => {
							v.feedatarCode = 200
						})
						this.endParam.otherFee = objr.valuationTemplate
					} else {
						this.endParam.otherFee = attr
					}
					console.log(this.endParam,'----',item)
					this.statemenShow = true
				} else {
					this.getTempValue(item)
				}

			},

			getTempValue(item) {
				tempvaluationinfo({
					params: {
						id: item.valuationId,
					}
				}).then((data) => {
					let arrt = JSON.parse(data.otherFee)
					arrt.forEach(v => {
						v.feedatarCode = 200
					})
					this.endParam.otherFee = arrt
					this.$nextTick(() => {
						this.statemenShow = true
					})
				})
			},
			// 驳回请求
			adoptFun(row) {
				if (!row) return this.rejectshow = false
				dispatchcompreject({
					dispatchRejectVo: {
						reason: row.checkRemark
					},
					id: this.detail.travelId
				}).then((data) => {
					uni.$u.toast('操作成功')
					this.rejectshow = false
					if (this.formName == '退单') {
						setTimeout(() => {
							uni.$u.route({
								url: '/pager/dispatch/dispatch',
							})
						}, 800)
					}
					setTimeout(() => {
						this.getDetail()
					}, 1000)
				})
			},


			getDetail() {
				this.detail.orderEventHis = []
				dispcomporderinfo({
					params: {
						id: this.id,
					}
				}).then((data) => {
					if (data.feeDetail) {
						data.feeDetail = JSON.parse(data.feeDetail)
						data.feeDetail.map((item, index) => {
							if (item.feeDetailCode == 200) {
								item.unit = JSON.parse(item.unit)
							}
						})
					}
					if (data.throughAddrInfo) {
						data.throughAddrInfo = JSON.parse(data.throughAddrInfo)
					}
					this.detail = data
					this.choiceOrder = data
				})
			},

			cancelOrderclose() {
				this.cancelOrdershow = false
			},
			// 添加拆单派车数组
			addArr() {
				this.twoCarDriverArr.push({
					carId: '',
					carName: '',
					driverUserName: '',
					driverUserId: '',
					carTypeId: '',
					valuationId: '',
				})
			},
			// 删除拆单派车数组
			removeArr(index) {
				this.twoCarDriverArr.splice(index, 1)
			},
			// 选择车辆
			choiceCar(index, iszl) {
				let typeNum;
				let dispatchMode;
				let carTypeId;
				if (iszl) {
					typeNum = this.leaseradioVal
					if (typeNum == "3") {
						dispatchMode = "0"
					} else {
						dispatchMode = "1"
						// 如果是租赁拆单的话车型必需线先选择
						if (!this.twoCarDriverLeaseArr[index].carTypeId) {
							uni.$u.toast('请先选择车型')
						} else {
							carTypeId = this.twoCarDriverLeaseArr[index].carTypeId
						}
					}
				} else {
					typeNum = this.radioVal
					if (typeNum == "1") {
						dispatchMode = "0"
					} else {
						dispatchMode = "1"
					}
				}

				uni.$u.route('/pages/wayPassenger/workBench/dispatch/choiceCar/choiceCar', {
					travelId: this.detail.travelId,
					type: typeNum,
					index: index,
					dispatchMode: dispatchMode,
					carTypeId: carTypeId,
					detail: true
				});
			},
			// 选择司机
			choiceDriver(index, iszl) {
				let typeNum;
				if (iszl) {
					typeNum = this.leaseradioVal
				} else {
					typeNum = this.radioVal
				}
				uni.$u.route('/pages/wayPassenger/workBench/dispatch/choiceDriver/choiceDriver', {
					travelId: this.detail.travelId,
					type: typeNum,
					index: index,
					detail: true
				});
			},
			// 派车弹窗
			paiche(item, t) {
			
				let obj ={
					travelId: item.travelId,
					reserveStartTime: item.reserveStartTime,
					compOrderState: item.compOrderState,
					wantCarTypeId: item.wantCarTypeId,
					carTypeId: item.carTypeId,
					wantCarTypeFullName: item.wantCarTypeFullName,
					carTypeIdName: item.carTypeIdName,
					feeTemplateId: item.feeTemplateId,
					rentType: item.rentType,
					travelType: item.travelType,
				}
				obj.typer = t
				obj.newTyper = 'edit'
				uni.$u.route('/pager/dispatch/newDispatch/newDispatch', {
					item: JSON.stringify(obj),
				});
				return
				this.setEmpty()
				this.popupshow = true
				this.sheetType = false //判断是改派还是派单 false = 改派
			},
			popupclose() {
				this.popupshow = false
			},
			// 改派、派单中间件判断
			dispatchBtn() {
				//  true 改派 、false 派單
				if (this.sheetType) {
					this.GPselect(this.sheetObj)
				} else {
					this.dispatchleasecarFun()
				}
			},
			// 派车操作
			dispatchleasecarFun() {
				if (this.radioVal == '1') {
					//派车
					dispatchleasecar({
						dispatchCarVo: {
							dispatchCarChildList: this.oneCarDriverArr,
							dispatchMode: 0
						},
						id: this.detail.travelId,
					}).then((data) => {
						uni.$u.toast('操作成功')
						this.popupshow = false
						setTimeout(() => {
							this.popupshow = false
							this.getDetail()
							this.setEmpty()
						}, 1000)
					})
				} else {
					//拆单派车
					dispatchleasecar({
						dispatchCarVo: {
							dispatchCarChildList: this.twoCarDriverArr,
							dispatchMode: 1
						},
						id: this.detail.travelId,
					}).then((data) => {
						uni.$u.toast('操作成功')
						this.popupshow = false
						setTimeout(() => {
							this.popupshow = false
							this.getDetail()
							this.setEmpty()
						}, 1000)
					})
				}


			},
			setEmpty() {
				this.oneCarDriverArr = [{
						carId: '',
						carName: '',
						driverUserName: '',
						driverUserId: '',
						carTypeId: '',
						valuationId: '',
					}],
					this.twoCarDriverArr = [{
						carId: '',
						carName: '',
						driverUserName: '',
						driverUserId: '',
						carTypeId: '',
						valuationId: '',
					}]
			},


			// 撤单
			cancelOrder() {
				dispatchtoleaseback({
					id: this.detail.travelId,
				}).then((data) => {
					uni.$u.toast('操作成功')
					setTimeout(() => {
						this.getDetail()
					}, 1000)
				})
			},
			operation(item, num, type) {
				this.choiceOrder = item
				if (num == 0) {

				} else if (num == 1) {
					//驳回
					this.formName = type ? '退单' : '驳回'
					this.rejectshow = true

				} else if (num == 2) {
					//转车队
					uni.$u.route('/pagec/choiceFleet/choiceFleet', {
						travelId: this.detail.travelId,
						typer: 'detail'
					});
				} else if (num == 3) {
					//转租赁
					uni.$u.route('/pagec/newSublease/newSublease', {
						travelId: this.detail.travelId,
						reserveStartTime: new Date(this.detail.reserveStartTime).getTime()
					});
					this.leaseshow = true
				} else if (num == 4) {
					//撤单
					this.cancelOrdershow = true
				}
			},

			// 转租赁派车弹窗唤起
			leasePop() {

				this.leaseshow = false
				this.leasePopshow = true
			},
			// 转租赁操作弹窗唤起
			leaseSubmitPop() {
				this.leaseSubmitArr = [{
						carTypeId: "",
						coopeMerId: "",
						psgNums: "",
						remark: "",
						valuationId: ""
					}],

					this.leaseshow = false
				this.leasePopshow = true
			},
			leasePopclose() {
				this.leasePopshow = false
			},
			carleasePopclose() {
				this.carleasePopshow = false
			},
			// 添加租赁拆单派车数组
			addleaseArr() {
				this.twoCarDriverLeaseArr.push({
					carId: '',
					carName: '',
					driverUserName: '',
					driverUserId: '',
					carTypeId: '',
					valuationId: '',
				})
			},
			// 删除租赁拆单派车数组
			removeleaseArr(index) {
				this.twoCarDriverLeaseArr.splice(index, 1)
			},
			// 添加租赁操作数组
			addleaseSubmitArr() {
				this.leaseSubmitArr.push({
					carTypeId: "",
					coopeMerId: "",
					psgNums: "",
					remark: "",
					valuationId: ""
				})
			},
			// 删除租赁操作数组
			removeleaseSubmitArr(index) {
				this.leaseSubmitArr.splice(index, 1)
			},

			// 转租赁操作
			leasePopFun() {
				dispatchtolease({
					dispatchToLeaseVo: {
						orderItem: this.leaseSubmitArr
					},
					id: this.choiceOrder.travelId,
				}).then((data) => {
					uni.$u.toast('操作成功')
					this.leasePopshow = false
					setTimeout(() => {
						this.getDetail()
					}, 1000)
				})
			},

			//转租赁选择租赁公司
			choiceCoopeMer(index) {
				uni.$u.route('/pages/wayPassenger/workBench/dispatch/choiceCompany/choiceCompany', {
					index: index
				});
			},
			//转租赁选择车型ischaid 是否是租赁拆单
			choiceCarType(index, ischaid) {
				this.ischaid = ischaid
				this.carTypeWin = true
				this.leaseIndex = index
			},
			getTree() {
				cartypetreelist({
					params: {}
				}).then((data) => {
					this.cartypetree = data
					this.carTypecolumns = [data, data[0].children]

				})
			},
			// 车型弹窗方法
			carTypeclose() {
				this.carTypeWin = false
			},
			carTypechange(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e
				console.log(value, 'value')
				// 当第一列值发生变化时，变化第二列(后一列)对应的选项
				if (columnIndex === 0) {
					// picker为选择器this实例，变化第二列对应的选项
					if (value[0].children && value[0].children.length > 0) {
						picker.setColumnValues(1, this.cartypetree[index].children)
					} else {
						picker.setColumnValues(1, [])
					}
				}
			},
			// 回调参数为包含columnIndex、value、values
			carTypeconfirm(e) {
				console.log('confirm', e)
				// 是否是拆单操作
				if (this.ischaid) {
					if (e.value.length > 1 && e.value[1]) {
						this.twoCarDriverLeaseArr[this.leaseIndex].carTypeName = e.value[0].carTypeName + '/' + e.value[1]
							.carTypeName;
						this.twoCarDriverLeaseArr[this.leaseIndex].carTypeId = e.value[1].carTypeId
					} else {
						this.twoCarDriverLeaseArr[this.leaseIndex].carTypeName = e.value[0].carTypeName
						this.twoCarDriverLeaseArr[this.leaseIndex].carTypeId = e.value[0].carTypeId
					}
				} else {
					if (e.value.length > 1 && e.value[1]) {
						this.leaseSubmitArr[this.leaseIndex].carTypeName = e.value[0].carTypeName + '/' + e.value[1]
							.carTypeName;
						this.leaseSubmitArr[this.leaseIndex].carTypeId = e.value[1].carTypeId
					} else {
						this.leaseSubmitArr[this.leaseIndex].carTypeName = e.value[0].carTypeName
						this.leaseSubmitArr[this.leaseIndex].carTypeId = e.value[0].carTypeId
					}
				}

				this.carTypeWin = false
			},
			// 选择包车套餐
			choiceValuation(index, ischaid) {
				this.ischaid = ischaid;
				// 是否是拆单操作
				if (this.ischaid) {
					if (this.twoCarDriverLeaseArr[index].carTypeId) {
						this.leaseIndex = index
						tempvaluationlist({
							params: {
								// 租赁单整个任务的计费模板ID
								feeTemplateId: this.choiceOrder.feeTemplateId,
								carTypeId: this.twoCarDriverLeaseArr[index].carTypeId,
							}
						}).then((data) => {
							this.packagecolumns = [data]
							this.packageWin = true
						})
					} else {
						uni.$u.toast("请先选择租赁单位和车型!")
					}
				} else {
					if (this.leaseSubmitArr[index].feeTemplateId && this.leaseSubmitArr[index].carTypeId) {
						this.leaseIndex = index
						tempvaluationlist({
							params: {
								// 计费模板ID
								feeTemplateId: this.leaseSubmitArr[index].feeTemplateId,
								carTypeId: this.leaseSubmitArr[index].carTypeId,
							}
						}).then((data) => {
							this.packagecolumns = [data]
							this.packageWin = true
						})
					} else {
						uni.$u.toast("请先选择租赁单位和车型!")
					}
				}

			},
			// 车型弹窗方法
			packageclose() {
				this.packageWin = false
			},
			// 回调参数为包含columnIndex、value、values
			packageconfirm(e) {
				console.log('confirm', e)
				if (this.ischaid) {
					if (e.value[0]) {
						this.twoCarDriverLeaseArr[this.leaseIndex].valuationId = e.value[0].valuationId;
						this.twoCarDriverLeaseArr[this.leaseIndex].valuationName = e.value[0].valuationFullName;
					}
				} else {
					if (e.value[0]) {
						this.leaseSubmitArr[this.leaseIndex].valuationId = e.value[0].valuationId;
						this.leaseSubmitArr[this.leaseIndex].valuationName = e.value[0].valuationFullName;
					}
				}

				this.packageWin = false
			},
			// 租赁派车弹窗
			zlpaiche(item, t) {
			
				let obj ={
					travelId: item.travelId,
					reserveStartTime: item.reserveStartTime,
					compOrderState: item.compOrderState,
					wantCarTypeId: item.wantCarTypeId,
					carTypeId: item.carTypeId,
					wantCarTypeFullName: item.wantCarTypeFullName,
					carTypeIdName: item.carTypeIdName,
					feeTemplateId: item.feeTemplateId,
					rentType: item.rentType,
					travelType: item.travelType,
				}
				obj.typer = 2
				obj.newTyper = 'edit'
				uni.$u.route('/pager/dispatch/newDispatch/newDispatch', {
					item: JSON.stringify(obj),
				});
				return

				this.choiceOrder = item
				this.carleasePopshow = true
			},

			// 改派弹窗方法
			opengaip(item, t) {
				
				let obj ={
					travelId: item.travelId,
					reserveStartTime: item.reserveStartTime,
					compOrderState: item.compOrderState,
					wantCarTypeId: item.wantCarTypeId,
					carTypeId: item.carTypeId,
					wantCarTypeFullName: item.wantCarTypeFullName,
					carTypeIdName: item.carTypeIdName,
					feeTemplateId: item.feeTemplateId,
					rentType: item.rentType,
					travelType: item.travelType,
				}
				obj.typer = t
				obj.newTyper = 'edit'
				uni.$u.route('/pager/dispatch/newDispatch/newDispatch', {
					item: JSON.stringify(obj),
				});
				return
				this.choiceOrder = item;
				this.GPshow = true;
			},
			// 选择派车（新版本）
			setSheet(e, t) {
				this.sheetType = t
				this.sheetObj = e
				// this.choiceOrder = item;
				this.popupshow = true
			},
			// 选择派车（旧版本）
			GPselect(e) {
				if (e.name == "派车") {
					if (this.choiceOrder.travelType == 1) {
						this.dispatchleasecarFunGP()
					} else {
						this.leaseFunGP()
					}
				} else if (e.name == "驳回") {
					//驳回
					this.reason = ''
					this.rejectshow = true
				} else if (e.name == "转车队") {
					//转车队
					uni.$u.route('/pages/wayPassenger/workBench/dispatch/choiceFleet/choiceFleet', {
						travelId: this.choiceOrder.travelId
					});
				}
			},
			// 改派审核
			opengpsh(item) {
				this.choiceOrder = item;
				this.gpshshow = true
			},
			gpshclose() {
				this.gpshshow = false
			},
			// 企业单改派操作
			dispatchleasecarFunGP() {
				if (this.radioVal == '1') {
					//派车
					dispatchleasechangecar({
						dispatchCarVo: {
							dispatchCarChildList: this.oneCarDriverArr,
							dispatchMode: 0
						},
						id: this.choiceOrder.travelId,
					}).then((data) => {
						uni.$u.toast('操作成功')
						this.popupshow = false
						setTimeout(() => {
							this.getDetail()
							this.setEmpty()
						}, 1000)
					})
				} else {
					//拆单派车
					dispatchleasechangecar({
						dispatchCarVo: {
							dispatchCarChildList: this.twoCarDriverArr,
							dispatchMode: 1
						},
						id: this.choiceOrder.travelId,
					}).then((data) => {
						uni.$u.toast('操作成功')
						this.popupshow = false
						setTimeout(() => {
							this.getDetail()
							this.setEmpty()
						}, 1000)
					})
				}

			},
			// 转租赁改派
			leaseFunGP() {
				if (this.leaseradioVal == '3') {
					//派车
					dispatchleasechangecar({
						dispatchCarVo: {
							dispatchCarChildList: this.oneCarDriverLeaseArr,
							dispatchMode: 0
						},
						id: this.choiceOrder.travelId,
					}).then((data) => {
						uni.$u.toast('操作成功')
						this.popupshow = false
						setTimeout(() => {
							this.getDetail()
							this.setEmpty()
						}, 1000)
					})
				} else {
					//拆单派车
					dispatchleasechangecar({
						dispatchCarVo: {
							dispatchCarChildList: this.twoCarDriverLeaseArr,
							dispatchMode: 1
						},
						id: this.choiceOrder.travelId,
					}).then((data) => {
						uni.$u.toast('操作成功')
						this.popupshow = false
						setTimeout(() => {
							this.getDetail()
							this.setEmpty()
						}, 1000)
					})
				}
			},
			// 开始时间
			confirmstartEnd(e) {
				this.startTimeShow = false
				this.endParam.actualStartTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM')
			},
			confirmendEnd(e) {
				let tiemr = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM')
				if (this.endParam.actualStartTime > tiemr) return uni.$u.toast('实际结束时间要大于实际开始时间！')
				this.endTimeShow = false
				this.endParam.actualEndTime = tiemr
			},
		}
	}
</script>

<style lang="scss" scoped>
	.detail {
		padding-bottom: 120rpx;

		.filterBox {
			padding: 0 32rpx;
			margin-top: 14rpx;

			.search {
				/deep/.u-search__content {
					background-color: #E9ECF7 !important;
				}

				/deep/.u-search__content__input {
					background-color: #E9ECF7 !important;
				}
			}
		}

		.app_list {
			background-color: #fff;
			margin: 14rpx 11rpx;
			border-radius: 11rpx;
			padding: 30rpx;
			font-size: 28rpx;
			position: relative;

			.icon-right {
				position: absolute;
				top: 50%;
				right: 27rpx;
				margin-top: -16rpx;
			}

			.tab_text {
				color: #346CF2;
				margin: 0 20rpx;
			}

			.tab_icon {
				margin-right: 16rpx;
			}

			.text-dh {
				font-size: 24rpx;
				color: #999999;
			}

			.text-rt {
				color: #346CF2;

				.text-rt-tag {
					margin-right: 4rpx;

					/deep/.u-tag--mini {
						height: 33rpx;
						line-height: 33rpx;
						padding: 0 4rpx;
					}
				}
			}

			.app_t_line {
				margin-top: 20rpx;
			}

			.dw_box {
				font-size: 24rpx;
			}

			.spot {
				width: 16rpx;
				height: 16rpx;
				background-color: #239EFC;
				border-radius: 50%;
				margin: 0 24rpx 0 8rpx;
			}

			.endspot {
				background-color: #404040;
			}
		}

		.btn_box {
			background-color: #fff;
			height: 80rpx;
			box-shadow: 0px -1px 43px 0px rgba(131, 131, 131, 0.15);

			.btnmini {
				width: 133rpx;
				height: 66rpx;

				/deep/ .u-button__text {
					font-size: 28rpx !important;
				}
			}

			.hui {
				background-color: #999999;
				border-color: #999999;
			}
		}

		.line-dotted {
			border-top: 1px dotted #999999;
			margin: 20rpx 0;
		}

		.res_jd {
			padding: 20rpx 0rpx;

			.u-steps-item--column {
				padding-bottom: 20px;
			}
		}

		.popup_by {
			margin: 20rpx 0;
			padding: 20rpx;
		}

		.popup_bg {

			.popupcbox {
				padding: 0 53rpx;
			}

			.select_input {
				height: 60rpx;
				border: 1px solid #CCCCCC;
				border-radius: 8rpx;
				padding-left: 20rpx;
				line-height: 60rpx;
				color: #999;
				margin: 20rpx 0;
			}

			.selectbox {
				padding: 0 53rpx;
				font-size: 28rpx;
			}

			.popup_btn_box {
				padding-bottom: 27rpx;
				margin-top: 30rpx;
			}

			.popup_tit {
				font-size: 36rpx;
				text-align: center;
				padding: 40rpx 10rpx 30rpx 10rpx;
			}

			.fonts {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}


			.popup_txt {
				font-size: 28rpx;
				text-align: center;
				margin-bottom: 40rpx;
			}

			/deep/.u-popup__content {
				width: 84%;
			}

			.popup_btn {
				width: 230rpx;
				height: 80rpx;

				/deep/ .u-button__text {
					font-size: 36rpx !important;
				}
			}

			.two {
				color: #666666 !important;
			}
		}

		.popupbox {
			.popupbox_top {
				font-size: 28rpx;
				height: 102rpx;
				padding: 0 32rpx;
				border-bottom: 1px solid #E9ECF7;
				justify-content: space-between;

				.define {
					color: #346CF2;
				}
			}

			.radio_type {
				padding: 24rpx 0;

				.u-radio-group {
					justify-content: space-around;
				}

				.u-radio {
					padding: 18rpx 28rpx;
					border: 1px solid #999999;
					border-radius: 7rpx;
				}

				.u-radio.active {
					border: 1px solid #346CF2;
				}
			}

			.list_one {
				margin: 0 32rpx;
			}

			.list_two {
				margin: 0 32rpx;
			}

			.inputBox {
				padding: 23rpx 0;
				border-top: 1px solid #E9ECF7;

				.s_input_box {
					width: 200rpx;
					margin-left: 10rpx;
					margin-right: 50rpx;
				}

				.s_input {
					padding: 0 9px !important;
				}

				.s_btn_box {
					.del {
						background-color: #999999;
						border-color: #999999;
						// height: 100rpx;
					}

					.yes {
						// height: 100rpx;
					}
				}
			}

			.inputBoxTwo {
				padding: 13rpx 0;
				border-top: 1px solid #E9ECF7;

				.s_input_box {
					width: 200rpx;
					margin-left: 10rpx;
					margin-right: 50rpx;
				}

				.s_input {
					padding: 0 9px !important;
				}

				.s_btn_box {
					.del {
						background-color: #999999;
						border-color: #999999;
						// height: 100rpx;
					}

					.yes {
						// height: 100rpx;
					}
				}
			}
		}

		.sinput {
			margin: 10rpx 0;
		}

		.sinputzl {
			margin: 5px 0;
		}

		.ui-list {
			padding: 0 30rpx;
			list-style: none;

			li {
				height: 80rpx;
				line-height: 80rpx;
				border-bottom: 1px solid #E9ECF7;

				.ui-list-info {
					display: flex;
					justify-content: space-between;
				}

				.ui-nowrap {
					font-weight: inherit;
					font-size: 14px;
					width: 70%;
				}

				.ui-nowrap-d {
					font-weight: inherit;
					font-size: 14px;
					width: 50%;
				}

				.ui-nowrap-l {
					font-weight: inherit;
					font-size: 13px;
					width: 76%;
				}

				.width100 {
					width: 100px;
				}

				.mileage_input {
					color: #346CF2;
					font-size: 14px;
					height: 80rpx;
				}
			}
		}

		.mileage_tit_s {
			font-size: 14px;
			color: #999999;
			margin-left: 12px;
			line-height: 40px;
		}

		.font_bold {
			font-weight: bold;
		}

		.startImg {
			width: 80px;
			height: 80px;
			background-color: #ccc;
			border-radius: 6px;
			margin-bottom: 4px;
		}

		.startEndBox {
			width: 74%;
			margin: 0 auto;
			margin-top: 12px;
		}

		.drmp_file_box {
			/deep/.u-upload__button {
				margin: 0;
			}

			/deep/.u-upload__wrap__preview {
				margin: 0;
			}
		}

		.dhbox {
			margin-top: 20rpx;
		}

		.detail_box {
			margin-top: 10rpx;

			/deep/.u-cell__body {
				padding: 14rpx 0;
			}

			/deep/.u-line {
				border-color: #ccc !important;
			}
		}

		.detail_tit {
			color: #262626;
			font-size: 28rpx;
		}

		.detail_val {
			color: #666666;
			font-size: 28rpx;

			span {
				color: #FF3131;
			}
		}

		.detail_span {
			color: #000 !important;
			margin-left: 20rpx;
		}

		.detail_label {
			color: #999;
			font-size: 24rpx;
		}

		.detail_val_b {
			color: #262626;
			font-size: 28rpx;
		}

		.columnr {
			flex-direction: column;
		}

		.widthr {
			width: 65vw !important;
			margin-right: 10rpx;
		}

		.hui {
			color: #ccc;
		}

	}

	.app_span {
		color: #ccc;
		margin-right: 10rpx;
	}

	.app_addrs {
		background: #ebebeb;
		padding: 10rpx;
		border-radius: 10rpx;
		margin-top: 10rpx;
	}

	.ma {
		margin: 10rpx 0;
	}

	.weight {
		font-weight: bold;
		display: flex;
		align-items: center;
	}

	.dian {
		display: block;
		width: 10rpx;
		height: 28rpx;
		background-color: #239EFC;
		border-radius: 10rpx;
		margin-right: 10rpx;
	}

	.black {
		color: #000 !important;
	}

	.feeDetai_item {
		background: #f8f8f8;
		padding: 0 20rpx;
	}
</style>