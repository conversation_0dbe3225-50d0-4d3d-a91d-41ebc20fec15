<template>
	<view class="result">
		<u-navbar title="提交成功" :autoBack="true" :placeholder="true"></u-navbar>
		<view class="result_top">
			<view class="res_img">
				<u--image :showLoading="true" src="https://zqcx.di-digo.com/app/image/adopt.png" width="75px"
					height="75px"></u--image>
			</view>
			<view class="result_txt">
				提交成功
			</view>
		</view>
		<view class="res_dh">
			申请单号：{{pageParams.applyCode}}
		</view>

		<view class="res_jd">
			<u-steps :current="pageParams.orderEventHis.length-1" direction="column">
				<u-steps-item v-for="(item,index) in pageParams.orderEventHis" :key="index"
					:title="item.operatorName+' '+item.evtDetContent"
					:desc="Number(item.operatorLongTime) | date('yy年mm月dd日 hh:MM:ss')">
				</u-steps-item>
			</u-steps>
		</view>


		<view class="footer_box">
			<u-button type="primary" color="#346CF2" text="返回列表" @click="gobackList()"></u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pageParams: {}
			}
		},
		onLoad(option) {
			this.pageParams = option;
			if (option.orderEventHis) {
				this.pageParams.orderEventHis = JSON.parse(this.pageParams.orderEventHis)
			}
			console.log(this.pageParams, 'this.pageParamsthis.pageParams')
		},
		methods: {
			gobackList() {
				let {
					regulationScene
				} = this.pageParams

				let t = regulationScene == 5 ? '2' : regulationScene == 1 ? '1' : regulationScene == 10 ? '3' : null
				if (this.pageParams.regulationScene) {
					uni.redirectTo({
						url: '/pages/wayPassenger/workBench/apply/apply?typer=' + t
					})
				} else {
					uni.switchTab({
						url: '/pages/wayPassenger/index/index'
					})
				}


			}
		}
	}
</script>

<style lang="scss" scoped>
	.result {
		.result_top {
			background: url("https://zqcx.di-digo.com/app/image/adopt_bg.png") no-repeat center center;
			background-size: cover;
			height: 333rpx;
			text-align: center;
			overflow: hidden;

		}

		.res_img {
			margin: 58rpx auto 30rpx auto;
			width: 150rpx;
		}

		.result_txt {
			font-size: 36rpx;
			color: #fff;
		}

		.res_dh {
			height: 104rpx;
			line-height: 104rpx;
			margin: 0 32rpx;
			font-size: 28rpx;
			border-bottom: 1px solid #E9ECF7;
		}

		.res_jd {
			padding: 40rpx 32rpx;

			.u-steps-item--column {
				padding-bottom: 20px;
			}
		}
	}
</style>