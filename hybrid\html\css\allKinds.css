/* 导航 */
.drawerUl {
	list-style: none;
	margin: 0;
	margin-top: 14rem;
	font-size: 14rem;
	position: fixed;
	bottom: 0;
	background: #fff;
	padding: 10rem 20rem;
	max-height: 50%;
	overflow-y: auto;
	z-index: 9999;
	width: calc(100% - 40rem);

}

.drawerUl li {
	background-color: #EFEFEF;
	padding: 8rem 16rem;
	border-radius: 6rem;
	line-height: 20rem;
	margin-bottom: 12rem;
}

.drawerUl li .gobtn {
	background-color: #fff;
	border-radius: 25rem;
	padding: 4rem 14rem;
}

.drawerTlite {
	font-weight: 600;
	line-height: 30rem;
	display: flex;
	justify-content: space-between;
}

.drawerClose {
	color: #9b9b9b;
}

/* 弹窗 */
#msg_c {
	position: fixed;
	top: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

#successMsg {
	background: #585858e3;
	box-shadow: 0rem 0rem 16rem 0rem rgba(148, 148, 148, 0.36);
	border-radius: 4rem;
	padding: 10rem;
	font-size: 12rem;
	color: #FFFFFF;
	z-index: 99;
	display: flex;
	justify-content: center;
	align-items: center;
	max-width: 50%;
	word-break: break-all;
}

/* 弹窗 加载中*/
#loading {
	background: #585858e3;
	box-shadow: 0rem 0rem 16rem 0rem rgba(148, 148, 148, 0.36);
	border-radius: 4rem;
	padding: 10rem;
	font-size: 12rem;
	color: #FFFFFF;
	z-index: 99;
	display: flex;
	justify-content: center;
	align-items: center;
	max-width: 50%;
	word-break: break-all;
}

/* 加载中 */
.loder {
	display: flex;
	justify-content: center;
}

.loder div {
	width: 16rem;
	height: 16rem;
	margin: 0 5rem;
	background: #fff;
	border-radius: 50%;
	animation: loder 0.6s infinite alternate;
}

.loder div:nth-child(2) {
	animation-delay: 0.2s
}

.loder div:nth-child(3) {
	animation-delay: 0.4s
}

@keyframes loder {
	to {
		opacity: 0.1;
		transform: translate3d(0, 16rem, 0);
	}
}

/* 返回按钮 */
.backbtn {
	position: fixed;
	background-color: #ffffff;
	top: 20rem;
	left: 16rem;
	width: 30rem;
	height: 30rem;
	z-index: 999;
	text-align: center;
	line-height: 40rem;
	font-size: 22rem;
	color: #666666;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 50%;
	box-shadow: 1px 1px 3px;
}

/* 按钮 */
.popup_btn_yes {
	width: 120rem;
	height: 30rem;
	font-size: 14rem;
	background-color: #346CF2;
	color: #fff;
	border-radius: 4rem;
}

.popup_btn_no {
	width: 120rem;
	height: 30rem;
	font-size: 14rem;
	background-color: #E9ECF7;
	color: #666666;
	border-radius: 4rem;
}

/* 输入框 */
.mileage_input {
	border: none;
	text-align: right;
	color: #346CF2;
}

.mileage_tit_s {
	font-size: 12rem;
	color: #999999;
}

/* 盖章 */
.seal {
	width: 160rem;
	height: 160rem;
	border: solid 6rem #B4B4B4;
	border-radius: 100%;
	background-color: rgba(255, 255, 255, 0.8);
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
}

.seal-son {
	width: 145rem;
	height: 145rem;
	border: solid 2rem #B4B4B4;
	border-radius: 100%;
	background-color: rgba(255, 255, 255, 0.8);
	position: relative;
}

/* 滚动框 */
.map_detail {
	overflow: hidden;
	margin-top: 7rem;
	background: rgba(255, 255, 255, .85);
}

.map_detail_ul {
	border-top: 1rem solid #E9ECF7;
}

.map_detail_ul span {
	font-size: 12rem;
	color: #999;
}

.map_detail_ul li {
	padding: 4rem 12rem;
	border-bottom: 1rem solid #E9ECF7;
}

.map_detail_ul li p {
	margin: 4rem 0;
}

.map_detail_num {
	padding: 6rem 12rem;
	font-size: 12rem;
}

.map_detail_num_t span {
	font-size: 18rem;
	font-weight: bold;
}

.map_detail_num_r {
	color: #999999;
}

.map_detail_num_r i {
	font-size: 20rem;
	margin-left: 4rem;
	width: 14rem;
	text-align: center;
}

.map_detail_scoll {
	max-height: 0;
	transition: all .3s linear 0s;
	overflow-y: auto;
}

.map_detail_scoll.active {
	max-height: 200rem;
	transition: all .3s linear 0s;
}